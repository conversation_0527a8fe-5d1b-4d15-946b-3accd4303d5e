client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "ChessKit-x86_64-apple-macosx15.0-debug.module": ["<ChessKit-x86_64-apple-macosx15.0-debug.module>"]
  "MacChessBase-x86_64-apple-macosx15.0-debug.exe": ["<MacChessBase-x86_64-apple-macosx15.0-debug.exe>"]
  "MacChessBase-x86_64-apple-macosx15.0-debug.module": ["<MacChessBase-x86_64-apple-macosx15.0-debug.module>"]
  "Mac<PERSON>hessBasePackageTests-x86_64-apple-macosx15.0-debug.module": ["<MacChessBasePackageTests-x86_64-apple-macosx15.0-debug.module>"]
  "MacChessBasePackageTests-x86_64-apple-macosx15.0-debug.test": ["<MacChessBasePackageTests-x86_64-apple-macosx15.0-debug.test>"]
  "MacChessBaseTests-x86_64-apple-macosx15.0-debug.module": ["<MacChessBaseTests-x86_64-apple-macosx15.0-debug.module>"]
  "PackageStructure": ["<PackageStructure>"]
  "main": ["<MacChessBase-x86_64-apple-macosx15.0-debug.exe>","<MacChessBase-x86_64-apple-macosx15.0-debug.module>"]
  "test": ["<MacChessBase-x86_64-apple-macosx15.0-debug.exe>","<MacChessBase-x86_64-apple-macosx15.0-debug.module>","<MacChessBasePackageTests-x86_64-apple-macosx15.0-debug.test>","<MacChessBaseTests-x86_64-apple-macosx15.0-debug.module>"]
default: "main"
nodes:
  "/Users/<USER>/Projects/MacChessBase/MacChessBase/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase":
    is-mutated: true
commands:
  "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Collection.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Deprecated.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Index.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/sources"

  "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase-entitlement.plist":
    tool: write-auxiliary-file
    inputs: ["<entitlement-plist>","<com.apple.security.get-task-allow>"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase-entitlement.plist"]
    description: "Write auxiliary file /Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase-entitlement.plist"

  "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessBoardView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessGameViewModel.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessKitExtensions.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/ContentView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/DraggedPieceView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineAnalysisView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineManager.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineSettingsView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSession.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSessionManager.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/InteractiveMoveNotationView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/Item.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/MacChessBaseApp.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/MoveNotationView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/OptimizedChessSquareView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/PGNMetadataView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/SoundManager.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/SplitView.swift"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/sources"

  "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Collection.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Deprecated.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Index.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView.swift.o"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.product/Objects.LinkFileList"

  "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.derived/runner.swift"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/sources"

  "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.derived/runner.swift":
    tool: test-entry-point-tool
    inputs: []
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.derived/runner.swift"]

  "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Collection.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Deprecated.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Index.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/runner.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/MacChessBaseTests.swift.o"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.product/Objects.LinkFileList"

  "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/MacChessBaseTests.swift"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/sources"

  "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"

  "<ChessKit-x86_64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Collection.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Deprecated.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Index.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule"]
    outputs: ["<ChessKit-x86_64-apple-macosx15.0-debug.module>"]

  "<MacChessBase-x86_64-apple-macosx15.0-debug.exe>":
    tool: phony
    inputs: ["<MacChessBase-x86_64-apple-macosx15.0-debug.exe-CodeSigning>"]
    outputs: ["<MacChessBase-x86_64-apple-macosx15.0-debug.exe>"]

  "<MacChessBase-x86_64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBase.swiftmodule"]
    outputs: ["<MacChessBase-x86_64-apple-macosx15.0-debug.module>"]

  "<MacChessBasePackageTests-x86_64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/runner.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBasePackageTests.swiftmodule"]
    outputs: ["<MacChessBasePackageTests-x86_64-apple-macosx15.0-debug.module>"]

  "<MacChessBasePackageTests-x86_64-apple-macosx15.0-debug.test>":
    tool: phony
    inputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.xctest/Contents/MacOS/MacChessBasePackageTests"]
    outputs: ["<MacChessBasePackageTests-x86_64-apple-macosx15.0-debug.test>"]

  "<MacChessBaseTests-x86_64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/MacChessBaseTests.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBaseTests.swiftmodule"]
    outputs: ["<MacChessBaseTests-x86_64-apple-macosx15.0-debug.module>"]

  "C.ChessKit-x86_64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Collection.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Deprecated.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Index.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift","/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/sources"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Collection.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Deprecated.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Index.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule"]
    description: "Compiling Swift Module 'ChessKit' (26 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","ChessKit","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule","-output-file-map","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/sources","-I","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules","-target","x86_64-apple-macosx10.15","-enable-batch-mode","-index-store-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j16","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/ChessKit-Swift.h","-color-diagnostics","-swift-version","6","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","chesskit_swift"]

  "C.MacChessBase-x86_64-apple-macosx15.0-debug.exe":
    tool: shell
    inputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Collection.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Deprecated.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Index.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase"]
    description: "Linking ./.build/x86_64-apple-macosx/debug/MacChessBase"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug","-o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase","-module-name","MacChessBase","-Xlinker","-no_warn_duplicate_libraries","-emit-executable","-Xlinker","-alias","-Xlinker","_MacChessBase_main","-Xlinker","_main","-Xlinker","-rpath","-Xlinker","@loader_path","@/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.product/Objects.LinkFileList","-target","x86_64-apple-macosx13.0","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBase.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.MacChessBase-x86_64-apple-macosx15.0-debug.exe-entitlements":
    tool: shell
    inputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase-entitlement.plist"]
    outputs: ["<MacChessBase-x86_64-apple-macosx15.0-debug.exe-CodeSigning>"]
    description: "Applying debug entitlements to ./.build/x86_64-apple-macosx/debug/MacChessBase"
    args: ["codesign","--force","--sign","-","--entitlements","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase-entitlement.plist","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase"]

  "C.MacChessBase-x86_64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessBoardView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessGameViewModel.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessKitExtensions.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/ContentView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/DraggedPieceView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineAnalysisView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineManager.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineSettingsView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSession.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSessionManager.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/InteractiveMoveNotationView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/Item.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/MacChessBaseApp.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/MoveNotationView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/OptimizedChessSquareView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/PGNMetadataView.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/SoundManager.swift","/Users/<USER>/Projects/MacChessBase/MacChessBase/SplitView.swift","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/sources"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBase.swiftmodule"]
    description: "Compiling Swift Module 'MacChessBase' (18 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","MacChessBase","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBase.swiftmodule","-output-file-map","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/output-file-map.json","-incremental","-c","@/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/sources","-I","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules","-target","x86_64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j16","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ModuleCache","-parseable-output","-Xfrontend","-entry-point-function-name","-Xfrontend","MacChessBase_main","-color-diagnostics","-swift-version","6","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","macchessbase"]

  "C.MacChessBasePackageTests-x86_64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.derived/runner.swift","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBaseTests.swiftmodule","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/sources"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/runner.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBasePackageTests.swiftmodule"]
    description: "Compiling Swift Module 'MacChessBasePackageTests' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","MacChessBasePackageTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBasePackageTests.swiftmodule","-output-file-map","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/sources","-I","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules","-target","x86_64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j16","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/MacChessBasePackageTests-Swift.h","-color-diagnostics","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","macchessbase"]

  "C.MacChessBasePackageTests-x86_64-apple-macosx15.0-debug.test":
    tool: shell
    inputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Collection.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Deprecated.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Index.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/runner.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/MacChessBaseTests.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.xctest/Contents/MacOS/MacChessBasePackageTests"]
    description: "Linking ./.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.xctest/Contents/MacOS/MacChessBasePackageTests"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug","-o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.xctest/Contents/MacOS/MacChessBasePackageTests","-module-name","MacChessBasePackageTests","-Xlinker","-no_warn_duplicate_libraries","-Xlinker","-bundle","-Xlinker","-rpath","-Xlinker","@loader_path/../../../","@/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.product/Objects.LinkFileList","-target","x86_64-apple-macosx13.0","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBase.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBasePackageTests.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBaseTests.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.MacChessBaseTests-x86_64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/MacChessBaseTests.swift","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/sources"]
    outputs: ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/MacChessBaseTests.swift.o","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBaseTests.swiftmodule"]
    description: "Compiling Swift Module 'MacChessBaseTests' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","MacChessBaseTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBaseTests.swiftmodule","-output-file-map","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/sources","-I","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules","-target","x86_64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j16","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-color-diagnostics","-swift-version","6","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","macchessbase"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/Projects/MacChessBase/MacChessBase/","/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/","/Users/<USER>/Projects/MacChessBase/Package.swift","/Users/<USER>/Projects/MacChessBase/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

