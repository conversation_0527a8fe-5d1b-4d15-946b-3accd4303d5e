{"": {"swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/master.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessBoardView.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessGameViewModel.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessKitExtensions.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/ContentView.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/DraggedPieceView.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineAnalysisView.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineManager.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineSettingsView.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSession.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSessionManager.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/InteractiveMoveNotationView.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Item.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/MacChessBaseApp.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/MoveNotationView.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/OptimizedChessSquareView.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/PGNMetadataView.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/SoundManager.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/SplitView.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView.swiftdeps"}}