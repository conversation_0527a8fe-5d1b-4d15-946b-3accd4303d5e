/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessBoardView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessGameViewModel.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessKitExtensions.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/ContentView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/DraggedPieceView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineAnalysisView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineManager.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineSettingsView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSession.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSessionManager.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/InteractiveMoveNotationView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Item.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/MacChessBaseApp.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/MoveNotationView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/OptimizedChessSquareView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/PGNMetadataView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/SoundManager.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/SplitView.swift
