{"": {"swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/master.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.derived/runner.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/runner.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/runner.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/runner~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/runner.swiftdeps"}}