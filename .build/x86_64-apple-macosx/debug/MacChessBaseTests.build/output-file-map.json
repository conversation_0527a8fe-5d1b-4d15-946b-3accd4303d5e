{"": {"swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/master.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/MacChessBaseTests.swift": {"dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/MacChessBaseTests.d", "object": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/MacChessBaseTests.swift.o", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/MacChessBaseTests~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/MacChessBaseTests.swiftdeps"}}