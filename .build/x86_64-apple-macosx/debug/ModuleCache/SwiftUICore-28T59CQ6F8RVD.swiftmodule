---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.2/SwiftUICore.swiftmodule/x86_64-apple-macos.swiftmodule'
dependencies:
  - mtime:           1733472521000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.2/SwiftUICore.swiftmodule/x86_64-apple-macos.swiftmodule'
    size:            3708836
  - mtime:           1731214587000000000
    path:            'usr/lib/swift/Swift.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            2034701
    sdk_relative:    true
  - mtime:           1731215502000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1731227095000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1731199229000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1731227987000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            52901
    sdk_relative:    true
  - mtime:           1731199540000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1731219474000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1731228215000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1731215914000000000
    path:            'usr/lib/swift/_errno.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            3749
    sdk_relative:    true
  - mtime:           1731215935000000000
    path:            'usr/lib/swift/_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            998
    sdk_relative:    true
  - mtime:           1731215945000000000
    path:            'usr/lib/swift/_signal.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1034
    sdk_relative:    true
  - mtime:           1731215950000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1035
    sdk_relative:    true
  - mtime:           1731215937000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1460
    sdk_relative:    true
  - mtime:           1731215958000000000
    path:            'usr/lib/swift/unistd.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            730
    sdk_relative:    true
  - mtime:           1731215915000000000
    path:            'usr/lib/swift/_math.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            24003
    sdk_relative:    true
  - mtime:           1731214847000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            5847
    sdk_relative:    true
  - mtime:           1731215979000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            19812
    sdk_relative:    true
  - mtime:           1731216731000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            248725
    sdk_relative:    true
  - mtime:           1731216802000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22702
    sdk_relative:    true
  - mtime:           1731217513000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            172351
    sdk_relative:    true
  - mtime:           1731217449000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            6479
    sdk_relative:    true
  - mtime:           1731217641000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            56615
    sdk_relative:    true
  - mtime:           1731217830000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22834
    sdk_relative:    true
  - mtime:           1731217818000000000
    path:            'usr/lib/swift/XPC.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            33190
    sdk_relative:    true
  - mtime:           1731218046000000000
    path:            'usr/lib/swift/IOKit.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            3532
    sdk_relative:    true
  - mtime:           1731216756000000000
    path:            'usr/lib/swift/Observation.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            3778
    sdk_relative:    true
  - mtime:           1731217484000000000
    path:            'usr/lib/swift/System.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            94401
    sdk_relative:    true
  - mtime:           1731218706000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1002637
    sdk_relative:    true
  - mtime:           1731221014000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            53279
    sdk_relative:    true
  - mtime:           1731220018000000000
    path:            'System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            21203
    sdk_relative:    true
  - mtime:           1731220908000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1731219848000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            20493
    sdk_relative:    true
  - mtime:           1731215502000000000
    path:            'usr/include/os.apinotes'
    size:            1658
    sdk_relative:    true
  - mtime:           1731217852000000000
    path:            'usr/lib/swift/os.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            107976
    sdk_relative:    true
  - mtime:           1731221703000000000
    path:            'System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            22342
    sdk_relative:    true
  - mtime:           1731220007000000000
    path:            'System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            10801
    sdk_relative:    true
  - mtime:           1731220908000000000
    path:            'System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes'
    size:            1662
    sdk_relative:    true
  - mtime:           1729835551000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            80245
    sdk_relative:    true
  - mtime:           1732076708000000000
    path:            'System/Library/Frameworks/ApplicationServices.framework/Headers/ApplicationServices.apinotes'
    size:            2012
    sdk_relative:    true
  - mtime:           1731228760000000000
    path:            'System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes'
    size:            7428
    sdk_relative:    true
  - mtime:           1731219309000000000
    path:            'usr/lib/swift/Metal.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            25055
    sdk_relative:    true
  - mtime:           1731220987000000000
    path:            'System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1462
    sdk_relative:    true
  - mtime:           1731220329000000000
    path:            'usr/lib/swift/QuartzCore.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1693
    sdk_relative:    true
  - mtime:           1731220765000000000
    path:            'System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            21477
    sdk_relative:    true
  - mtime:           1731217482000000000
    path:            'usr/lib/swift/simd.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            218640
    sdk_relative:    true
  - mtime:           1731559801000000000
    path:            'System/Library/Frameworks/SwiftUICore.framework/Modules/SwiftUICore.swiftmodule/x86_64-apple-macos.swiftinterface'
    size:            1009664
    sdk_relative:    true
version:         1
...
