{"builtTestProducts": [{"binaryPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.xctest/Contents/MacOS/MacChessBasePackageTests", "packagePath": "/Users/<USER>/Projects/MacChessBase", "productName": "MacChessBasePackageTests"}], "copyCommands": {}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.ChessKit-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/sources", "importPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Collection.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Deprecated.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Index.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/sources"}], "isLibrary": true, "moduleName": "ChessKit", "moduleOutputPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule", "objects": ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Collection.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Deprecated.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Index.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx10.15", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/ChessKit-Swift.h", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "chesskit_swift"], "outputFileMapPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Collection.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Deprecated.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Index.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Collection.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Deprecated.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Index.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift"], "tempsPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build", "wholeModuleOptimization": false}, "C.MacChessBase-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/sources", "importPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessBoardView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessGameViewModel.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessKitExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/ContentView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/DraggedPieceView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineAnalysisView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineSettingsView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSession.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSessionManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/InteractiveMoveNotationView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/Item.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/MacChessBaseApp.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/MoveNotationView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/OptimizedChessSquareView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/PGNMetadataView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/SoundManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/SplitView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/ChessKit.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/sources"}], "isLibrary": false, "moduleName": "MacChessBase", "moduleOutputPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBase.swiftmodule", "objects": ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager.swift.o", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "MacChessBase_main", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "macchessbase"], "outputFileMapPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBase.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessBoardView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessGameViewModel.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessKitExtensions.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/ContentView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/DraggedPieceView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineAnalysisView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineManager.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineSettingsView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSession.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSessionManager.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/InteractiveMoveNotationView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/Item.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/MacChessBaseApp.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/MoveNotationView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/OptimizedChessSquareView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/PGNMetadataView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/SoundManager.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/SplitView.swift"], "tempsPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build", "wholeModuleOptimization": false}, "C.MacChessBasePackageTests-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/sources", "importPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.derived/runner.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBaseTests.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/sources"}], "isLibrary": true, "moduleName": "MacChessBasePackageTests", "moduleOutputPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBasePackageTests.swiftmodule", "objects": ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/runner.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/MacChessBasePackageTests-Swift.h", "-color-diagnostics", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "macchessbase"], "outputFileMapPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/runner.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBasePackageTests.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.derived/runner.swift"], "tempsPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build", "wholeModuleOptimization": false}, "C.MacChessBaseTests-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/sources", "importPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/MacChessBaseTests.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/sources"}], "isLibrary": true, "moduleName": "MacChessBaseTests", "moduleOutputPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBaseTests.swiftmodule", "objects": ["/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/MacChessBaseTests.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "macchessbase"], "outputFileMapPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/MacChessBaseTests.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules/MacChessBaseTests.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/MacChessBaseTests.swift"], "tempsPath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"ChessKit": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "ChessKit", "-package-name", "chesskit_swift", "-incremental", "-c", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Collection.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Deprecated.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Index.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift", "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift", "-I", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx10.15", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/ChessKit-Swift.h", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "chesskit_swift", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "MacChessBase": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "MacChessBase", "-package-name", "macchessbase", "-incremental", "-c", "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessBoardView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessGameViewModel.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessKitExtensions.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/ContentView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/DraggedPieceView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineAnalysisView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineManager.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineSettingsView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSession.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSessionManager.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/InteractiveMoveNotationView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/Item.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/MacChessBaseApp.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/MoveNotationView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/OptimizedChessSquareView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/PGNMetadataView.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/SoundManager.swift", "/Users/<USER>/Projects/MacChessBase/MacChessBase/SplitView.swift", "-I", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "MacChessBase_main", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "macchessbase", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "MacChessBasePackageTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "MacChessBasePackageTests", "-package-name", "macchessbase", "-incremental", "-c", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.derived/runner.swift", "-I", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/MacChessBasePackageTests-Swift.h", "-color-diagnostics", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "macchessbase", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "MacChessBaseTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "MacChessBaseTests", "-package-name", "macchessbase", "-incremental", "-c", "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/MacChessBaseTests.swift", "-I", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx13.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j16", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "macchessbase", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"ChessKit": [], "MacChessBase": ["ChessKit"], "MacChessBasePackageTests": ["MacChessBaseTests", "MacChessBase", "ChessKit"], "MacChessBaseTests": ["MacChessBase", "ChessKit"]}, "testDiscoveryCommands": {}, "testEntryPointCommands": {"/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.derived/runner.swift": {"inputs": [], "outputs": [{"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.derived/runner.swift"}]}}, "writeCommands": {"/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Attacks.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Bitboard.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/PieceSet.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Bitboards/Square+BB.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Board.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Clock.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Configuration.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Game.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Move.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Collection.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Deprecated.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree+Index.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser+Regex.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/EngineLANParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/FENParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser+Regex.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/PGNParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser+Regex.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Parsers/SANParser.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Piece.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Position.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/Castling.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Special Moves/EnPassant.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Square.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/Utilities/Comparable+Bounded.swift"}], "outputFilePath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/sources"}, "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase-entitlement.plist": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<entitlement-plist>"}, {"kind": "virtual", "name": "<com.apple.security.get-task-allow>"}], "outputFilePath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase-entitlement.plist"}, "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessBoardView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessGameViewModel.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/ChessKitExtensions.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/ContentView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/DraggedPieceView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineAnalysisView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/EngineSettingsView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSession.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/GameSessionManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/InteractiveMoveNotationView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/Item.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/MacChessBaseApp.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/MoveNotationView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/OptimizedChessSquareView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/PGNMetadataView.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/SoundManager.swift"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBase/SplitView.swift"}], "outputFilePath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/sources"}, "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Collection.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Deprecated.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Index.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView.swift.o"}], "outputFilePath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.product/Objects.LinkFileList"}, "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.derived/runner.swift"}], "outputFilePath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/sources"}, "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Attacks.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Bitboard.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Board.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Castling.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Clock.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Comparable+Bounded.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Configuration.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EnPassant.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/EngineLANParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/FENParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Game.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Move.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Collection.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Deprecated.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree+Index.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/MoveTree.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PGNParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Piece.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/PieceSet.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Position.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser+Regex.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/SANParser.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square+BB.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/ChessKit.build/Square.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessBoardView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessGameViewModel.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ChessKitExtensions.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/ContentView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/DraggedPieceView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineAnalysisView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/EngineSettingsView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSession.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/GameSessionManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/InteractiveMoveNotationView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/Item.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MacChessBaseApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/MoveNotationView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/OptimizedChessSquareView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/PGNMetadataView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SoundManager.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBase.build/SplitView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.build/runner.swift.o"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/MacChessBaseTests.swift.o"}], "outputFilePath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBasePackageTests.product/Objects.LinkFileList"}, "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/MacChessBaseTests.swift"}], "outputFilePath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/MacChessBaseTests.build/sources"}, "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/Projects/MacChessBase/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}}}