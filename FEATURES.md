# MacChessBase - Current Features (Release 1.4.0)

A native macOS professional chess analysis suite built with Swift and SwiftUI, featuring multi-module architecture with card-based navigation and comprehensive chess functionality powered by the enhanced ChessKit-Swift library.

## 🏠 Main Interface & Navigation (Release 1.3.0)

### Card-Based Navigation System
- **Main Navigation Interface**: Professional 2x2 card layout replacing single-view interface
- **Four Module Cards**:
  - **Chess Analysis** (Free) - Complete chess analysis functionality
  - **Online Database** (Pro) - Database management and search capabilities  
  - **OCR Recognition** (Pro) - Board/notation image-to-text conversion
  - **Tournament Management** (Pro) - Professional tournament organization software
- **Dynamic Card Layout**: Cards automatically resize based on main window dimensions (300x200 to 500x350 pixels)
- **Responsive Design**: Maintains optimal proportions across different screen sizes

### Multi-Window Architecture
- **Independent Module Windows**: Each card opens dedicated window instead of view switching
- **Window Coordination**: Main window closing automatically closes all child windows
- **Memory-Safe Implementation**: Proper NSHostingController lifecycle with cleanup observers
- **Screen-Optimized Sizing**: 
  - Main window: 98% of screen visible area
  - Child windows: 90% of screen visible area
  - Chess interface: Minimum 1200x600 for optimal board display
- **Dynamic Window Sizing**: Content-driven sizing with appropriate minimum dimensions per module
- **Window Management**: Centralized WindowManager singleton for lifecycle coordination

### Subscription System Integration
- **Three-Tier Access Control**: Free, Pro, and Expert subscription levels
- **Feature Gating**: Pro features display upgrade alerts for free users
- **Development Mode**: Temporary subscription upgrade capability for testing
- **Native Integration**: SwiftUI alerts for subscription upgrade prompts
- **Visual Indicators**: Pro badges on restricted feature cards

## 🎯 Chess Analysis Module (ChessView)

### Interactive Chess Board
- **Visual Chess Board**: Fully interactive 8x8 chess board with piece drag-and-drop
- **Board Orientation**: Flip board functionality with context menu support
- **Piece Movement**: 
  - Click-to-move and drag-and-drop piece movement
  - Reverse drag support (drag from target square to source)
  - Legal move highlighting and validation
  - Last move highlighting
- **Visual Feedback**: Selected piece highlighting, possible moves indication
- **Pawn Promotion**: Interactive promotion dialog with keyboard shortcuts (Q, R, B, N)

### Chess Board Screenshot System (Release 1.3.0)
- **Dual Export Modes**: Professional screenshot functionality with two distinct modes
  - **Clean Board Mode**: Pure chess position export without any UI decorations
    - No selection highlighting or move hints
    - No last move indicators or interaction overlays
    - No visual annotations (arrows, square highlights)
    - Clean, publication-ready chess diagrams
  - **With Highlights Mode**: Complete current board state with all visual elements
    - All current selection and move highlighting
    - Visual annotations from position comments
    - Last move indicators and possible move hints
    - Exact representation of current user interface
- **Export Options**: Flexible sharing and saving capabilities
  - **Copy to Clipboard**: Instant screenshot copying for immediate use in other applications
  - **Save to File**: Professional file export with NSSavePanel integration
  - **High Resolution Output**: 800x800 base resolution with 2x scaling for crisp images
  - **Format Support**: PNG and JPEG export options with automatic format detection
- **Intelligent File Naming**: Automatic timestamp and mode identification (ChessBoard_clean_timestamp.png)
- **Context Menu Integration**: Right-click access through organized submenus
  - Copy Board Screenshot → Clean Board / With Highlights
  - Save Board Screenshot → Clean Board / With Highlights
- **Professional UI Icons**: Distinct SF Symbols for each mode (camera.fill, camera.badge.ellipsis)
- **Workflow Optimization**: Seamless integration with chess analysis workflow

### Position Editor (Release 1.1.0+)
- **Complete Position Editing**: Full-featured chess position editor with drag-and-drop piece placement
- **Visual Consistency**: Chess board colors match main ChessBoardView (brown.opacity(0.3/0.7)) for unified appearance
- **Screenshot Functionality (Release 1.3.0+)**: Professional diagram generation capabilities
  - **Copy Screenshot**: Capture position to clipboard with camera icon button
  - **Save Screenshot**: Export position to file with NSSavePanel integration
  - **Clean Output**: Optimized for professional chess diagram generation
    - Square corners (no rounded edges) for formal presentation
    - No coordinate labels (1-8, a-h) for clean appearance
    - High-resolution output (2x scale) for crisp printing and sharing
  - **Horizontal Button Layout**: Space-efficient side-by-side screenshot controls below delete tool
  - **Multi-line Text Support**: Button labels automatically wrap for optimal space usage
  - **Professional File Naming**: Automatic timestamp-based filenames (PositionEditor_timestamp.png)
  - **Format Support**: PNG and JPEG export options with automatic format detection
- **FEN Integration**: 
  - Real-time FEN string editing with live board updates
  - Comprehensive FEN validation with detailed error checking
  - Copy/paste FEN functionality with clipboard integration
  - Real-time validation status indicator ("FEN (Invalid)" in red when position is invalid)
- **Position Validation**: 
  - Complete chess rule validation (king count, piece placement, castling rights)
  - En passant validation with proper pawn position checking
  - Check logic validation to prevent illegal positions
  - User-friendly error messages for invalid positions
  - Validation only on position confirmation (allows temporary invalid states during editing)
- **Editing Tools**:
  - Piece palette with all chess pieces (white and black)
  - Delete tool for removing pieces
  - Castling rights toggles for both sides
  - Side-to-move selection
  - Initial move number control with stepper interface
  - Board flip functionality
- **Position Management**:
  - Reset to standard starting position
  - Clear board for custom positions
  - Load position from FEN string
  - Import error handling with "Invalid PGN or FEN" alerts
- **Move Number Control**:
  - Editable initial move number (corresponds to FEN fullmove number)
  - Increment/decrement buttons for easy adjustment
  - Direct text field editing with validation
  - Automatic FEN synchronization

### Game Logic & Validation
- **Complete Chess Rules**: Full implementation via enhanced ChessKit-Swift
- **Move Validation**: Real-time legal move checking
- **Game States**: Checkmate, stalemate, draw detection (50-move rule, insufficient material)
- **Turn Management**: Proper side-to-move tracking and enforcement

## 📝 Game Notation & Analysis

### PGN (Portable Game Notation) Support
- **Full PGN Import/Export**: Complete PGN file reading and writing
- **Multi-Game PGN Import (Release 1.4.0)**: Intelligent detection and handling of multi-game PGN files
  - **Automatic Detection**: Distinguishes between single and multiple games in PGN input
  - **Smart Routing**: Single games use undo/redo, multiple games create separate sessions
  - **Universal Support**: Both file and clipboard imports support multi-game detection
  - **Intelligent Naming**: Automatic session naming based on PGN metadata or game indices
- **FEN Support**: Forsyth-Edwards Notation for position setup
- **Clipboard Integration**: Import/export games via clipboard with multi-game support
- **Interactive Notation View**: Click moves to navigate, with move highlighting

### Game Variations & Analysis
- **Complex Variation Support**:
  - Create, edit, and navigate multiple variations
  - Promote variations to main line
  - Delete variations and moves
  - Delete moves before a target position ("Delete Before Move")
  - Collapse/expand variations for better readability
- **Move Tree Management**: Advanced tree structure for handling complex game analysis
- **Move Annotations**:
  - Text comments on moves and positions
  - Move assessments (?, ??, !, !!, !?, ?!)
  - Position evaluations

### Visual Annotations
- **Square Highlighting**: Multi-color square highlighting system
- **Arrow Annotations**: Draw colored arrows on the board
- **Annotation Colors**: Green, red, blue annotation support
- **Annotation Editing**: Right-click context menus for quick annotation editing
- **Right-Click Gesture Recognition**: Native macOS right-click gesture support for board interaction
  - Custom NSViewRepresentable implementation for right-click detection
  - Context menu integration for position editor and board interaction
  - Configurable right mouse button (0x2) mask for precise gesture recognition

## 🔧 Game Management

### Multi-Game Session Support
- **Game Sessions**: Multiple concurrent game sessions in sidebar
- **Session Management**: Create, rename, remove game sessions
- **Active Session Tracking**: Visual indication of current active game
- **Auto-naming**: Smart game naming based on player names and metadata
- **Multi-Game Import (Release 1.4.0)**: Automatic session creation for multi-game PGN imports
  - **Bulk Import**: Import multiple games from single PGN file or clipboard
  - **Session Separation**: Each game creates its own session with unique naming
  - **Metadata Extraction**: Game names derived from PGN tags (Event, White/Black players)
  - **Performance Optimization**: Multi-game imports skip undo/redo for better performance

### File Operations
- **File Formats**: Support for .pgn and .fen files
- **Save Operations**: 
  - Save current game to file
  - Save As with metadata editing
  - Auto-suggest filenames based on game content
- **File Loading**: Open games from files with automatic format detection
- **Recent Files**: Quick access to recently opened games

### Native Undo/Redo System (Release 1.2.0 Enhanced)
- **Native macOS Integration**: Full macOS UndoManager integration with system-level undo/redo
- **Complete Operation Support**: Full undo/redo for all chess operations
  - Move addition, deletion, and editing
  - Variation promotion and management
  - Position overwriting and "delete before move"
  - Position editor changes and custom position setup
  - Clipboard import operations (PGN/FEN)
  - Game state and metadata changes
- **Advanced State Management**: 7 specialized undo state types with memory optimization
- **Memory Efficiency (Release 1.2.0)**: Optimized UndoState structures with reduced memory footprint
  - Eliminated redundant index and path fields that can be derived
  - Non-optional path fields for enhanced reliability
- **Encapsulated API (Release 1.2.0)**: Clean index resolution methods
  - `resolvedParentIndex(in:)` for robust parent index resolution
  - `newMoveIndexToRemove(in:)` for structural move identification
  - Eliminated repetitive code across modules
- **Position Restoration**: Automatic position dictionary synchronization
- **Reliable Indexing**: Integer-based MoveTree indexing system compatible with undo/redo operations
- **Performance Optimized**: Efficient state capture and restoration with structural logic

### Game Metadata Management (Release 1.1.0+ Optimized)
- **Modern Architecture**: Direct binding system with single source of truth (no data duplication)
- **Real-time Updates**: Instant metadata changes without callback delays
- **Comprehensive PGN Tags**:
  - Player information (names, ratings, titles, FIDE IDs)
  - Tournament data (event, site, date, round)
  - Game details (result, time control, ECO code, opening)
  - Technical tags (FEN, SetUp for custom positions)
- **Streamlined Editor**: Optimized metadata dialog with KeyPath-based binding
- **Auto-Detection**: Automatic result detection when game ends
- **Performance Optimized**: Eliminated data copying and synchronization overhead

## 🤖 Chess Engine Integration

### Stockfish Engine Support
- **Engine Management**: Start, stop, pause, and resume engine analysis
- **Real-time Analysis**: Continuous position evaluation
- **Multi-PV Analysis**: Multiple best move candidates
- **Engine Settings**: Configurable depth, threads, hash size, and analysis lines
- **Analysis Display**: 
  - Current position evaluation
  - Principal variations with proper chess notation
  - Analysis depth and nodes information

### Analysis Features
- **Position Analysis**: Automatic analysis of current position
- **Engine Information**: Display engine name and version
- **Analysis Controls**: Start, pause, resume, and clear analysis
- **Performance Metrics**: Nodes per second, analysis time, hash usage

## 🎮 User Interface & Experience

### Native macOS Integration
- **Menu Bar Integration**: Native macOS menu commands with keyboard shortcuts
- **Keyboard Shortcuts**:
  - Game navigation: ← → (previous/next move), ⌘← ⌘→ (start/end)
  - File operations: ⌘N (new), ⌘O (open), ⌘S (save), ⇧⌘S (save as)
  - Import/Export: ⇧⌘V (import clipboard), ⇧⌘C (copy PGN)
  - Native Undo/Redo: ⌘Z (undo), ⇧⌘Z (redo) - integrated with macOS system
  - Annotations: ⌥1/G (green), ⌥2/R (red), ⌥3/B (blue)
- **Responsive Layout**: Adaptive UI with split views and resizable panels
- **Dark Mode Support**: Automatic system appearance adaptation

### Split View Interface
- **Three-Panel Layout**:
  - Left: Interactive chess board with player clocks
  - Top Right: Game notation with variation management
  - Bottom Right: Engine analysis panel
- **Resizable Panels**: Customizable panel sizes to suit user preferences
- **Collapsible Sections**: Expandable/collapsible variation display

### Audio Features
- **Move Sounds**: Audio feedback for moves and captures
- **Sound Management**: Separate sound effects for different move types
- **Volume Control**: Adjustable sound volumes

## 🕐 Game Clock Features
- **Visual Clocks**: Display for both players with dynamic positioning
- **Clock Orientation**: Automatic clock positioning based on board flip state
- **Player Information Display**: Names and ratings shown on clocks

## 🎨 Visual Customization
- **Board Themes**: Clean, professional chess board appearance
- **Piece Graphics**: High-quality SVG piece graphics for all piece types
- **Color Coding**: 
  - Move notation: Different colors for main line vs variations
  - Annotations: Multi-color highlight and arrow system
  - Engine evaluation: Color-coded evaluation display

## 💾 Data Management

### Import/Export Capabilities
- **Multiple Formats**: PGN and FEN file support
- **Clipboard Operations**: Seamless clipboard integration
- **Batch Processing**: Support for games with multiple variations
- **Error Handling**: Robust error handling for invalid files/data

### Game Storage
- **SwiftData Integration**: Modern data persistence
- **File Management**: Intelligent file naming and organization
- **Auto-Save**: Prevention of data loss with proper save workflows

## 🔍 Navigation & Search

### Game Navigation
- **Move-by-Move**: Navigate through game moves with full history
- **Variation Navigation**: Jump between main line and variations
- **Position Seeking**: Go to specific moves or positions
- **Navigation Controls**: Button and keyboard-based navigation

### Interactive Features
- **Click Navigation**: Click on moves in notation to jump to position
- **Context Menus**: Right-click for move editing and analysis options
- **Variation Selection**: Visual variation picker when multiple options exist
- **Move Editing**: Comprehensive move editing with promote/delete options
- **Position Restructuring**: "Delete Before Move" functionality to create new starting positions from any point in the game

## 🏗️ Technical Architecture

### MVVM Architecture (Release 1.2.0 Enhanced)
- **Native UndoManager Integration**: System-level undo/redo with dependency injection and weak references
- **Modular UI Structure**: Separated ContentView (entry point) from ChessView (chess functionality) for future expansion
- **GameSession**: Core game state management (refactored from ChessGameViewModel)
- **ChessGameViewModel**: UI-focused view model with clean separation
- **GameSessionManager**: Multi-game session coordination
- **Modular Services**: Separate managers for engine, sound, and file operations
- **Clean Architecture**: Clear separation between UI logic and game state management
- **Future-Ready**: Foundation for multi-view application (settings, databases, tournaments)

### Performance Optimizations (Release 1.1.0+)
- **Caching System**: Intelligent move notation caching
- **Optimized Rendering**: Hardware-accelerated board drawing
- **Throttled Updates**: Performance-optimized UI updates
- **Modern Binding Architecture**: KeyPath-based direct binding eliminates data copying
- **Single Source of Truth**: Eliminated metadata duplication and synchronization overhead

### Enhanced ChessKit Integration (Release 1.2.0)
- **Custom ChessKit Build**: Modified for advanced editing capabilities with memory optimization
- **Integer-Based MoveTree**: Reliable sequential indexing system (0, 1, 2, ...) with undo/redo compatibility
- **Native Undo/Redo Infrastructure**: macOS UndoManager integration with 7 specialized undo state types and memory efficiency improvements
- **Encapsulated API**: Clean index resolution methods eliminating code duplication
- **Position Comments**: Rich commenting system with visual annotations
- **Test Suite**: 183 comprehensive tests ensuring system reliability and optimization correctness
- **Bug Fixes**: Resolved consecutive redo operations and variation navigation issues

---

## 🚀 Current Status (Release 1.3.0)

MacChessBase is a fully functional chess analysis application that provides professional-level features for chess study and analysis. The application successfully implements all planned basic and advanced features from the original README, plus native macOS undo/redo system integration and modular architecture:

## 🔗 Additional Modules (Pro Features)

### Online Database Module
- **Coming Soon**: Professional database management interface
- **Multi-Game Collections**: Batch PGN parsing and organization
- **Advanced Search**: Position-based and metadata-based game searches
- **Database Optimization**: Custom storage format for large game collections
- **Import/Export Tools**: Support for various chess database formats

### OCR Recognition Module
- **Board Recognition**: Convert chess board images to FEN notation
- **Notation Recognition**: Convert chess notation images to PGN format  
- **Dual Interface**: Segmented picker for Board vs Notation recognition modes
- **Visual Workflow**: Step-by-step image-to-text conversion interface
- **Coming Soon**: Full computer vision integration with Core ML/Vision framework

### Tournament Management Module
- **Professional Tournament Software**: Inspired by SP98 tournament management
- **Player Management**: Registration, ratings, profiles, and history tracking
- **Pairing Systems**: Swiss System, Round Robin, Knockout, and custom formats
- **Live Scoring**: Real-time results entry with standings and statistics
- **Feature Categories**: Visual feature cards for major functionality groups
- **Coming Soon**: Complete tournament organization system

## 🏆 Implementation Status (Release 1.3.0)

✅ **Multi-Module Architecture** - Card-based navigation with four distinct modules  
✅ **Multi-Window System** - Independent windows with coordinated lifecycle management  
✅ **Subscription Integration** - Three-tier feature access with upgrade prompts  
✅ **Dynamic UI Responsiveness** - Adaptive layout scaling based on window dimensions  
✅ **Complete Chess Analysis** - Full ChessView functionality with all existing features  
✅ **Memory-Safe Windows** - NSHostingController pattern with proper cleanup  
✅ **Professional Interface** - Modern card design with hover effects and Pro badges  
✅ **Screen Optimization** - Intelligent window sizing for main and child windows  
✅ **Native macOS Integration** - UndoManager, keyboard shortcuts, and menu bar integration  
✅ **Robust Architecture** - WindowManager singleton with NotificationCenter coordination  
✅ **Modern Compatibility** - macOS 14.0+ API compliance and SwiftUI best practices  

The application has evolved from a single-view chess analysis tool into a comprehensive professional chess suite, providing a foundation for advanced database management, OCR capabilities, and tournament organization while maintaining all existing chess analysis functionality in a modern multi-window architecture.