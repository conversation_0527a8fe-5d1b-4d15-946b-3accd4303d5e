//
//  MacChessBaseApp.swift
//  MacChessBase
//
//  Created by Kai on 2025/5/31.
//

import SwiftUI
import SwiftData
import AppKit

@main
struct MacChessBaseApp: App {
    @StateObject private var sessionManager = GameSessionManager()

    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            Item.self,
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .frame(minWidth: 900, minHeight: 700)
                .onAppear {
                    // Set window to fill the entire available screen area (excluding dock and menu bar)
                    if let window = NSApplication.shared.windows.first {
                        // Get the visible frame (excludes dock and menu bar)
                        let visibleFrame = NSScreen.main?.visibleFrame ?? CGRect(x: 0, y: 0, width: 1920, height: 1080)
                        
                        // Set window to fill the entire visible area but slightly smaller for better UX
                        let windowFrame = CGRect(
                            x: visibleFrame.origin.x + 20,
                            y: visibleFrame.origin.y + 20,
                            width: visibleFrame.width - 40,
                            height: visibleFrame.height - 40
                        )
                        window.setFrame(windowFrame, display: true)
                        
                        // Register main window with WindowManager
                        WindowManager.shared.setMainWindow(window)
                    }
                }
                .environmentObject(sessionManager)
                
        }
        .modelContainer(sharedModelContainer)
        .commands {
            ChessMenuCommands()
        }
        .windowResizability(.contentSize)
    }
}

// MARK: - Chess Menu Commands
struct ChessMenuCommands: Commands {
    var body: some Commands {
        CommandMenu("Chess") {
            Button("New Game") {
                NotificationCenter.default.post(name: .newGame, object: nil)
            }
            .keyboardShortcut("n", modifiers: [.command])
            
            Button("Set Up Position") {
                NotificationCenter.default.post(name: .setUpPosition, object: nil)
            }
            .keyboardShortcut("p", modifiers: [.command])

            Divider()
            
            Button("Toggle Full Screen") {
                if let window = NSApplication.shared.keyWindow {
                    window.toggleFullScreen(nil)
                }
            }
            .keyboardShortcut("f", modifiers: [.command, .control])
            
            Divider()
            
            Button("Open File...") {
                NotificationCenter.default.post(name: .openFile, object: nil)
            }
            .keyboardShortcut("o", modifiers: [.command])
            
            Button("Import from Clipboard") {
                NotificationCenter.default.post(name: .importFromClipboard, object: nil)
            }
            .keyboardShortcut("v", modifiers: [.command, .shift])
            
            Button("Copy PGN") {
                NotificationCenter.default.post(name: .copyPGN, object: nil)
            }
            .keyboardShortcut("c", modifiers: [.command, .shift])
            
            Divider()
            
            Button("EditMetadata") {
                NotificationCenter.default.post(name: .editMetadata, object: nil)
            }
            .keyboardShortcut("e", modifiers: [.command])
            
            Button("Save") {
                NotificationCenter.default.post(name: .save, object: nil)
            }
            .keyboardShortcut("s", modifiers: [.command])
            
            Button("Save As...") {
                NotificationCenter.default.post(name: .saveAs, object: nil)
            }
            .keyboardShortcut("s", modifiers: [.command, .shift])
        }
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let newGame = Notification.Name("newGame")
    static let setUpPosition = Notification.Name("setUpPosition")
    
    static let openFile = Notification.Name("openFile")
    static let importFromClipboard = Notification.Name("importFromClipboard")
    static let editMetadata = Notification.Name("editMetadata")
    static let save = Notification.Name("save")
    static let saveAs = Notification.Name("saveAs")
    static let copyPGN = Notification.Name("copyPGN")
}
