//
//  FIDEPlayerData.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/8/8.
//

import Foundation

/// Cached FIDE player information to avoid repeated network calls
struct FIDEPlayerData: Codable, Equatable {
    let fideId: String
    let federation: String?
    let photoData: Data?
    let photoBase64: String?
    let fetchDate: Date
    
    /// Checks if the cached data is still valid (within 24 hours)
    var isValid: Bool {
        let twentyFourHoursAgo = Date().addingTimeInterval(-24 * 60 * 60)
        return fetchDate > twentyFourHoursAgo
    }
    
    /// Checks if the cached data is expired (older than 24 hours)
    var isExpired: Bool {
        return !isValid
    }
}
