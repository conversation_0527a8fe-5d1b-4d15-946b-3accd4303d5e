//
//  ChessMoveExecutor.swift
//  MacChessBase
//
//  Created by AI on 2025/8/9.
//

import SwiftUI
import ChessKit
import AVFoundation

/// <PERSON>les chess move execution logic, decoupled from UI concerns
@MainActor
final class ChessMoveExecutor: ObservableObject {
    
    // MARK: - Dependencies
    private let soundManager: SoundManager
    private let engineManager: EngineManager
    
    // MARK: - Move execution callbacks
    var onMoveCompleted: ((Move) -> Void)?
    var onPromotionNeeded: ((Square, Square) -> Void)?  // Changed to pass squares instead of move
    var onVariationCreationNeeded: ((Move, MoveTree.MoveIndex, MoveTree.MoveIndex?) -> Void)?
    var onGameStatusChanged: ((Move) -> Void)?
    
    // MARK: - Initialization
    init(soundManager: SoundManager? = nil, 
         engineManager: EngineManager? = nil) {
        self.soundManager = soundManager ?? SoundManager.shared
        self.engineManager = engineManager ?? EngineManager.shared
    }
    
    // MARK: - Public Move Execution Interface
    
    /// Attempts to execute a move from one square to another
    func attemptMove(from startSquare: Square, to endSquare: Square, in session: GameSession) -> MoveExecutionResult {
        // If clicking the same square, return deselection signal
        if startSquare == endSquare {
            return .shouldDeselect
        }
        
        // If clicking another piece of the same color, return reselection signal
        if let piece = session.board.position.piece(at: endSquare),
           piece.color == session.board.position.sideToMove {
            return .shouldReselect(endSquare)
        }
        
        // Check if the move is legal
        guard session.board.canMove(pieceAt: startSquare, to: endSquare) else {
            return .invalidMove
        }
        
        // Check if this is a pawn promotion - handle it separately
        if isPawnPromotion(from: startSquare, to: endSquare, in: session) {
            // Play sound immediately for promotion move
            soundManager.playGenericMoveSound() // Play generic move sound
            
            // Trigger promotion dialog without creating the move yet
            onPromotionNeeded?(startSquare, endSquare)
            return .promotionNeeded
        }
        
        // Create a temporary move to check for variations
        var tempBoard = session.board
        guard let tempMove = tempBoard.move(pieceAt: startSquare, to: endSquare) else {
            return .invalidMove
        }
        
        // Check if we need to show variation creation dialog
        if shouldShowVariationCreationDialog(for: tempMove, in: session) {
            let existingNextMoveIndex = getExistingNextMoveIndex(for: tempMove, in: session)
            onVariationCreationNeeded?(tempMove, session.currentMoveIndex, existingNextMoveIndex)
            return .variationDialogNeeded
        }
        
        // Execute the move directly
        return executeMove(from: startSquare, to: endSquare, in: session)
    }
    
    /// Executes a move with the specified variation creation option
    func executeMove(_ move: Move, option: VariationCreationOption, from fromIndex: MoveTree.MoveIndex, in session: GameSession) -> Bool {
        guard let metaMove = move.metaMove else {
            return false
        }
        
        var success = false
        var resultMove: Move? = nil
        
        switch option {
        case .newVariation:
            success = session.makeMove(from: metaMove.start, to: metaMove.end)
            if success {
                resultMove = session.game.moves.getNodeMove(index: session.currentMoveIndex)
            }
            
        case .newMainLine:
            success = session.makeMove(from: metaMove.start, to: metaMove.end)
            if success {
                resultMove = session.game.moves.getNodeMove(index: session.currentMoveIndex)
                _ = session.promoteVariation(at: session.currentMoveIndex)
            }
            
        case .overwrite:
            let newIndex = session.overwriteMove(move, from: fromIndex)
            session.goToMove(at: newIndex)
            success = true
            resultMove = session.game.moves.getNodeMove(index: session.currentMoveIndex)
        }
        
        if success, let executedMove = resultMove {
            handleMoveResult(executedMove, in: session)
            return true
        }
        
        return false
    }
    
    /// Completes a pawn promotion by creating the move with the promoted piece
    func completePromotion(from startSquare: Square, to endSquare: Square, promoteTo pieceKind: Piece.Kind, in session: GameSession) -> Move? {
        // Create a temporary move to check for variations
        var tempBoard = session.board
        guard let tempMove = tempBoard.moveWithPromotion(pieceAt: startSquare, to: endSquare, promotionPiece: pieceKind) else {
            return nil
        }

        // Check if this move would create a variation
        if let existingMoveIndex = session.game.moves.hasNextMove(containing: tempMove, for: session.currentMoveIndex) {
            // Move already exists, handle as variation
            onVariationCreationNeeded?(tempMove, session.currentMoveIndex, existingMoveIndex)
            return nil
        }

        // Make the move with promotion directly
        guard session.makeMoveWithPromotion(from: startSquare, to: endSquare, promotionPiece: pieceKind) else {
            return nil
        }

        // Get the created move
        guard let createdMove = session.game.moves.getNodeMove(index: session.currentMoveIndex) else {
            return nil
        }

        // Handle the completed move (skip sound since it was already played at promotion detection)
        handleMoveResult(createdMove, in: session, skipSound: true)

        return createdMove
    }
    
    // MARK: - Private Implementation
    
    /// Checks if a move would be a pawn promotion
    private func isPawnPromotion(from startSquare: Square, to endSquare: Square, in session: GameSession) -> Bool {
        guard let piece = session.board.position.piece(at: startSquare) else { return false }
        guard piece.kind == .pawn else { return false }
        
        // Check if pawn is moving to promotion rank
        return (piece.color == .white && endSquare.rank.value == 8) ||
               (piece.color == .black && endSquare.rank.value == 1)
    }
    
    /// Executes a move from coordinates
    private func executeMove(from startSquare: Square, to endSquare: Square, in session: GameSession) -> MoveExecutionResult {
        guard session.makeMove(from: startSquare, to: endSquare) else {
            return .invalidMove
        }
        
        if let actualMove = session.game.moves.getNodeMove(index: session.currentMoveIndex) {
            handleMoveResult(actualMove, in: session)
            return .success(actualMove)
        }
        
        return .invalidMove
    }
    
    /// Handles the result of a successful move execution
    private func handleMoveResult(_ move: Move, in session: GameSession, skipSound: Bool = false) {
        // Play move sound (unless skipped, e.g., for promotion where sound was already played)
        if !skipSound {
            soundManager.playMoveSound(for: move)
        }
        
        // Notify about completed move
        onMoveCompleted?(move)
        
        // Check game status and notify if changed
        onGameStatusChanged?(move)
        
        // Auto-analyze new position if engine is running
        if engineManager.state == .analyzing {
            Task.detached(priority: .background) { [weak self] in
                await self?.engineManager.analyzePosition(session.board.position)
            }
        }
    }
    
    /// Checks if a variation creation dialog should be shown
    private func shouldShowVariationCreationDialog(for move: Move, in session: GameSession) -> Bool {
        // Check if there's already a next move that would create a variation
        if session.game.moves.hasNextMove(containing: move, for: session.currentMoveIndex) != nil {
            return false
        }
        
        // Special handling for the first move
        if session.currentMoveIndex == session.game.startingIndex {
            if !session.game.moves.isEmpty {
                return true
            }
        }
        
        // Check if there are existing moves from this position
        let mainLineNextIndex = session.game.moves.nextIndex(currentIndex: session.currentMoveIndex)
        let variations = session.game.moves.variations(from: session.currentMoveIndex)
        
        return mainLineNextIndex != nil || !variations.isEmpty
    }
    
    /// Gets the existing next move index for the current position
    private func getExistingNextMoveIndex(for move: Move, in session: GameSession) -> MoveTree.MoveIndex? {
        if session.currentMoveIndex == session.game.startingIndex {
            return session.game.moves.nextIndex(currentIndex: session.currentMoveIndex)
        }
        return session.game.moves.nextIndex(currentIndex: session.currentMoveIndex)
    }
}

// MARK: - Supporting Types

/// Result of a move execution attempt
enum MoveExecutionResult {
    case success(Move)
    case invalidMove
    case shouldDeselect
    case shouldReselect(Square)
    case variationDialogNeeded
    case promotionNeeded
}

/// Options for creating variations when making moves
enum VariationCreationOption {
    case newVariation      // Create new variation (default behavior)
    case newMainLine       // Create new variation and promote it
    case overwrite         // Create new variation and delete existing move
}
