//
//  EngineManager.swift
//  MacChessBase
//
//  Created on 2025/6/8.
//

import Foundation
import ChessKit
import Combine

/// Engine state enumeration for better state management
enum EngineState {
    case stopped    // Engine not running
    case idle       // Engine running but not analyzing
    case analyzing  // Engine actively analyzing
    case paused     // Analysis paused, results preserved
}

/// Manages communication with chess engines
@MainActor
final class EngineManager: ObservableObject {

    // MARK: - Singleton
    static let shared = EngineManager()

    // MARK: - Published Properties
    @Published var state: EngineState = .stopped
    @Published var currentEvaluation: EngineEvaluation?
    @Published var engineLines: [EngineLine] = []
    @Published var engineInfo: EngineInfo?
    
    // MARK: - Engine Settings
    @Published var maxDepth = 20
    @Published var maxLines = 2
    @Published var hashSizeMB = 128
    @Published var threadsCount = 1
    @Published var useFixedDepth = false
    
    // MARK: - Private Properties
    private var process: Process?
    private var inputPipe: Pipe?
    private var outputPipe: Pipe?
    private var cancellables = Set<AnyCancellable>()
    private let enginePath: String
    private var engineLineDict: [Int: EngineLine] = [:]
    private var analyzedPositionSideToMove: Piece.Color? // Added to store side to move for current analysis
    private var currentAnalysisPosition: Position? // 添加当前分析的局面

    // Continuations for awaiting engine responses
    private var uciOkContinuation: CheckedContinuation<Void, Never>?
    private var readyOkContinuation: CheckedContinuation<Void, Never>?
    private var bestMoveContinuation: CheckedContinuation<Void, Never>?

    // Debounce mechanism for position analysis
    private var debounceTask: Task<Void, Never>?

    // Analysis session tracking to prevent state confusion
    private var currentAnalysisId: Int = 0
    private var analysisSessionMap: [Int: Position] = [:] // Map analysis ID to position

    // 暂停时保存的分析结果
    private var pausedEvaluation: EngineEvaluation?
    private var pausedLines: [EngineLine] = []
    private var pausedLineDict: [Int: EngineLine] = [:]
    
    // MARK: - Initialization
    private init(enginePath: String? = nil) {
        if let customPath = enginePath {
            self.enginePath = customPath
        } else {
            // Try different ways to find the engine
            if let bundlePath = Bundle.main.path(forResource: "stockfish-macos-x86-64-bmi2", ofType: nil) {
                self.enginePath = bundlePath
            } else if let bundlePath = Bundle.main.path(forResource: "stockfish-macos-x86-64-bmi2", ofType: nil, inDirectory: "Engine") {
                self.enginePath = bundlePath
            } else {
                // Fallback to the project directory path (for development)
                let projectPath = "/Users/<USER>/Projects/MacChessBase/MacChessBase/stockfish-macos-x86-64-bmi2"
                if FileManager.default.fileExists(atPath: projectPath) {
                    self.enginePath = projectPath
                } else {
                    self.enginePath = ""
                }
            }
        }
        
        // Now we can initialize
        print("🚀 EngineManager initialized with path: \(self.enginePath)")
        print("✅ Engine file exists: \(FileManager.default.fileExists(atPath: self.enginePath))")
        
        // Check if the file is executable
        if FileManager.default.fileExists(atPath: self.enginePath) {
            let fileManager = FileManager.default
            if fileManager.isExecutableFile(atPath: self.enginePath) {
                print("✅ Engine file is executable")
            } else {
                print("❌ Engine file is NOT executable")
            }
        }
    }

    // MARK: - Testing Support
    /// Creates a new instance for testing purposes only
    internal init(forTesting: Bool, enginePath: String? = nil) {
        if let customPath = enginePath {
            self.enginePath = customPath
        } else {
            // Try different ways to find the engine
            if let bundlePath = Bundle.main.path(forResource: "stockfish-macos-x86-64-bmi2", ofType: nil) {
                self.enginePath = bundlePath
            } else if let bundlePath = Bundle.main.path(forResource: "stockfish-macos-x86-64-bmi2", ofType: nil, inDirectory: "Engine") {
                self.enginePath = bundlePath
            } else {
                // Fallback to the project directory path (for development)
                let projectPath = "/Users/<USER>/Projects/MacChessBase/MacChessBase/stockfish-macos-x86-64-bmi2"
                if FileManager.default.fileExists(atPath: projectPath) {
                    self.enginePath = projectPath
                } else {
                    self.enginePath = ""
                }
            }
        }

        // Now we can initialize
        print("🚀 EngineManager initialized with path: \(self.enginePath)")
        print("✅ Engine file exists: \(FileManager.default.fileExists(atPath: self.enginePath))")

        // Check if the file is executable
        if FileManager.default.fileExists(atPath: self.enginePath) {
            let fileManager = FileManager.default
            if fileManager.isExecutableFile(atPath: self.enginePath) {
                print("✅ Engine file is executable")
            } else {
                print("❌ Engine file is NOT executable")
            }
        }
    }

    // MARK: - Public Methods
    
    /// Starts the chess engine
    func startEngine() async {
        guard state == .stopped else {
            return
        }

        // Check if engine file exists
        guard FileManager.default.fileExists(atPath: enginePath) else {
            print("❌ Error: Engine file not found at path: \(enginePath)")
            return
        }

        // Check if file is executable
        let fileManager = FileManager.default
        guard fileManager.isExecutableFile(atPath: enginePath) else {
            print("❌ Engine file is NOT executable")
            return
        }

        // Create process and pipes
        process = Process()
        inputPipe = Pipe()
        outputPipe = Pipe()

        guard let process = process,
              let inputPipe = inputPipe,
              let outputPipe = outputPipe else {
            print("❌ Error: Failed to create process or pipes")
            return
        }

        process.executableURL = URL(fileURLWithPath: enginePath)
        process.standardInput = inputPipe
        process.standardOutput = outputPipe
        process.standardError = outputPipe

        // Set up output reading
        setupOutputReading()

        do {
            try process.run()
            print("✅ Engine process started successfully!")

            // Send UCI initialization commands and wait for responses
            await sendUciCommand()        // Wait for "uciok"
            await sendIsReadyCommand()    // Wait for "readyok"

            // Apply engine settings
            await applyEngineSettings()

            await MainActor.run {
                self.state = .idle
            }
            print("✅ Engine fully initialized and ready!")

        } catch {
            print("❌ Error starting engine: \(error)")
            cleanup()
        }
    }
    
    /// Stops the chess engine
    func stopEngine() async {
        guard state != .stopped else {
            return
        }

        await sendCommand("quit")

        // Give the engine time to quit gracefully
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds

        cleanup()
        print("✅ Engine stopped successfully")
    }
    
    /// Analyzes the current position with debounce mechanism
    /// - Parameters:
    ///   - position: The chess position to analyze
    ///   - debounceDelay: Delay in seconds before starting analysis (default: 0.25)
    func analyzePosition(_ position: Position, debounceDelay: TimeInterval = 0.25) async {
        // Cancel any existing debounce task
        debounceTask?.cancel()

        // Create new debounce task
        debounceTask = Task {
            // Wait for the debounce delay
            try? await Task.sleep(nanoseconds: UInt64(debounceDelay * 1_000_000_000))

            // Check if task was cancelled during the delay
            guard !Task.isCancelled else {
                print("🚫 Analysis request cancelled due to debounce")
                return
            }

            // Execute the actual analysis
            await analyzePositionImmediate(position)
        }
    }

    /// Immediately analyzes the given position without debounce
    private func analyzePositionImmediate(_ position: Position) async {
        // Increment analysis ID to track this session
        currentAnalysisId += 1
        let analysisId = currentAnalysisId

        print("🔍 Starting analysis session \(analysisId) for position: \(position.fen)")

        // If engine is not running, start it first
        if state == .stopped {
            await startEngine()
            guard state != .stopped else {
                print("❌ Failed to start engine for analysis")
                return
            }
        }

        // Check if this analysis session is still current after engine startup
        guard analysisId == currentAnalysisId else {
            print("🚫 Analysis session \(analysisId) cancelled - newer session started")
            return
        }

        // Stop any current analysis first
        if state == .analyzing {
            await sendStopCommand()
        }

        // Final check if this analysis session is still current
        guard analysisId == currentAnalysisId else {
            print("🚫 Analysis session \(analysisId) cancelled after stop command")
            return
        }

        // Store the position being analyzed and side to move
        self.currentAnalysisPosition = position
        self.analyzedPositionSideToMove = position.sideToMove
        self.analysisSessionMap[analysisId] = position // Store position for this session

        // Update UI state on main thread
        await MainActor.run {
            self.state = .analyzing
            self.engineLines.removeAll()
            self.engineLineDict.removeAll()
            self.currentEvaluation = nil
            print("🔍 UI Update - Starting analysis, cleared data")
        }

        // Final check before sending commands to engine
        guard analysisId == currentAnalysisId else {
            print("🚫 Analysis session \(analysisId) cancelled before engine commands")
            return
        }

        // Send position to engine
        let fen = position.fen
        await sendCommand("position fen \(fen)")

        // Check again after position command
        guard analysisId == currentAnalysisId else {
            print("🚫 Analysis session \(analysisId) cancelled after position command")
            return
        }

        // Configure engine settings
        await sendCommand("setoption name Hash value \(hashSizeMB)")
        await sendCommand("setoption name Threads value \(threadsCount)")
        await sendCommand("setoption name MultiPV value \(maxLines)")

        // Final check before starting analysis
        guard analysisId == currentAnalysisId else {
            print("🚫 Analysis session \(analysisId) cancelled before go command")
            return
        }

        // Start analysis based on mode
        if useFixedDepth {
            await sendCommand("go depth \(maxDepth)")
        } else {
            await sendCommand("go infinite")
        }

        print("✅ Analysis started for session \(analysisId) with \(maxLines) lines")
    }
    
    /// Stops the current analysis
    func stopAnalysis() async {
        guard state == .analyzing else { return }

        await sendStopCommand()
        await MainActor.run {
            self.state = .idle
            print("🛑 UI Update - Analysis stopped")
        }
    }

    /// Pauses the current analysis and preserves results
    func pauseAnalysis() async {
        guard state == .analyzing else { return }

        await sendStopCommand()
        await MainActor.run {
            // Save current analysis results
            self.pausedEvaluation = self.currentEvaluation
            self.pausedLines = Array(self.engineLines)
            self.pausedLineDict = self.engineLineDict

            // Update state
            self.state = .paused
            print("⏸️ UI Update - Analysis paused, results preserved")
        }
    }
    
    /// Resumes analysis from paused state with given position
    func resumeAnalysis(with position: Position) async {
        guard state == .paused else { return }

        await MainActor.run {
            // Restore saved analysis results
            self.currentEvaluation = self.pausedEvaluation
            self.engineLines = Array(self.pausedLines)
            self.engineLineDict = self.pausedLineDict

            print("▶️ UI Update - Analysis resumed, results restored")
        }

        // Restart engine analysis
        await analyzePosition(position)
    }

    /// Clears all analysis results and stops engine if running
    func clearAnalysisResults() async {
        // Stop engine completely if it's running
        if state != .stopped {
            await stopEngine()
        }

        await MainActor.run {
            self.currentEvaluation = nil
            self.engineLines.removeAll()
            self.engineLineDict.removeAll()
            self.pausedEvaluation = nil
            self.pausedLines.removeAll()
            self.pausedLineDict.removeAll()
            self.state = .stopped
            print("🗑️ UI Update - Analysis results cleared and engine stopped")
        }
    }
    
    /// Applies current settings to the running engine
    func applyEngineSettings() async {
        guard state == .idle || state == .analyzing else { return }
        
        await sendCommand("setoption name Hash value \(hashSizeMB)")
        await sendCommand("setoption name Threads value \(threadsCount)")
        
        print("✅ Engine settings applied: Hash=\(hashSizeMB)MB, Threads=\(threadsCount)")
    }
    
    // MARK: - Private Methods
    
    private func setupOutputReading() {
        guard let outputPipe = outputPipe else { return }
        
        let fileHandle = outputPipe.fileHandleForReading
        
        // Set up continuous reading
        fileHandle.readabilityHandler = { [weak self] handle in
            let data = handle.availableData
            guard !data.isEmpty else { return }
            
            if let output = String(data: data, encoding: .utf8) {
                Task {
                    await self?.processEngineOutput(output)
                }
            }
        }
    }
    
    private func processEngineOutput(_ output: String) async {
        let lines = output.components(separatedBy: .newlines)
        
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)
            guard !trimmed.isEmpty else { continue }
            
            // Parse different types of engine output
            if trimmed.hasPrefix("id name") {
                await parseEngineInfo(trimmed)
            } else if trimmed.hasPrefix("id author") {
                await parseEngineAuthor(trimmed)
            } else if trimmed.hasPrefix("info") {
                await parseAnalysisInfo(trimmed, analysisId: currentAnalysisId)
            } else if trimmed == "readyok" {
                print("✅ Engine ready")
                readyOkContinuation?.resume()
                readyOkContinuation = nil
            } else if trimmed == "uciok" {
                print("✅ Engine UCI initialized")
                uciOkContinuation?.resume()
                uciOkContinuation = nil
            } else if trimmed.hasPrefix("bestmove") {
                await MainActor.run {
                    self.state = .idle
                }
                print("🎯 Analysis complete")
                bestMoveContinuation?.resume()
                bestMoveContinuation = nil
            }
        }
    }
    
    private func parseEngineInfo(_ line: String) async {
        if line.hasPrefix("id name") {
            let name = String(line.dropFirst(8)) // Remove "id name "
            await MainActor.run {
                if self.engineInfo == nil {
                    self.engineInfo = EngineInfo(name: name, version: "")
                } else {
                    self.engineInfo = EngineInfo(name: name, version: self.engineInfo?.version ?? "")
                }
            }
        }
    }
    
    private func parseEngineAuthor(_ line: String) async {
        if line.hasPrefix("id author") {
            let author = String(line.dropFirst(10)) // Remove "id author "
            // Can store author info if needed
        }
    }
    
    private func parseAnalysisInfo(_ line: String, analysisId: Int) async {
        // Check if this analysis session is still current
        guard analysisId == currentAnalysisId else {
            print("🚫 Ignoring analysis output from old session \(analysisId)")
            return
        }
        let components = line.components(separatedBy: " ")
        guard components.count > 2 else { return }
        
        var depth: Int?
        var score: String? // This will be the adjusted score string
        var pv: [String] = []
        var nodes: Int?
        var multipv: Int?
        var time: Int?
        var nps: Int? // Nodes per second
        var hashfull: Int? // Hash table usage
        
        var i = 1 // Skip "info"
        while i < components.count {
            switch components[i] {
            case "depth":
                if i + 1 < components.count {
                    depth = Int(components[i + 1])
                    i += 2
                } else {
                    i += 1
                }
            case "seldepth":
                // Selective depth - skip for now
                if i + 1 < components.count {
                    i += 2
                } else {
                    i += 1
                }
            case "score":
                if i + 2 < components.count {
                    let scoreType = components[i + 1]
                    let scoreValue = components[i + 2]
                    
                    // Use the stored sideToMove for the currently analyzed position
                    // Default to .white if not set, though it should always be set by analyzePosition
                    let sideToMoveForThisEval = self.analyzedPositionSideToMove ?? .white

                    if scoreType == "cp" {
                        if let centipawns = Int(scoreValue) { // centipawns is from White's perspective
                            var displayScoreValue = Double(centipawns) / 100.0
                            if sideToMoveForThisEval == .black {
                                displayScoreValue = -displayScoreValue // Flip for Black's perspective
                            }
                            score = String(format: "%+.2f", displayScoreValue)
                        }
                    } else if scoreType == "mate" {
                        if let mateInRaw = Int(scoreValue) { // mateInRaw: positive if White mates, negative if Black mates
                            if sideToMoveForThisEval == .white {
                                if mateInRaw > 0 { // White mates (good for White)
                                    score = "M\\(mateInRaw)"
                                } else { // Black mates (bad for White)
                                    score = "-M\\(abs(mateInRaw))"
                                }
                            } else { // sideToMoveForThisEval == .black
                                if mateInRaw > 0 { // White mates (bad for Black)
                                    score = "-M\\(mateInRaw)"
                                } else { // Black mates (good for Black)
                                    score = "M\\(abs(mateInRaw))"
                                }
                            }
                        }
                    }
                    i += 3
                } else {
                    i += 1
                }
            case "nodes":
                if i + 1 < components.count {
                    nodes = Int(components[i + 1])
                    i += 2
                } else {
                    i += 1
                }
            case "nps":
                if i + 1 < components.count {
                    nps = Int(components[i + 1])
                    i += 2
                } else {
                    i += 1
                }
            case "hashfull":
                if i + 1 < components.count {
                    hashfull = Int(components[i + 1])
                    i += 2
                } else {
                    i += 1
                }
            case "multipv":
                if i + 1 < components.count {
                    multipv = Int(components[i + 1])
                    i += 2
                } else {
                    i += 1
                }
            case "time":
                if i + 1 < components.count {
                    time = Int(components[i + 1])
                    i += 2
                } else {
                    i += 1
                }
            case "pv":
                // Principal variation follows
                pv = Array(components[(i + 1)...])
                i = components.count // Exit loop
            default:
                i += 1
            }
        }
        
        // Create engine line if we have the required data
        if let depth = depth,
           let score = score,
           let nodes = nodes,
           let multipv = multipv,
           let analysisPosition = self.analysisSessionMap[analysisId],
           !pv.isEmpty {
            
            let pvString = pv.joined(separator: " ")
            let line = EngineLine(
                id: UUID(),
                depth: depth,
                evaluation: score,
                principalVariation: pvString,
                nodes: nodes,
                multiPV: multipv,
                nps: nps,
                time: time,
                analysisPosition: analysisPosition
            )
            
            // Reduced debug output - analysis results now primarily displayed in UI
            print("📊 Line \(multipv): D\(depth) \(score) - \(pvString.prefix(30))")
            
            // 用字典收集所有 multipv 的分析行，并整体替换 engineLines
            await MainActor.run {
                self.engineLineDict[multipv] = line
                let sortedLines = self.engineLineDict.keys.sorted().compactMap { self.engineLineDict[$0] }
                self.engineLines = sortedLines
                if multipv == 1 {
                    self.currentEvaluation = EngineEvaluation(
                        value: score,
                        depth: depth,
                        nodes: nodes,
                        time: time ?? 0,
                        nps: nps
                    )
                }
            }
        }
    }

    // MARK: - Async Command Methods

    private func sendUciCommand() async {
        await sendCommand("uci")
        await withCheckedContinuation { continuation in
            uciOkContinuation = continuation
        }
    }

    private func sendIsReadyCommand() async {
        await sendCommand("isready")
        await withCheckedContinuation { continuation in
            readyOkContinuation = continuation
        }
    }

    private func sendStopCommand() async {
        await sendCommand("stop")

        // Only wait for bestmove if we don't already have a pending continuation
        if bestMoveContinuation == nil {
            await withCheckedContinuation { continuation in
                bestMoveContinuation = continuation
            }
        } else {
            // If there's already a pending continuation, just give a small delay
            // to allow the engine to process the stop command
            try? await Task.sleep(nanoseconds: 50_000_000) // 0.05 seconds
        }
    }

    private func sendCommand(_ command: String) async {
        guard let inputPipe = inputPipe else { 
            print("❌ No input pipe available")
            return 
        }
        
        let data = (command + "\n").data(using: .utf8)!
        inputPipe.fileHandleForWriting.write(data)
    }
    
    private func cleanup() {
        // Cancel any pending debounce tasks
        debounceTask?.cancel()
        debounceTask = nil

        // Clean up any pending continuations to prevent leaks
        uciOkContinuation?.resume()
        uciOkContinuation = nil
        readyOkContinuation?.resume()
        readyOkContinuation = nil
        bestMoveContinuation?.resume()
        bestMoveContinuation = nil

        // Clear analysis session map
        analysisSessionMap.removeAll()

        process?.terminate()
        process?.waitUntilExit()

        inputPipe?.fileHandleForWriting.closeFile()
        outputPipe?.fileHandleForReading.closeFile()

        process = nil
        inputPipe = nil
        outputPipe = nil

        // Update state synchronously since we're already on MainActor
        self.state = .stopped
        self.engineLines.removeAll()
        self.currentEvaluation = nil
        self.pausedEvaluation = nil
        self.pausedLines.removeAll()
        self.pausedLineDict.removeAll()
    }
}

// MARK: - Data Models

/// Information about the chess engine
struct EngineInfo {
    let name: String
    let version: String
}

/// Current position evaluation from the engine
struct EngineEvaluation {
    let value: String  // e.g., "+0.24", "M5"
    let depth: Int
    let nodes: Int
    let time: Int  // milliseconds
    let nps: Int?  // nodes per second
}

/// A single analysis line from the engine
struct EngineLine: Identifiable {
    let id: UUID
    let depth: Int
    let evaluation: String
    let principalVariation: String
    let nodes: Int
    let multiPV: Int
    let nps: Int?  // nodes per second
    let time: Int? // analysis time in milliseconds
    let analysisPosition: Position // 添加引擎分析时的局面
    
    init(id: UUID = UUID(), depth: Int, evaluation: String, principalVariation: String, nodes: Int, multiPV: Int = 1, nps: Int? = nil, time: Int? = nil, analysisPosition: Position) {
        self.id = id
        self.depth = depth
        self.evaluation = evaluation
        self.principalVariation = principalVariation
        self.nodes = nodes
        self.multiPV = multiPV
        self.nps = nps
        self.time = time
        self.analysisPosition = analysisPosition
    }
}
