//
//  FIDEService.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/7/2.
//

import Foundation
import SwiftSoup

/// Service for fetching and managing FIDE player information
@MainActor
class FIDEService: ObservableObject {
    static let shared = FIDEService()

    /// Represents a FIDE player's information
    struct FIDEPlayer {
        let fideId: String
        let federation: String?
        let photoData: Data?
        let photoBase64: String?
    }

    /// Errors that can occur during FIDE data fetching
    enum FIDEError: Error, LocalizedError {
        case invalidFideId
        case networkError(Error)
        case parseError
        case playerNotFound
        case invalidURL

        var errorDescription: String? {
            switch self {
            case .invalidFideId:
                return "Invalid FIDE ID format"
            case .networkError(let error):
                return "Network error: \(error.localizedDescription)"
            case .parseError:
                return "Failed to parse FIDE website data"
            case .playerNotFound:
                return "Player not found in FIDE database"
            case .invalidURL:
                return "Invalid FIDE URL"
            }
        }
    }

    // MARK: - Global FIDE Data Cache
    private var fideDataCache: [String: FIDEPlayerData] = [:]
    private let session = URLSession.shared

    private init() {
        loadCacheFromDisk()
    }

    // MARK: - Public Interface

    /// Gets cached FIDE data for a player, returns nil if not cached or expired
    func getCachedFIDEData(for fideId: String) -> FIDEPlayerData? {
        guard !fideId.isEmpty else { return nil }

        let cachedData = fideDataCache[fideId]

        // Check if data is expired (24 hours)
        if let data = cachedData, !data.isExpired {
            return data
        } else if cachedData != nil {
            // Remove expired data
            fideDataCache.removeValue(forKey: fideId)
            saveCacheToDisk()
        }

        return nil
    }

    /// Fetches FIDE data if not cached, otherwise returns cached data
    func fetchFIDEDataIfNeeded(for fideId: String) async -> FIDEPlayerData? {
        // First check cache
        if let cachedData = getCachedFIDEData(for: fideId) {
            return cachedData
        }

        // Fetch from network if not cached
        do {
            let fidePlayer = try await fetchPlayerInfo(fideId: fideId)
            let cacheData = createCacheData(from: fidePlayer)

            // Cache the result
            cacheFIDEData(cacheData)

            return cacheData
        } catch {
            #if DEBUG
            print("Failed to fetch FIDE data for ID \(fideId): \(error)")
            #endif
            return nil
        }
    }

    /// Caches FIDE data
    func cacheFIDEData(_ data: FIDEPlayerData) {
        fideDataCache[data.fideId] = data
        saveCacheToDisk()
    }

    /// Clears all cached FIDE data
    func clearFIDEDataCache() {
        fideDataCache.removeAll()
        saveCacheToDisk()
    }

    /// Cleans up expired FIDE data
    func cleanupExpiredFIDEData() {
        let expiredKeys = fideDataCache.compactMap { key, value in
            value.isExpired ? key : nil
        }

        for key in expiredKeys {
            fideDataCache.removeValue(forKey: key)
        }

        if !expiredKeys.isEmpty {
            saveCacheToDisk()
        }
    }

    // MARK: - Network Operations

    /// Fetches player information from FIDE website by FIDE ID
    /// - Parameter fideId: The FIDE ID to look up
    /// - Returns: FIDEPlayer information
    private func fetchPlayerInfo(fideId: String) async throws -> FIDEPlayer {
        // Validate FIDE ID
        guard !fideId.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw FIDEError.invalidFideId
        }
        
        // Construct FIDE URL
        guard let url = URL(string: "https://ratings.fide.com/profile/\(fideId)") else {
            throw FIDEError.invalidURL
        }
        
        do {
            // Fetch HTML content
            let (data, _) = try await session.data(from: url)
            guard let html = String(data: data, encoding: .utf8) else {
                throw FIDEError.parseError
            }
            
            // Parse player information
            return try parsePlayerInfo(from: html, fideId: fideId)
            
        } catch {
            if error is FIDEError {
                throw error
            } else {
                throw FIDEError.networkError(error)
            }
        }
    }
    
    /// Parses player information from HTML content
    /// - Parameters:
    ///   - html: The HTML content from FIDE website
    ///   - fideId: The FIDE ID to look up
    /// - Returns: FIDEPlayer information
    func parsePlayerInfo(from html: String, fideId: String) throws -> FIDEPlayer {
        var federation: String?
        var photoData: Data?
        var photoBase64: String?
        
        do {
            let doc = try SwiftSoup.parse(html)
            
            // Parse federation (country code)
            federation = try parseFederation(from: doc)
            
            // Parse player photo
            let photoResult = try parsePlayerPhoto(from: doc)
            photoData = photoResult.data
            photoBase64 = photoResult.base64
            
        } catch {
            throw FIDEError.parseError
        }
        
        return FIDEPlayer(
            fideId: fideId,
            federation: federation,
            photoData: photoData,
            photoBase64: photoBase64
        )
    }

    /// Converts FIDEPlayer to FIDEPlayerData for caching
    private func createCacheData(from player: FIDEPlayer) -> FIDEPlayerData {
        return FIDEPlayerData(
            fideId: player.fideId,
            federation: player.federation,
            photoData: player.photoData,
            photoBase64: player.photoBase64,
            fetchDate: Date()
        )
    }
    
    /// Extracts player name from the document
    private func parsePlayerName(from doc: Document) throws -> String? {
        // Try multiple selectors for player name
        let nameSelectors = [
            ".profile-top-title",
            "h1.profile-top-title",
            ".profile-top-info h1",
            "h1"
        ]

        for selector in nameSelectors {
            if let nameElement = try doc.select(selector).first() {
                let nameText = try nameElement.text().trimmingCharacters(in: .whitespacesAndNewlines)
                if !nameText.isEmpty {
                    return nameText
                }
            }
        }

        return nil
    }
    
    /// Extracts federation code from the document
    private func parseFederation(from doc: Document) throws -> String? {
        // 首先尝试从 .profile-info 元素中提取联盟信息
        if let profileInfo = try doc.select(".profile-info").first() {
            let profileText = try profileInfo.text()
            print("🔍 Profile info text: '\(profileText)'")

            // 使用正则表达式提取Federation后面的国家名称
            // 格式: "FIDE ID 8609667 Federation China B-Year 2003 Gender Male FIDE title FIDE Master"
            let pattern = "Federation\\s+([A-Za-z\\s]+?)\\s+[A-Z]"
            if let regex = try? NSRegularExpression(pattern: pattern, options: []),
               let match = regex.firstMatch(in: profileText, options: [], range: NSRange(location: 0, length: profileText.count)),
               let range = Range(match.range(at: 1), in: profileText) {
                let countryName = String(profileText[range]).trimmingCharacters(in: .whitespacesAndNewlines)
                print("✅ 找到国家名称: '\(countryName)'")
                return countryName
            }
        }

        print("⚠️ 未找到联盟信息")
        return nil
    }
    
    /// Extracts player photo from the document
    private func parsePlayerPhoto(from doc: Document) throws -> (data: Data?, base64: String?) {
        // 查找选手照片，通常有特定的class名称
        let photoSelectors = [
            ".profile-top__photo",  // 从调试输出中发现的class
            "img[alt='photo']",     // 通过alt属性查找
            ".profile-photo",
            ".player-photo",
            ".profile img"
        ]

        for selector in photoSelectors {
            if let photoElement = try doc.select(selector).first() {
                let src = try photoElement.attr("src")

                // 检查是否是base64编码的图片
                if src.hasPrefix("data:image/") {
                    // 提取base64数据部分
                    if let base64Range = src.range(of: "base64,") {
                        let base64String = String(src[base64Range.upperBound...])

                        // 将base64字符串转换为Data
                        if let imageData = Data(base64Encoded: base64String) {
                            print("✅ 找到选手照片 (base64编码): \(imageData.count) 字节")
                            return (data: imageData, base64: base64String)
                        }
                    }
                } else if !src.isEmpty {
                    // 如果是URL形式的图片，返回URL信息
                    print("✅ 找到选手照片 (URL): \(src)")
                    return (data: nil, base64: src) // 将URL存储在base64字段中
                }
            }
        }

        print("⚠️ 未找到选手照片")
        return (data: nil, base64: nil)
    }
    
    /// 调试函数：输出HTML文档的所有标签和属性
    func debugHTMLStructure(doc: Document) throws {
        print("\n🔍 HTML结构调试信息")
        print(String(repeating: "=", count: 60))

        // 获取所有元素
        let allElements = try doc.select("*")

        print("📊 总共找到 \(allElements.count) 个HTML元素\n")

        // 按标签名分组统计
        var tagCounts: [String: Int] = [:]
        for element in allElements {
            let tagName = element.tagName()
            tagCounts[tagName] = (tagCounts[tagName] ?? 0) + 1
        }

        print("📋 标签统计:")
        for (tag, count) in tagCounts.sorted(by: { $0.key < $1.key }) {
            print("   \(tag): \(count)个")
        }

        print("\n🎯 重点关注的元素:")

        // 查找可能包含选手信息的关键元素
        try debugPlayerInfoElements(doc: doc)

        print("\n📝 所有表格内容:")
        try debugTableContent(doc: doc)

        print("\n🏷️ 所有带class的元素:")
        try debugClassElements(doc: doc)

        print("\n🆔 所有带id的元素:")
        try debugIdElements(doc: doc)

        print(String(repeating: "=", count: 60))
    }

    /// 调试选手信息相关元素
    private func debugPlayerInfoElements(doc: Document) throws {
        // 查找标题元素
        let titleSelectors = ["h1", "h2", "h3", ".title", ".name", ".profile"]
        for selector in titleSelectors {
            let elements = try doc.select(selector)
            if !elements.isEmpty() {
                print("   \(selector): 找到\(elements.count)个")
                for (index, element) in elements.enumerated() {
                    let text = try element.text().trimmingCharacters(in: .whitespacesAndNewlines)
                    let className = try element.className()
                    let id = element.id()
                    print("     [\(index)] 文本: '\(text.prefix(50))' | class: '\(className)' | id: '\(id)'")
                }
            }
        }

        // 查找图片元素（可能包含国旗、头像等信息）
        let images = try doc.select("img")
        if !images.isEmpty() {
            print("   img: 找到\(images.count)个图片")
            for (index, img) in images.enumerated() {
                let src = try img.attr("src")
                let alt = try img.attr("alt")
                let className = try img.className()
                let id = img.id()
                let title = try img.attr("title")
                print("     [\(index)] src: '\(src.prefix(50))' | alt: '\(alt)' | class: '\(className)' | id: '\(id)' | title: '\(title)'")
            }
        }
    }

    /// 调试表格内容
    private func debugTableContent(doc: Document) throws {
        let tables = try doc.select("table")
        print("   找到 \(tables.count) 个表格")

        for (tableIndex, table) in tables.enumerated() {
            print("   表格 \(tableIndex + 1):")
            let rows = try table.select("tr")

            for (rowIndex, row) in rows.enumerated() {
                let cells = try row.select("td, th")
                if !cells.isEmpty() {
                    var rowData: [String] = []
                    for cell in cells {
                        let cellText = try cell.text().trimmingCharacters(in: .whitespacesAndNewlines)
                        rowData.append(cellText.isEmpty ? "[空]" : String(cellText.prefix(30)))
                    }
                    print("     行\(rowIndex + 1): \(rowData.joined(separator: " | "))")
                }
            }
        }
    }

    /// 调试带class的元素
    private func debugClassElements(doc: Document) throws {
        let elementsWithClass = try doc.select("[class]")
        var classMap: [String: [String]] = [:]

        for element in elementsWithClass {
            let className = try element.className()
            let text = try element.text().trimmingCharacters(in: .whitespacesAndNewlines)
            let preview = text.isEmpty ? "[无文本]" : String(text.prefix(30))

            if classMap[className] == nil {
                classMap[className] = []
            }
            classMap[className]?.append(preview)
        }

        for (className, texts) in classMap.sorted(by: { $0.key < $1.key }) {
            print("   .\(className): \(texts.count)个元素")
            for (index, text) in texts.prefix(3).enumerated() {
                print("     [\(index + 1)] \(text)")
            }
            if texts.count > 3 {
                print("     ... 还有\(texts.count - 3)个")
            }
        }
    }

    /// 调试带id的元素
    private func debugIdElements(doc: Document) throws {
        let elementsWithId = try doc.select("[id]")

        for element in elementsWithId {
            let id = element.id()
            let tagName = element.tagName()
            let text = try element.text().trimmingCharacters(in: .whitespacesAndNewlines)
            let preview = text.isEmpty ? "[无文本]" : String(text.prefix(50))
            print("   #\(id) (\(tagName)): \(preview)")
        }
    }
    
}

// MARK: - Country Code to Name Mapping
extension FIDEService {
    
    /// Converts full country name to FIDE federation code
    func countryNameToFederationCode(_ countryName: String) -> String? {
        let federationMap: [String: String] = [
            "Afghanistan": "AFG",
            "Albania": "ALB",
            "Algeria": "ALG",
            "Andorra": "AND",
            "Angola": "ANG",
            "Antigua and Barbuda": "ANT",
            "Argentina": "ARG",
            "Armenia": "ARM",
            "Aruba": "ARU",
            "Australia": "AUS",
            "Austria": "AUT",
            "Azerbaijan": "AZE",
            "Bahamas": "BAH",
            "Bahrain": "BRN",
            "Bangladesh": "BAN",
            "Barbados": "BAR",
            "Belarus": "BLR",
            "Belgium": "BEL",
            "Belize": "BIZ",
            "Benin": "BEN",
            "Bermuda": "BER",
            "Bhutan": "BHU",
            "Bolivia": "BOL",
            "Bosnia and Herzegovina": "BIH",
            "Botswana": "BOT",
            "Brazil": "BRA",
            "British Virgin Islands": "IVB",
            "Brunei": "BRU",
            "Bulgaria": "BUL",
            "Burkina Faso": "BUR",
            "Burundi": "BDI",
            "Cambodia": "CAM",
            "Cameroon": "CMR",
            "Canada": "CAN",
            "Cape Verde": "CPV",
            "Cayman Islands": "CAY",
            "Central African Republic": "CAF",
            "Chad": "CHA",
            "Chile": "CHI",
            "China": "CHN",
            "Colombia": "COL",
            "Comoros": "COM",
            "Congo": "CGO",
            "Cook Islands": "COK",
            "Costa Rica": "CRC",
            "Croatia": "CRO",
            "Cuba": "CUB",
            "Cyprus": "CYP",
            "Czech Republic": "CZE",
            "Democratic Republic of the Congo": "COD",
            "Denmark": "DEN",
            "Djibouti": "DJI",
            "Dominica": "DMA",
            "Dominican Republic": "DOM",
            "Ecuador": "ECU",
            "Egypt": "EGY",
            "El Salvador": "ESA",
            "England": "ENG",
            "Equatorial Guinea": "GEQ",
            "Eritrea": "ERI",
            "Estonia": "EST",
            "Ethiopia": "ETH",
            "Faroe Islands": "FAI",
            "Fiji": "FIJ",
            "Finland": "FIN",
            "France": "FRA",
            "Gabon": "GAB",
            "Gambia": "GAM",
            "Georgia": "GEO",
            "Germany": "GER",
            "Ghana": "GHA",
            "Greece": "GRE",
            "Grenada": "GRN",
            "Guatemala": "GUA",
            "Guernsey": "GCI",
            "Guinea": "GUI",
            "Guinea-Bissau": "GBS",
            "Guyana": "GUY",
            "Haiti": "HAI",
            "Honduras": "HON",
            "Hong Kong": "HKG",
            "Hungary": "HUN",
            "Iceland": "ISL",
            "India": "IND",
            "Indonesia": "INA",
            "Iran": "IRI",
            "Iraq": "IRQ",
            "Ireland": "IRL",
            "Isle of Man": "IOM",
            "Israel": "ISR",
            "Italy": "ITA",
            "Ivory Coast": "CIV",
            "Jamaica": "JAM",
            "Japan": "JPN",
            "Jersey": "JCI",
            "Jordan": "JOR",
            "Kazakhstan": "KAZ",
            "Kenya": "KEN",
            "Kyrgyzstan": "KGZ",
            "Laos": "LAO",
            "Latvia": "LAT",
            "Lebanon": "LBN",
            "Lesotho": "LES",
            "Liberia": "LBR",
            "Libya": "LBA",
            "Liechtenstein": "LIE",
            "Lithuania": "LTU",
            "Luxembourg": "LUX",
            "Macau": "MAC",
            "Madagascar": "MAD",
            "Malawi": "MAW",
            "Malaysia": "MAS",
            "Maldives": "MDV",
            "Mali": "MLI",
            "Malta": "MLT",
            "Marshall Islands": "MHL",
            "Mauritania": "MTN",
            "Mauritius": "MRI",
            "Mexico": "MEX",
            "Micronesia": "FSM",
            "Moldova": "MDA",
            "Monaco": "MNC",
            "Mongolia": "MGL",
            "Montenegro": "MNE",
            "Morocco": "MAR",
            "Mozambique": "MOZ",
            "Myanmar": "MYA",
            "Namibia": "NAM",
            "Nauru": "NRU",
            "Nepal": "NEP",
            "Netherlands": "NED",
            "New Zealand": "NZL",
            "Nicaragua": "NCA",
            "Niger": "NIG",
            "Nigeria": "NGR",
            "North Korea": "PRK",
            "North Macedonia": "MKD",
            "Northern Ireland": "NIR",
            "Norway": "NOR",
            "Oman": "OMA",
            "Pakistan": "PAK",
            "Palau": "PLW",
            "Palestine": "PLE",
            "Panama": "PAN",
            "Papua New Guinea": "PNG",
            "Paraguay": "PAR",
            "Peru": "PER",
            "Philippines": "PHI",
            "Poland": "POL",
            "Portugal": "POR",
            "Puerto Rico": "PUR",
            "Qatar": "QAT",
            "Romania": "ROU",
            "Russia": "RUS",
            "Rwanda": "RWA",
            "Saint Kitts and Nevis": "SKN",
            "Saint Lucia": "LCA",
            "Saint Vincent and the Grenadines": "VIN",
            "Samoa": "SAM",
            "San Marino": "SMR",
            "Sao Tome and Principe": "STP",
            "Saudi Arabia": "KSA",
            "Scotland": "SCO",
            "Senegal": "SEN",
            "Serbia": "SRB",
            "Seychelles": "SEY",
            "Sierra Leone": "SLE",
            "Singapore": "SGP",
            "Slovakia": "SVK",
            "Slovenia": "SLO",
            "Solomon Islands": "SOL",
            "Somalia": "SOM",
            "South Africa": "RSA",
            "South Korea": "KOR",
            "South Sudan": "SSD",
            "Spain": "ESP",
            "Sri Lanka": "SRI",
            "Sudan": "SUD",
            "Suriname": "SUR",
            "Swaziland": "SWZ",
            "Sweden": "SWE",
            "Switzerland": "SUI",
            "Syria": "SYR",
            "Taiwan": "TPE",
            "Tajikistan": "TJK",
            "Tanzania": "TAN",
            "Thailand": "THA",
            "Timor-Leste": "TLS",
            "Togo": "TOG",
            "Tonga": "TON",
            "Trinidad and Tobago": "TTO",
            "Tunisia": "TUN",
            "Turkey": "TUR",
            "Turkmenistan": "TKM",
            "Tuvalu": "TUV",
            "Uganda": "UGA",
            "Ukraine": "UKR",
            "United Arab Emirates": "UAE",
            "United States": "USA",
            "Uruguay": "URU",
            "Uzbekistan": "UZB",
            "Vanuatu": "VAN",
            "Venezuela": "VEN",
            "Vietnam": "VIE",
            "US Virgin Islands": "ISV",
            "Wales": "WLS",
            "Yemen": "YEM",
            "Zambia": "ZAM",
            "Zimbabwe": "ZIM"
        ]

        return federationMap[countryName]
    }

    /// Converts FIDE federation code to full country name
    func federationCodeToCountryName(_ code: String) -> String {
        let federationMap: [String: String] = [
            "AFG": "Afghanistan",
            "ALB": "Albania",
            "ALG": "Algeria",
            "AND": "Andorra",
            "ANG": "Angola",
            "ANT": "Antigua and Barbuda",
            "ARG": "Argentina",
            "ARM": "Armenia",
            "ARU": "Aruba",
            "AUS": "Australia",
            "AUT": "Austria",
            "AZE": "Azerbaijan",
            "BAH": "Bahamas",
            "BRN": "Bahrain",
            "BAN": "Bangladesh",
            "BAR": "Barbados",
            "BLR": "Belarus",
            "BEL": "Belgium",
            "BIZ": "Belize",
            "BEN": "Benin",
            "BER": "Bermuda",
            "BHU": "Bhutan",
            "BOL": "Bolivia",
            "BIH": "Bosnia and Herzegovina",
            "BOT": "Botswana",
            "BRA": "Brazil",
            "IVB": "British Virgin Islands",
            "BRU": "Brunei",
            "BUL": "Bulgaria",
            "BUR": "Burkina Faso",
            "BDI": "Burundi",
            "CAM": "Cambodia",
            "CMR": "Cameroon",
            "CAN": "Canada",
            "CPV": "Cape Verde",
            "CAY": "Cayman Islands",
            "CAF": "Central African Republic",
            "CHA": "Chad",
            "CHI": "Chile",
            "CHN": "China",
            "COL": "Colombia",
            "COM": "Comoros",
            "CGO": "Congo",
            "COD": "Democratic Republic of the Congo",
            "COK": "Cook Islands",
            "CRC": "Costa Rica",
            "CIV": "Côte d'Ivoire",
            "CRO": "Croatia",
            "CUB": "Cuba",
            "CYP": "Cyprus",
            "CZE": "Czech Republic",
            "DEN": "Denmark",
            "DJI": "Djibouti",
            "DMA": "Dominica",
            "DOM": "Dominican Republic",
            "ECU": "Ecuador",
            "EGY": "Egypt",
            "ESA": "El Salvador",
            "GEQ": "Equatorial Guinea",
            "ERI": "Eritrea",
            "EST": "Estonia",
            "ETH": "Ethiopia",
            "FAI": "Faroe Islands",
            "FIJ": "Fiji",
            "FIN": "Finland",
            "FRA": "France",
            "GAB": "Gabon",
            "GAM": "Gambia",
            "GEO": "Georgia",
            "GER": "Germany",
            "GHA": "Ghana",
            "GRE": "Greece",
            "GRN": "Grenada",
            "GUA": "Guatemala",
            "GUI": "Guinea",
            "GBS": "Guinea-Bissau",
            "GUY": "Guyana",
            "HAI": "Haiti",
            "HON": "Honduras",
            "HKG": "Hong Kong",
            "HUN": "Hungary",
            "ISL": "Iceland",
            "IND": "India",
            "INA": "Indonesia",
            "IRI": "Iran",
            "IRQ": "Iraq",
            "IRL": "Ireland",
            "ISR": "Israel",
            "ITA": "Italy",
            "JAM": "Jamaica",
            "JPN": "Japan",
            "JOR": "Jordan",
            "KAZ": "Kazakhstan",
            "KEN": "Kenya",
            "KIR": "Kiribati",
            "PRK": "North Korea",
            "KOR": "South Korea",
            "KUW": "Kuwait",
            "KGZ": "Kyrgyzstan",
            "LAO": "Laos",
            "LAT": "Latvia",
            "LIB": "Lebanon",
            "LES": "Lesotho",
            "LBR": "Liberia",
            "LBA": "Libya",
            "LIE": "Liechtenstein",
            "LTU": "Lithuania",
            "LUX": "Luxembourg",
            "MAC": "Macau",
            "MKD": "North Macedonia",
            "MAD": "Madagascar",
            "MAW": "Malawi",
            "MAS": "Malaysia",
            "MDV": "Maldives",
            "MLI": "Mali",
            "MLT": "Malta",
            "MHL": "Marshall Islands",
            "MTN": "Mauritania",
            "MRI": "Mauritius",
            "MEX": "Mexico",
            "FSM": "Micronesia",
            "MDA": "Moldova",
            "MON": "Monaco",
            "MGL": "Mongolia",
            "MNE": "Montenegro",
            "MAR": "Morocco",
            "MOZ": "Mozambique",
            "MYA": "Myanmar",
            "NAM": "Namibia",
            "NRU": "Nauru",
            "NEP": "Nepal",
            "NED": "Netherlands",
            "NZL": "New Zealand",
            "NCA": "Nicaragua",
            "NIG": "Niger",
            "NGR": "Nigeria",
            "NOR": "Norway",
            "OMA": "Oman",
            "PAK": "Pakistan",
            "PLW": "Palau",
            "PLE": "Palestine",
            "PAN": "Panama",
            "PNG": "Papua New Guinea",
            "PAR": "Paraguay",
            "PER": "Peru",
            "PHI": "Philippines",
            "POL": "Poland",
            "POR": "Portugal",
            "PUR": "Puerto Rico",
            "QAT": "Qatar",
            "ROU": "Romania",
            "RUS": "Russia",
            "RWA": "Rwanda",
            "SKN": "Saint Kitts and Nevis",
            "LCA": "Saint Lucia",
            "VIN": "Saint Vincent and the Grenadines",
            "SAM": "Samoa",
            "SMR": "San Marino",
            "STP": "São Tomé and Príncipe",
            "KSA": "Saudi Arabia",
            "SEN": "Senegal",
            "SRB": "Serbia",
            "SEY": "Seychelles",
            "SLE": "Sierra Leone",
            "SGP": "Singapore",
            "SVK": "Slovakia",
            "SLO": "Slovenia",
            "SOL": "Solomon Islands",
            "SOM": "Somalia",
            "RSA": "South Africa",
            "SSD": "South Sudan",
            "ESP": "Spain",
            "SRI": "Sri Lanka",
            "SUD": "Sudan",
            "SUR": "Suriname",
            "SWZ": "Eswatini",
            "SWE": "Sweden",
            "SUI": "Switzerland",
            "SYR": "Syria",
            "TPE": "Chinese Taipei",
            "TJK": "Tajikistan",
            "TAN": "Tanzania",
            "THA": "Thailand",
            "TLS": "Timor-Leste",
            "TOG": "Togo",
            "TON": "Tonga",
            "TTO": "Trinidad and Tobago",
            "TUN": "Tunisia",
            "TUR": "Turkey",
            "TKM": "Turkmenistan",
            "TUV": "Tuvalu",
            "UGA": "Uganda",
            "UKR": "Ukraine",
            "UAE": "United Arab Emirates",
            "ENG": "England",
            "SCO": "Scotland",
            "WLS": "Wales",
            "USA": "United States",
            "URU": "Uruguay",
            "UZB": "Uzbekistan",
            "VAN": "Vanuatu",
            "VEN": "Venezuela",
            "VIE": "Vietnam",
            "ISV": "US Virgin Islands",
            "YEM": "Yemen",
            "ZAM": "Zambia",
            "ZIM": "Zimbabwe"
        ]
        
        return federationMap[code.uppercased()] ?? code
    }
    
    /// Converts FIDE federation code to country flag emoji
    func federationCodeToFlag(_ code: String) -> String? {
        // Map FIDE codes to ISO country codes for flag emoji
        let federationToISOMap: [String: String] = [
            "AFG": "AF", "ALB": "AL", "ALG": "DZ", "AND": "AD", "ANG": "AO",
            "ANT": "AG", "ARG": "AR", "ARM": "AM", "ARU": "AW", "AUS": "AU",
            "AUT": "AT", "AZE": "AZ", "BAH": "BS", "BRN": "BH", "BAN": "BD",
            "BAR": "BB", "BLR": "BY", "BEL": "BE", "BIZ": "BZ", "BEN": "BJ",
            "BER": "BM", "BHU": "BT", "BOL": "BO", "BIH": "BA", "BOT": "BW",
            "BRA": "BR", "IVB": "VG", "BRU": "BN", "BUL": "BG", "BUR": "BF",
            "BDI": "BI", "CAM": "KH", "CMR": "CM", "CAN": "CA", "CPV": "CV",
            "CAY": "KY", "CAF": "CF", "CHA": "TD", "CHI": "CL", "CHN": "CN",
            "COL": "CO", "COM": "KM", "COD": "CD", "CGO": "CG", "CRC": "CR",
            "CIV": "CI", "CRO": "HR", "CUB": "CU", "CYP": "CY", "CZE": "CZ",
            "DEN": "DK", "DJI": "DJ", "DMA": "DM", "DOM": "DO", "ECU": "EC",
            "EGY": "EG", "ESA": "SV", "ENG": "GB", "GEQ": "GQ", "ERI": "ER",
            "EST": "EE", "ETH": "ET", "FAI": "FO", "FIJ": "FJ", "FIN": "FI",
            "FRA": "FR", "GAB": "GA", "GAM": "GM", "GEO": "GE", "GER": "DE",
            "GHA": "GH", "GRE": "GR", "GRN": "GD", "GUA": "GT", "GUI": "GN",
            "GBS": "GW", "GUY": "GY", "HAI": "HT", "HON": "HN", "HKG": "HK",
            "HUN": "HU", "ISL": "IS", "IND": "IN", "INA": "ID", "IRI": "IR",
            "IRQ": "IQ", "IRL": "IE", "ISR": "IL", "ITA": "IT", "JAM": "JM",
            "JPN": "JP", "JOR": "JO", "KAZ": "KZ", "KEN": "KE", "KIR": "KI",
            "KOS": "XK", "KUW": "KW", "KGZ": "KG", "LAO": "LA", "LAT": "LV",
            "LIB": "LB", "LES": "LS", "LBR": "LR", "LBA": "LY", "LIE": "LI",
            "LTU": "LT", "LUX": "LU", "MAC": "MO", "MKD": "MK", "MAD": "MG",
            "MAW": "MW", "MAS": "MY", "MDV": "MV", "MLI": "ML", "MLT": "MT",
            "MHL": "MH", "MTN": "MR", "MRI": "MU", "MEX": "MX", "FSM": "FM",
            "MDA": "MD", "MON": "MC", "MGL": "MN", "MNE": "ME", "MAR": "MA",
            "MOZ": "MZ", "MYA": "MM", "NAM": "NA", "NRU": "NR", "NEP": "NP",
            "NED": "NL", "NZL": "NZ", "NCA": "NI", "NIG": "NE", "NGR": "NG",
            "NOR": "NO", "OMA": "OM", "PAK": "PK", "PLW": "PW", "PLE": "PS",
            "PAN": "PA", "PNG": "PG", "PAR": "PY", "PER": "PE", "PHI": "PH",
            "POL": "PL", "POR": "PT", "PUR": "PR", "QAT": "QA", "ROU": "RO",
            "RUS": "RU", "RWA": "RW", "SKN": "KN", "LCA": "LC", "VIN": "VC",
            "SAM": "WS", "SMR": "SM", "STP": "ST", "KSA": "SA", "SCO": "GB",
            "SEN": "SN", "SRB": "RS", "SEY": "SC", "SLE": "SL", "SGP": "SG",
            "SVK": "SK", "SLO": "SI", "SOL": "SB", "SOM": "SO", "RSA": "ZA",
            "KOR": "KR", "SSD": "SS", "ESP": "ES", "SRI": "LK", "SUD": "SD",
            "SUR": "SR", "SWZ": "SZ", "SWE": "SE", "SUI": "CH", "SYR": "SY",
            "TPE": "TW", "TJK": "TJ", "TAN": "TZ", "THA": "TH", "TLS": "TL",
            "TOG": "TG", "TON": "TO", "TTO": "TT", "TUN": "TN", "TUR": "TR",
            "TKM": "TM", "TUV": "TV", "UGA": "UG", "UKR": "UA", "UAE": "AE",
            "USA": "US", "URU": "UY", "UZB": "UZ", "VAN": "VU", "VEN": "VE",
            "VIE": "VN", "ISV": "VI", "WLS": "GB", "YEM": "YE", "ZAM": "ZM",
            "ZIM": "ZW"
        ]
        
        guard let isoCode = federationToISOMap[code.uppercased()] else {
            return nil
        }
        
        return isoCodeToFlag(isoCode)
    }
    
    /// Converts ISO country code to flag emoji
    private func isoCodeToFlag(_ isoCode: String) -> String? {
        guard isoCode.count == 2 else { return nil }
        
        // Unicode flag emoji base: Regional Indicator Symbol Letter A = U+1F1E6
        let base: UInt32 = 0x1F1E6
        var flag = ""
        
        for char in isoCode.uppercased() {
            guard let scalarValue = char.unicodeScalars.first?.value else { return nil }
            let letterIndex = scalarValue - UnicodeScalar("A").value
            let flagScalar = UnicodeScalar(base + letterIndex)!
            flag += String(flagScalar)
        }
        
        return flag
    }
}

// MARK: - Persistence
extension FIDEService {

    /// File URL for FIDE cache persistence
    private var cacheFileURL: URL {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        return documentsPath.appendingPathComponent("FIDECache.json")
    }

    /// Loads FIDE cache from disk
    private func loadCacheFromDisk() {
        do {
            let data = try Data(contentsOf: cacheFileURL)
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            fideDataCache = try decoder.decode([String: FIDEPlayerData].self, from: data)

            // Clean up expired data on load
            cleanupExpiredFIDEData()

            #if DEBUG
            print("✅ Loaded \(fideDataCache.count) FIDE cache entries from disk")
            #endif
        } catch {
            // File doesn't exist or is corrupted, start with empty cache
            fideDataCache = [:]
            #if DEBUG
            print("ℹ️ Starting with empty FIDE cache: \(error)")
            #endif
        }
    }

    /// Saves FIDE cache to disk
    private func saveCacheToDisk() {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(fideDataCache)
            try data.write(to: cacheFileURL)

            #if DEBUG
            print("✅ Saved \(fideDataCache.count) FIDE cache entries to disk")
            #endif
        } catch {
            #if DEBUG
            print("❌ Failed to save FIDE cache to disk: \(error)")
            #endif
        }
    }
}
