//
//  FileManager.swift
//  MacChessBase
//
//  Created on 2025/7/1.
//

import Foundation
import UniformTypeIdentifiers
import AppKit
import ChessKit

/// File management class responsible for reading and saving chess game files
@MainActor
class ChessFileManager: ObservableObject {
    
    // MARK: - Error Types
    enum FileError: Error, LocalizedError, Equatable {
        case invalidFileType
        case readingFailed(String)
        case writingFailed(String)
        case invalidPGN
        case invalidFEN
        case emptyContent
        
        var errorDescription: String? {
            switch self {
            case .invalidFileType:
                return "Invalid file type. Only PGN and FEN files are supported."
            case .readingFailed(let details):
                return "Failed to read file: \(details)"
            case .writingFailed(let details):
                return "Failed to write file: \(details)"
            case .invalidPGN:
                return "Invalid PGN format."
            case .invalidFEN:
                return "Invalid FEN format."
            case .emptyContent:
                return "File content is empty."
            }
        }
    }
    
    // MARK: - File Type Support
    
    /// Supported file types for reading and writing
    enum SupportedFileType: CaseIterable {
        case pgn
        case fen
        
        var utType: UTType {
            switch self {
            case .pgn:
                return UTType(filenameExtension: "pgn") ?? .text
            case .fen:
                return UTType(filenameExtension: "fen") ?? .text
            }
        }
        
        var fileExtension: String {
            switch self {
            case .pgn:
                return "pgn"
            case .fen:
                return "fen"
            }
        }
        
        var description: String {
            switch self {
            case .pgn:
                return "Portable Game Notation"
            case .fen:
                return "Forsyth-Edwards Notation"
            }
        }
    }
    
    // MARK: - Reading Methods
    
    /// Reads a chess game from a file URL
    /// - Parameter url: The file URL to read from
    /// - Returns: A Game object parsed from the file
    /// - Throws: FileError if reading or parsing fails
    func readGame(from url: URL) throws -> Game {
        // Validate file type
        guard isValidFileType(url: url) else {
            throw FileError.invalidFileType
        }
        
        // Read file content
        let content: String
        do {
            content = try String(contentsOf: url, encoding: .utf8)
        } catch {
            throw FileError.readingFailed(error.localizedDescription)
        }
        
        // Validate content is not empty
        guard !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw FileError.emptyContent
        }
        
        // Determine file type and parse accordingly
        let fileExtension = url.pathExtension.lowercased()
        
        if fileExtension == "fen" {
            // Parse FEN content
            let fenString = content.trimmingCharacters(in: .whitespacesAndNewlines)
            guard let position = FENParser.parse(fen: fenString) else {
                throw FileError.invalidFEN
            }
            
            // Create a new game with the FEN position as starting position
            var game = Game(startingWith: position)
            
            // If the position is not the standard starting position, set FEN and SetUp tags
            if position != .standard {
                game.tags.fen = fenString
                game.tags.setUp = "1"
            }
            
            return game
        } else {
            // Parse PGN content (ChessKit handles FEN tags automatically)
            guard let game = Game(pgn: content) else {
                throw FileError.invalidPGN
            }
            
            return game
        }
    }
    
    /// Reads a PGN string from a file URL
    /// - Parameter url: The file URL to read from
    /// - Returns: Raw PGN content as string
    /// - Throws: FileError if reading fails
    func readPGNContent(from url: URL) throws -> String {
        // Validate file type
        guard isValidFileType(url: url) else {
            throw FileError.invalidFileType
        }
        
        // Read file content
        do {
            let content = try String(contentsOf: url, encoding: .utf8)
            guard !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
                throw FileError.emptyContent
            }
            return content
        } catch {
            throw FileError.readingFailed(error.localizedDescription)
        }
    }
    
    /// Reads a chess game from clipboard
    /// - Returns: A Game object parsed from clipboard content, or nil if clipboard is empty or invalid
    func readGameFromClipboard() -> Game? {
        let pasteboard = NSPasteboard.general
        guard let content = pasteboard.string(forType: .string),
              !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return nil
        }
        
        let trimmedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Try to parse as FEN first (FEN is a single line format)
        if let position = FENParser.parse(fen: trimmedContent) {
            print(position)
            var game = Game(startingWith: position)
            
            // If the position is not the standard starting position, set FEN and SetUp tags
            if position != .standard {
                game.tags.fen = trimmedContent
                game.tags.setUp = "1"
            }
            
            return game
        }
        
        // Fall back to PGN parsing (ChessKit handles FEN tags automatically)
        return Game(pgn: content)
    }
    
    /// Reads PGN content from clipboard
    /// - Returns: Raw PGN content as string, or nil if clipboard is empty
    func readPGNFromClipboard() -> String? {
        let pasteboard = NSPasteboard.general
        guard let pgnString = pasteboard.string(forType: .string),
              !pgnString.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return nil
        }
        
        return pgnString
    }
    
    // MARK: - Writing Methods
    
    /// Saves a game to a file URL
    /// - Parameters:
    ///   - game: The Game object to save
    ///   - url: The destination file URL
    /// - Throws: FileError if writing fails
    func saveGame(_ game: Game, to url: URL) throws {
        let pgnContent = game.pgn
        try savePGNContent(pgnContent, to: url)
    }
    
    /// Saves raw PGN content to a file URL
    /// - Parameters:
    ///   - content: The PGN content to save
    ///   - url: The destination file URL
    /// - Throws: FileError if writing fails
    func savePGNContent(_ content: String, to url: URL) throws {
        do {
            try content.write(to: url, atomically: true, encoding: .utf8)
        } catch {
            throw FileError.writingFailed(error.localizedDescription)
        }
    }
    
    /// Copies a game's PGN to clipboard
    /// - Parameter game: The Game object to copy
    /// - Returns: true if successful, false otherwise
    @discardableResult
    func copyGameToClipboard(_ game: Game) -> Bool {
        return copyPGNToClipboard(game.pgn)
    }
    
    /// Copies PGN content to clipboard
    /// - Parameter content: The PGN content to copy
    /// - Returns: true if successful, false otherwise
    @discardableResult
    func copyPGNToClipboard(_ content: String) -> Bool {
        guard !content.isEmpty else {
            print("PGN content is empty, nothing to copy")
            return false
        }
        
        let pasteboard = NSPasteboard.general
        pasteboard.declareTypes([.string], owner: nil)
        let success = pasteboard.setString(content, forType: .string)
        
        if success {
            print("PGN copied to clipboard successfully")
        } else {
            print("Failed to copy PGN to clipboard")
        }
        
        return success
    }
    
    // MARK: - File Dialog Methods
    
    /// Shows an open file dialog for selecting chess files
    /// - Returns: Selected file URL, or nil if cancelled
    func showOpenDialog() -> URL? {
        let panel = NSOpenPanel()
        panel.allowedContentTypes = [SupportedFileType.pgn.utType, SupportedFileType.fen.utType]
        panel.allowsMultipleSelection = false
        panel.canChooseDirectories = false
        panel.canChooseFiles = true
        panel.title = "Open Chess File"
        panel.message = "Select a PGN or FEN file to open"
        
        return panel.runModal() == .OK ? panel.url : nil
    }
    
    /// Shows a save file dialog for saving PGN files
    /// - Parameter suggestedName: Suggested filename (without extension)
    /// - Returns: Selected file URL, or nil if cancelled
    func showSaveDialog(suggestedName: String = "chess_game") -> URL? {
        let panel = NSSavePanel()
        panel.allowedContentTypes = [SupportedFileType.pgn.utType]
        panel.title = "Save PGN File"
        panel.message = "Choose a location to save the PGN file"
        panel.nameFieldStringValue = "\(suggestedName).pgn"
        
        return panel.runModal() == .OK ? panel.url : nil
    }
    
    // MARK: - Utility Methods
    
    /// Validates if a file URL has a supported file type
    /// - Parameter url: The file URL to validate
    /// - Returns: true if the file type is supported
    func isValidFileType(url: URL) -> Bool {
        let fileExtension = url.pathExtension.lowercased()
        return SupportedFileType.allCases.contains { $0.fileExtension == fileExtension }
    }
    
    /// Generates a suggested filename based on game metadata
    /// - Parameter game: The game to generate filename for
    /// - Returns: Suggested filename without extension
    func generateSuggestedFilename(for game: Game) -> String {
        let metadata = game.metadata
        var suggestedName = "chess_game"
        
        // Use player names if available
        if !metadata.white.isEmpty || !metadata.black.isEmpty {
            let white = metadata.white.isEmpty ? "?" : sanitizeFilename(metadata.white)
            let black = metadata.black.isEmpty ? "?" : sanitizeFilename(metadata.black)
            suggestedName = "\(white) - \(black)"
        }
        
        // Add event if available
        if !metadata.event.isEmpty {
            let event = sanitizeFilename(metadata.event)
            suggestedName += "_\(event)"
        }
        
        // Add date if available and not default
        if !metadata.date.isEmpty && metadata.date != "?" {
            let date = metadata.date.replacingOccurrences(of: ".", with: "-")
            suggestedName += "_\(date)"
        }
        
        return suggestedName
    }
    
    /// Sanitizes a string for use in filenames
    /// - Parameter string: The string to sanitize
    /// - Returns: Sanitized string safe for use in filenames
    private func sanitizeFilename(_ string: String) -> String {
        return string
            .replacingOccurrences(of: " ", with: "_")
            .replacingOccurrences(of: "/", with: "-")
            .replacingOccurrences(of: "\\", with: "-")
            .replacingOccurrences(of: ":", with: "-")
            .replacingOccurrences(of: "*", with: "")
            .replacingOccurrences(of: "?", with: "")
            .replacingOccurrences(of: "\"", with: "")
            .replacingOccurrences(of: "<", with: "")
            .replacingOccurrences(of: ">", with: "")
            .replacingOccurrences(of: "|", with: "-")
    }
    
    // MARK: - Quick Actions
    
    /// Loads a game using the open dialog
    /// - Returns: The loaded Game object, or nil if cancelled or failed
    func loadGameWithDialog() -> Game? {
        guard let url = showOpenDialog() else { return nil }
        
        do {
            return try readGame(from: url)
        } catch {
            print("Failed to load game: \(error.localizedDescription)")
            return nil
        }
    }
    
    
    /// Saves a game using the save dialog
    /// - Parameters:
    ///   - game: The game to save (with updated metadata)
    /// - Returns: true if successful, false if cancelled or failed
    @discardableResult
    func saveGameWithDialog(_ game: Game) -> Bool {
        let suggestedName = generateSuggestedFilename(for: game)
        guard let url = showSaveDialog(suggestedName: suggestedName) else { return false }
        
        do {
            try saveGame(game, to: url)
            print("Game saved successfully to: \(url.lastPathComponent)")
            return true
        } catch {
            print("Failed to save game: \(error.localizedDescription)")
            return false
        }
    }
}

// MARK: - Extensions

extension ChessFileManager {
    /// Singleton instance for shared access
    static let shared = ChessFileManager()
}
