//
//  SoundManager.swift
//  MacChessBase
//
//  Created by Assistant on 2025/6/7.
//

import AVFoundation
import ChessKit

/// Manages sound effects for chess moves and interactions
class SoundManager: ObservableObject {
    // MARK: - Singleton
    static let shared = SoundManager()

    // MARK: - Properties
    private var audioPlayers: [String: AVAudioPlayer] = [:]
    
    // MARK: - Sound Types
    enum SoundType: String, CaseIterable {
        case move = "move"        // 普通走子音效
        case capture = "capture" // 吃子音效
        
        var fileName: String {
            switch self {
            case .move: return "move.mp3"
            case .capture: return "capture.mp3"
            }
        }
        
        var volume: Float {
            switch self {
            case .move: return 0.7      // 适中的音量
            case .capture: return 0.8   // 稍大一些的音量
            }
        }
    }
    
    // MARK: - Initialization
    private init() {
        setupAudio()
        preloadSounds()
    }

    // MARK: - Testing Support
    /// Creates a new instance for testing purposes only
    internal init(forTesting: Bool) {
        setupAudio()
        preloadSounds()
    }
    
    // MARK: - Public Methods
    
    /// Plays a sound for a chess move
    func playMoveSound(for move: Move) {
        guard let metaMove = move.metaMove else {
            return
        }
        let soundType: SoundType = metaMove.result.isCapture ? .capture : .move
        playSound(soundType)
    }
    
    /// Plays a generic move sound (for when no Move object is available yet)
    func playGenericMoveSound() {
        playSound(.move)
    }
    
    // MARK: - Private Methods
    
    private func setupAudio() {
        // macOS doesn't require AVAudioSession setup
        // Audio will work directly with AVAudioPlayer
    }
    
    /// Preloads all sound files for better performance
    private func preloadSounds() {
        for soundType in SoundType.allCases {
            loadSound(soundType)
        }
    }
    
    /// Loads a specific sound file
    private func loadSound(_ soundType: SoundType) {
        guard let url = Bundle.main.url(forResource: soundType.rawValue, withExtension: "mp3") else {
            print("Warning: Could not find \(soundType.fileName) in app bundle")
            return
        }
        
        do {
            let player = try AVAudioPlayer(contentsOf: url)
            player.volume = soundType.volume
            player.prepareToPlay()
            audioPlayers[soundType.rawValue] = player
        } catch {
            print("Error loading sound \(soundType.fileName): \(error)")
        }
    }
    
    /// Plays a specific sound type
    private func playSound(_ soundType: SoundType) {
        guard let player = audioPlayers[soundType.rawValue] else {
            print("Sound player not found for \(soundType.rawValue), attempting to load...")
            loadSound(soundType)
            return
        }
        
        // Reset player to beginning and play
        player.stop()
        player.currentTime = 0
        player.volume = soundType.volume
        player.play()
    }
}

// MARK: - Move Extension for Sound Support
extension MetaMove.Result {
    var isCapture: Bool {
        if case .capture = self {
            return true
        }
        return false
    }
}
