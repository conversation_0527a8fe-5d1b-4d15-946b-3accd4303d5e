//
//  SubscriptionService.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/7/27.
//

import Foundation
import Combine

enum SubscriptionTier {
    case free
    case pro
    case expert
}

class SubscriptionService: ObservableObject {
    static let shared = SubscriptionService()
    
    @Published var currentTier: SubscriptionTier = .free
    
    private init() {}
    
    func hasAccess(to feature: SubscriptionTier) -> Bo<PERSON> {
        switch currentTier {
        case .free:
            return feature == .free
        case .pro:
            return feature == .free || feature == .pro
        case .expert:
            return true
        }
    }
    
    func upgradeSubscription(to tier: SubscriptionTier) {
        // In a real app, this would handle payment processing
        // For now, we'll just simulate the upgrade
        currentTier = tier
    }
    
    func showSubscriptionAlert(for feature: String) -> (title: String, message: String) {
        return (
            title: "Pro Feature",
            message: "\(feature) is a Pro feature. Upgrade your subscription to access this functionality."
        )
    }
}