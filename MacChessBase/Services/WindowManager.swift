//
//  WindowManager.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/7/27.
//

import Foundation
import AppKit

class WindowManager: NSObject, ObservableObject {
    static let shared = WindowManager()
    
    private var childWindows: [NSWindow] = []
    private weak var mainWindow: NSWindow?
    
    private override init() {
        super.init()
    }
    
    func setMainWindow(_ window: NSWindow) {
        mainWindow = window
        // 监听主窗口关闭事件
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(mainWindowWillClose),
            name: NSWindow.willCloseNotification,
            object: window
        )
    }
    
    func addChildWindow(_ window: NSWindow) {
        childWindows.append(window)
        
        // 监听子窗口关闭事件以清理引用
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(childWindowWillClose(_:)),
            name: NSWindow.willCloseNotification,
            object: window
        )
    }
    
    @objc private func mainWindowWillClose() {
        // 先移除所有子窗口的监听器，避免在关闭过程中触发回调
        let windowsToClose = childWindows
        childWindows.removeAll()
        
        for window in windowsToClose {
            NotificationCenter.default.removeObserver(
                self,
                name: NSWindow.willCloseNotification,
                object: window
            )
            window.close()
        }
        
        // 移除主窗口监听器
        if let mainWindow = mainWindow {
            NotificationCenter.default.removeObserver(
                self,
                name: NSWindow.willCloseNotification,
                object: mainWindow
            )
        }
    }
    
    @objc private func childWindowWillClose(_ notification: Notification) {
        if let window = notification.object as? NSWindow {
            // 从数组中移除已关闭的窗口
            childWindows.removeAll { $0 === window }
            
            // 移除对该窗口的监听
            NotificationCenter.default.removeObserver(
                self,
                name: NSWindow.willCloseNotification,
                object: window
            )
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}