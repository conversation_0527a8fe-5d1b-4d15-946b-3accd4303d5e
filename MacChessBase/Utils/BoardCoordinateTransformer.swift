//
//  BoardCoordinateTransformer.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/7/6.
//

import Foundation
import ChessKit
import CoreGraphics

/// A utility struct that handles coordinate transformations for board flipping
struct BoardCoordinateTransformer {
    let isFlipped: Bool
    
    /// Converts logical square to visual position on the board
    func squareToPosition(_ square: Square, squareSize: CGFloat) -> CGPoint {
        let file = square.file.number - 1 // Convert a=1 to 0, b=2 to 1, etc.
        let rank = square.rank.value - 1  // Convert 1 to 0, 2 to 1, etc.
        
        let visualFile = isFlipped ? (7 - file) : file
        let visualRank = isFlipped ? rank : (7 - rank)
        
        return CGPoint(
            x: CGFloat(visualFile) * squareSize,
            y: CGFloat(visualRank) * squareSize
        )
    }
    
    /// Converts visual position to logical square
    func positionToSquare(_ position: CGPoint, squareSize: CGFloat) -> Square? {
        if position.x < 0 || position.y < 0 {
            return nil
        }
        
        let visualFile = Int(position.x / squareSize)
        let visualRank = Int(position.y / squareSize)
        
        guard visualFile >= 0 && visualFile < 8 && visualRank >= 0 && visualRank < 8 else {
            return nil
        }
        
        let logicalFile = isFlipped ? (7 - visualFile) : visualFile
        let logicalRank = isFlipped ? visualRank : (7 - visualRank)
        
        let fileNames = ["a", "b", "c", "d", "e", "f", "g", "h"]
        return Square("\(fileNames[logicalFile])\(logicalRank + 1)")
    }
    
    /// Gets the file labels in visual order (for display)
    var fileLabels: [String] {
        return isFlipped ? ["h", "g", "f", "e", "d", "c", "b", "a"] : ["a", "b", "c", "d", "e", "f", "g", "h"]
    }
    
    /// Gets the rank values in visual order (for display)
    var rankLabels: [Int] {
        return isFlipped ? Array(1...8) : Array((1...8).reversed())
    }
}
