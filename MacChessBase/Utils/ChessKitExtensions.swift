//
//  ChessKitExtensions.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/6/1.
//

import ChessKit
import Foundation

// MARK: - Square Extensions
extension Square {
    /// Creates a square from file and rank indices (0-based)
    static func from(fileIndex: Int, rankIndex: Int) -> Square? {
        let fileChar = Character(UnicodeScalar(97 + fileIndex)!) // 'a' + index
        let rankNum = rankIndex + 1
        return Square("\(fileChar)\(rankNum)")
    }
    
    /// Returns the array index for this square (0-63)
    var arrayIndex: Int {
        return (rank.value - 1) * 8 + (file.number - 1)
    }
    
    /// Returns all squares on the board
    static var allSquares: [Square] {
        var squares: [Square] = []
        for rank in 1...8 {
            for file in 1...8 {
                if let square = Square.from(fileIndex: file - 1, rankIndex: rank - 1) {
                    squares.append(square)
                }
            }
        }
        return squares
    }
}

// MARK: - Piece Extensions
extension Piece {
    /// Returns the asset name for this piece
    var imageName: String {
        let colorPrefix = color == .white ? "w" : "b"
        let pieceSymbol: String
        
        switch kind {
        case .pawn: pieceSymbol = "P"
        case .knight: pieceSymbol = "N"
        case .bishop: pieceSymbol = "B"
        case .rook: pieceSymbol = "R"
        case .queen: pieceSymbol = "Q"
        case .king: pieceSymbol = "K"
        }
        
        return "\(colorPrefix)\(pieceSymbol)"
    }
}

// MARK: - Position Extensions
extension Position {
    /// Returns all pieces of a specific color
    func pieces(of color: Piece.Color) -> [Piece] {
        return pieces.filter { $0.color == color }
    }
    
    /// Returns the king of a specific color
    func king(of color: Piece.Color) -> Piece? {
        return pieces.first { $0.color == color && $0.kind == .king }
    }
    
    /// Checks if the king of the specified color is in check
    func isInCheck(color: Piece.Color) -> Bool {
        guard let king = king(of: color) else { return false }
        
        // Check if any opponent piece can attack the king's square
        let opponentPieces = pieces(of: color.opposite)
        
        // This is a simplified check - in a real implementation,
        // you would use the Board's attack calculation methods
        return false // Placeholder - ChessKit handles this internally
    }
}

// MARK: - Move Extensions
extension Move {
    /// Returns a user-friendly description of the move
    var description: String {
        self.pgnDiscription
    }
}

// MARK: - Piece.Kind Extensions
extension Piece.Kind {
    var description: String {
        switch self {
        case .pawn: return "Pawn"
        case .knight: return "Knight"
        case .bishop: return "Bishop"
        case .rook: return "Rook"
        case .queen: return "Queen"
        case .king: return "King"
        }
    }
    
    /// Returns the Unicode symbol for this piece kind
    var symbol: String {
        switch self {
        case .pawn: return "♟"
        case .knight: return "♞"
        case .bishop: return "♝"
        case .rook: return "♜"
        case .queen: return "♛"
        case .king: return "♚"
        }
    }
}

// MARK: - Piece.Color Extensions
extension Piece.Color {
    var description: String {
        switch self {
        case .white: return "White"
        case .black: return "Black"
        }
    }
    
    /// Returns the opposite color
    var opposite: Piece.Color {
        return self == .white ? .black : .white
    }
}
