//
//  ChessFileOperations.swift
//  MacChessBase
//
//  Handles all file-related operations for chess games
//

import Foundation
import SwiftUI
import ChessKit

/// Manages file operations for chess games including loading, saving, and clipboard operations
@MainActor
final class ChessFileOperations: ObservableObject {
    
    // MARK: - Dependencies
    private let fileManager = ChessFileManager.shared
    
    // MARK: - Published Properties for UI State
    @Published var showImportErrorAlert = false
    @Published var showPositionEditor = false
    
    // MARK: - Callbacks
    /// Called when UI state needs to be reset after file operations
    var onStateReset: (() -> Void)?
    /// Called when cache needs to be invalidated
    var onCacheInvalidate: (() -> Void)?
    
    // MARK: - Initialization
    init() {}
    
    // MARK: - Game Loading Operations
    
    /// Loads a game from PGN string
    /// - Parameters:
    ///   - pgn: The PGN string to load
    ///   - session: The game session to load into
    func loadGame(from pgn: String, into session: GameSession) {
        session.loadGame(from: pgn)
        resetUIState()
    }
    
    /// Loads a game from PGN file
    /// - Parameters:
    ///   - url: The file URL to load from
    ///   - session: The game session to load into
    func loadGame(from url: URL, into session: GameSession) {
        session.currentFilePath = url
        session.loadGame(from: url)
        resetUIState()
    }
    
    /// Loads a game from a Game object
    /// - Parameters:
    ///   - gameObject: The Game object to load
    ///   - session: The game session to load into
    func loadGameFromObject(_ gameObject: Game, into session: GameSession) {
        session.loadGameFromObject(gameObject)
        resetUIState()
    }
    
    /// Loads a position from FEN string
    /// - Parameters:
    ///   - fen: The FEN string to load
    ///   - session: The game session to load into
    func loadPosition(from fen: String, into session: GameSession) {
        session.loadPosition(from: fen)
        resetUIState()
    }
    
    /// Loads game from clipboard
    /// - Parameter session: The game session to load into
    /// - Returns: True if successful, false if failed
    func loadGameFromClipboard(into session: GameSession) -> Bool {
        if session.loadGameFromClipboard() {
            // Successfully loaded from clipboard with undo support
            resetUIState()
            return true
        } else {
            // Failed to load - show error
            showImportErrorAlert = true
            return false
        }
    }
    
    // MARK: - Game Saving Operations
    
    /// Saves the current game to the current file path
    /// - Parameter session: The game session to save
    /// - Throws: GameSaveError if no current file path is set
    func saveGame(from session: GameSession) throws {
        guard let filePath = session.currentFilePath else {
            throw GameSaveError.noCurrentFile
        }
        try fileManager.saveGame(session.game, to: filePath)
    }
    
    /// Saves the current game to a new file path
    /// - Parameters:
    ///   - url: The file URL to save to
    ///   - session: The game session to save
    func saveGame(to url: URL, from session: GameSession) throws {
        session.currentFilePath = url
        try fileManager.saveGame(session.game, to: url)
    }
    
    // MARK: - Position Editor Operations
    
    /// Opens the position editor
    func openPositionEditor() {
        showPositionEditor = true
    }
    
    /// Handles setting a new position from the position editor
    /// - Parameters:
    ///   - position: The new position to set
    ///   - shouldFlip: Whether to flip the board
    ///   - session: The game session to update
    func setPosition(_ position: Position, shouldFlip: Bool = false, in session: GameSession) {
        if session.setPosition(position, shouldFlip: shouldFlip) {
            // Successfully set position with undo support
            resetUIState()
        }
        showPositionEditor = false
    }
    
    // MARK: - Utility Methods
    
    /// Resets UI state after file operations
    private func resetUIState() {
        // Call the callback to reset UI state in the main ViewModel
        onStateReset?()
        onCacheInvalidate?()
    }
}

// MARK: - Error Types
enum GameSaveError: Error {
    case noCurrentFile
}
