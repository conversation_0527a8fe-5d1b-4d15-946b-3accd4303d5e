//
//  MoveDisplayManager.swift
//  MacChessBase
//
//  Created by Augment Agent on 2025-08-08.
//

import Foundation
import SwiftUI
import Combine
import ChessKit

// MARK: - MoveDisplayItem
/// Represents a single item in the move notation display
struct MoveDisplayItem: Identifiable {
    let id: String
    let pgnElement: MoveTree.PGNElement?
    let move: Move?
    let index: MoveTree.MoveIndex?
    let moveNumber: Int?
    let isWhiteMove: Bool?

    // Create stable ID based on content instead of random UUID
    init(pgnElement: MoveTree.PGNElement, sequenceIndex: Int) {
        self.pgnElement = pgnElement
        self.move = nil
        self.index = nil
        self.moveNumber = nil
        self.isWhiteMove = nil

        // Generate stable ID based on content and position
        switch pgnElement {
        case .whiteNumber(let number):
            self.id = "white-\(number)-\(sequenceIndex)"
        case .blackNumber(let number):
            self.id = "black-\(number)-\(sequenceIndex)"
        case .move(_, let index):
            self.id = "move-\(index)-\(sequenceIndex)"
        case .variationStart:
            self.id = "var-start-\(sequenceIndex)"
        case .variationEnd:
            self.id = "var-end-\(sequenceIndex)"
        }
    }
    
    // New initializer for moves with embedded numbers (numbers can be nil for black moves in main line)
    init(move: Move, index: MoveTree.MoveIndex, moveNumber: Int?, isWhiteMove: Bool?, sequenceIndex: Int) {
        self.pgnElement = .move(move, index)
        self.move = move
        self.index = index
        self.moveNumber = moveNumber
        self.isWhiteMove = isWhiteMove
        self.id = "move-\(index)-\(sequenceIndex)"
    }

    var displayText: String {
        if let move = self.move {
            guard let metaMove = move.metaMove else {
                return ""
            }
            if let moveNumber = self.moveNumber, let isWhiteMove = self.isWhiteMove {
                // Format with embedded number
                if isWhiteMove {
                    return "\(moveNumber).\(metaMove.displayDescription)"
                } else {
                    return "\(moveNumber)…\(metaMove.displayDescription)"
                }
            } else {
                // No number to display, just the move (common for black moves in main line)
                return "\(metaMove.displayDescription)"
            }
        }
        
        // Fallback to original logic for non-move elements
        guard let pgnElement = self.pgnElement else { return "" }
        switch pgnElement {
        case .whiteNumber(let number):
            return "\(number)."
        case .blackNumber(let number):
            return "\(number)…"
        case .move(let move, _):
            return move.metaMove?.displayDescription ?? ""
        case .variationStart:
            return "("
        case .variationEnd:
            return ")"
        }
    }

    var isMove: Bool {
        return move != nil || (pgnElement != nil && { 
            if case .move = pgnElement! { return true }
            return false
        }())
    }
}

// MARK: - MoveDisplayManager Protocol
@MainActor
protocol MoveDisplayManagerProtocol: ObservableObject {
    var cachedMoves: [MoveDisplayItem] { get }
    var hasOnlyCurrentMoveChanged: Bool { get }
    
    func invalidateCache()
    func updateSession(_ session: GameSession)
    func buildGamePreview(maxMovesToShow: Int) -> String
}

// MARK: - MoveDisplayManager Implementation
@MainActor
final class MoveDisplayManager: MoveDisplayManagerProtocol {
    // MARK: - Published Properties
    @Published private var _cachedMoves: [MoveDisplayItem] = []
    
    // MARK: - Private Properties
    private weak var session: GameSession?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Performance Cache
    private var _cacheValid = false
    private var _lastCacheInvalidationTime = Date.distantPast
    private let _cacheInvalidationThreshold: TimeInterval = 0.1 // 100ms throttle
    private var _lastCurrentMoveIndex: MoveTree.MoveIndex? // Track the last current move for comparison
    
    // MARK: - Computed Properties
    var cachedMoves: [MoveDisplayItem] {
        if !_cacheValid {
            _cachedMoves = buildAllMovesWithVariations()
            _cacheValid = true
        }
        return _cachedMoves
    }
    
    /// Returns true if only the current move index has changed, not the game content
    var hasOnlyCurrentMoveChanged: Bool {
        defer { _lastCurrentMoveIndex = session?.currentMoveIndex }
        return _cacheValid && _lastCurrentMoveIndex != session?.currentMoveIndex
    }
    
    // MARK: - Initialization
    init() {}
    
    // MARK: - Public Methods
    func updateSession(_ session: GameSession) {
        // Cancel previous subscriptions
        cancellables.removeAll()
        
        self.session = session
        
        // Subscribe to session changes
        session.objectWillChange
            .sink { [weak self] _ in
                self?.throttledCacheInvalidation()
            }
            .store(in: &cancellables)
        
        // Initial cache invalidation
        invalidateCache()
    }
    
    func invalidateCache() {
        _cacheValid = false
        _lastCurrentMoveIndex = nil // Reset current move tracking when content changes
    }
    
    #if DEBUG
    internal func invalidateCacheTest() {
        invalidateCache()
    }
    #endif
    
    // MARK: - Private Methods
    
    /// Throttled cache invalidation to prevent excessive recalculation during rapid moves
    private func throttledCacheInvalidation() {
        let now = Date()
        if now.timeIntervalSince(_lastCacheInvalidationTime) >= _cacheInvalidationThreshold {
            invalidateCache()
            _lastCacheInvalidationTime = now
        }
    }
}

// MARK: - Move Building Logic
extension MoveDisplayManager {
    /// Builds all moves in the game including variations, filtering out numbers and adding them to moves
    private func buildAllMovesWithVariations() -> [MoveDisplayItem] {
        guard let session = self.session else { return [] }
        
        // Use ChessKit's pgnRepresentation to get the proper tree structure
        let pgnElements = session.game.moves.pgnRepresentation
        
        var result: [MoveDisplayItem] = []
        var sequenceIndex = 0
        // Track move numbers for each variation level
        var moveNumber = session.game.moves.initialNumber
        var whiteToMove = session.game.moves.initialSideToMove == .white
        var isInVariation = false
        var variationDepth = 0
        var justExitedVariation = false
        let StartingWithBlackToMove = !whiteToMove
        
        for pgnElement in pgnElements {
            switch pgnElement {
            case .whiteNumber(let number):
                moveNumber = number
                whiteToMove = true
            case .blackNumber(let number):
                moveNumber = number
                whiteToMove = false
            case .move(let move, let index):
                // Determine if we should show the number for this move
                let shouldShowNumber: Bool
                if whiteToMove {
                    // White moves always show number
                    shouldShowNumber = true
                } else {
                    let initialBlackToMove = StartingWithBlackToMove && index == session.game.moves.firstIndex
                    // Black moves show number in variations OR when just exited variation back to main line OR when the starting position is black to move
                    shouldShowNumber = isInVariation || justExitedVariation || initialBlackToMove
                }
                
                // Create a move item with embedded number (or nil if not showing)
                let moveWithNumber = MoveDisplayItem(
                    move: move,
                    index: index,
                    moveNumber: shouldShowNumber ? moveNumber: nil,
                    isWhiteMove: whiteToMove,
                    sequenceIndex: sequenceIndex
                )
                result.append(moveWithNumber)
                
                // Reset the justExitedVariation flag after processing a move
                justExitedVariation = false
                sequenceIndex += 1
                
            case .variationStart:
                let item = MoveDisplayItem(pgnElement: pgnElement, sequenceIndex: sequenceIndex)
                result.append(item)
                variationDepth += 1
                isInVariation = true
                sequenceIndex += 1
                
            case .variationEnd:
                let item = MoveDisplayItem(pgnElement: pgnElement, sequenceIndex: sequenceIndex)
                result.append(item)
                variationDepth -= 1
                
                // When exiting a variation back to main line (variationDepth == 0), 
                // mark that we just exited so the next move shows number if it's black
                if variationDepth == 0 {
                    isInVariation = false
                    justExitedVariation = true
                } else {
                    isInVariation = true
                }
                
                sequenceIndex += 1
            }
        }
        
        return result
    }
}

// MARK: - Game Preview Logic
extension MoveDisplayManager {
    /// Builds a preview string of the first few moves for display in game lists
    func buildGamePreview(maxMovesToShow: Int = 6) -> String {
        guard let session = self.session else { return "" }
        
        let pgnElements = session.game.moves.pgnRepresentation
        var result = ""
        var moveCount = 0
        
        for element in pgnElements {
            if moveCount >= maxMovesToShow { break }
            
            switch element {
            case .whiteNumber(let number):
                if !result.isEmpty { result += " " }
                result += "\(number)."
                
            case .blackNumber(let number):
                if moveCount != 0 {
                    break
                }
                if !result.isEmpty { result += " " }
                result += "\(number)…"
                
            case .move(let move, _):
                if !result.isEmpty && !result.hasSuffix(".") && !result.hasSuffix("…") {
                    result += " "
                }
                result += move.metaMove!.displayDescription
                moveCount += 1
                
            case .variationStart, .variationEnd:
                // Skip variation markers for this display
                break
            }
        }
        
        return result
    }
}
