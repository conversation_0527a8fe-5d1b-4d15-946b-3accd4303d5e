//
//  ChessGameView.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/6/1.
//

import SwiftUI
import ChessKit
import AppKit

/// A SwiftUI view that displays an interactive chess board
struct ChessGameView: View {
    @ObservedObject var viewModel: ChessGameViewModel
    
    // MARK: - Constants
    private let minBoardSize: CGFloat = 320
    private let maxBoardSize: CGFloat = 800
    
    var body: some View {
        GeometryReader { geometry in
            HSplitView(
                initialRatio: 0.6, // 60% for chess board, 40% for notation/engine
                minLeftWidth: 360,  // Minimum width for chess board area (320 + 40 padding)
                minRightWidth: 300, // Minimum width for notation/engine area
                leftContent: {
                    // Left side - Chess board and controls with perfect centering
                    GeometryReader { leftGeometry in
                        VStack(spacing: 0) {
                            Spacer() // Top spacing
                            
                            // Chess clock and board area
                            VStack(spacing: 0) {
                                // Top clock - dynamically determined based on flip state
                                ChessClockView(
                                    viewModel: viewModel,
                                    playerColor: viewModel.session.isBoardFlipped ? .white : .black
                                )
                                .frame(maxWidth: calculateBoardSize(geometry: leftGeometry))
                                
                                // Chess board - perfectly centered between sidebar and divider
                                HStack(spacing: 0) {
                                    Spacer(minLength: 0) // Flexible left margin
                                    ChessBoardView(
                                        viewModel: viewModel,
                                        boardSize: calculateBoardSize(geometry: leftGeometry)
                                    )
                                    .frame(maxWidth: .infinity, alignment: .center)
                                    Spacer(minLength: 0) // Flexible right margin (equal to left)
                                }
                                
                                // Bottom clock - dynamically determined based on flip state
                                ChessClockView(
                                    viewModel: viewModel,
                                    playerColor: viewModel.session.isBoardFlipped ? .black : .white
                                )
                                .frame(maxWidth: calculateBoardSize(geometry: leftGeometry))
                            }
                            
                            Spacer() // Bottom spacing
                        }
                        .padding(.horizontal, 20) // Minimum edge padding
                    }
                },
                rightContent: {
                    // Right side - Split view with notation and engine analysis
                    SplitView(
                    initialRatio: 0.7,
                    minTopHeight: 150,
                    minBottomHeight: 100,
                    topContent: {
                        InteractiveMoveNotationView(viewModel: viewModel)
                        .background(Color(NSColor.controlBackgroundColor))
                        .overlay(
                            // Variation selection popup positioned in notation area
                            GeometryReader { notationGeometry in
                                Group {
                                    if viewModel.navigator.showVariationSelection {
                                        ZStack {
                                            // Semi-transparent background
                                            Color.black.opacity(0.3)
                                                .onTapGesture {
                                                    viewModel.navigator.cancelVariationSelection()
                                                }

                                            // Variation selection view with calculated size
                                            VariationSelectionView(viewModel: viewModel)
                                                .frame(
                                                    width: calculatePopupWidth(for: notationGeometry.size),
                                                    height: calculatePopupHeight(for: notationGeometry.size)
                                                )
                                        }
                                        .transition(.opacity.combined(with: .scale(scale: 0.95)))
                                        .animation(.easeInOut(duration: 0.2), value: viewModel.navigator.showVariationSelection)
                                    }

                                    // Move edit menu
                                    if viewModel.showMoveEditMenu,
                                       let selectedMoveIndex = viewModel.selectedMoveForEdit {
                                        ZStack {
                                            // Semi-transparent background
                                            Color.clear
                                                .onTapGesture {
                                                    viewModel.hideEditMenu()
                                                }

                                            // Edit menu positioned at the specified location
                                            MoveEditMenuView(
                                                viewModel: viewModel,
                                                moveIndex: selectedMoveIndex
                                            )
                                            .position(
                                                x: viewModel.editMenuPosition.x + 100,
                                                y: viewModel.editMenuPosition.y + 50
                                            )
                                        }
                                        .transition(.opacity.combined(with: .scale(scale: 0.95)))
                                        .animation(.easeInOut(duration: 0.15), value: viewModel.showMoveEditMenu)
                                    }
                                }
                            }
                        )
                    },
                    bottomContent: {
                        // Replace the VStack with a direct call to EngineAnalysisView
                        EngineAnalysisView(chessGameViewModel: viewModel)
                            .background(Color(NSColor.controlBackgroundColor)) // Optional: keep the background
                    }
                )
                .frame(minWidth: 300)
                .padding(.leading, 20)
                }
            )
            .padding()
        }
        .sheet(isPresented: $viewModel.showPromotionDialog) {
            promotionDialogView
                .onAppear {
                    // Focus the dialog when it appears
                }
        }
        .sheet(isPresented: $viewModel.showVariationCreationDialog) {
            VariationCreationView(viewModel: viewModel)
                .onDisappear {
                    // Ensure navigation is re-enabled when dialog disappears
                    viewModel.navigator.isKeyboardNavigationDisabled = false
                }
        }
        .sheet(isPresented: $viewModel.showPositionEditor) {
            PositionEditorView(
                initialFEN: viewModel.session.board.position.fen
            ) { newPosition, shouldFlip in
                viewModel.setPosition(newPosition, shouldFlip: shouldFlip)
            }
        }
        .alert("Invalid PGN or FEN", isPresented: $viewModel.showImportErrorAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text("The content could not be imported. Please check that it's a valid PGN game or FEN position.")
        }
    }
    
    // MARK: - Helper Methods
    
    /// Calculates the optimal board size for the given geometry
    private func calculateBoardSize(geometry: GeometryProxy) -> CGFloat {
        // Calculate available space more precisely for perfect centering
        let horizontalPadding: CGFloat = 40 // Total horizontal padding (20px on each side)
        let verticalPadding: CGFloat = 120 // Space for controls and spacing
        
        // Available space for the chess board
        let availableWidth = max(0, geometry.size.width - horizontalPadding)
        let availableHeight = max(0, geometry.size.height - verticalPadding)
        
        // Calculate the optimal board size (must be square and within constraints)
        let maxAvailableSize = min(availableWidth, availableHeight)
        return min(maxBoardSize, max(minBoardSize, maxAvailableSize))
    }
    
    // MARK: - Game Controls
    private var gameControlsView: some View {
        HStack {
            Spacer()
            
            Text("Use Chess menu for game controls (⌘N, ⌘O, ⇧⌘V, ⌘S, ⇧⌘S, ⇧⌘C)")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
        }
        .padding(.horizontal)
    }
    
    // MARK: - Computed Properties
    private var promotingPieceColor: Piece.Color {
        guard let startSquare = viewModel.promotionStartSquare else {
            return viewModel.currentPlayer
        }
        
        // Get the piece at the start square from the current position
        if let piece = viewModel.session.board.position.piece(at: startSquare) {
            return piece.color
        }
        
        return viewModel.currentPlayer
    }
    
    private func isLastMoveSquare(_ square: Square) -> Bool {
        guard let lastMetaMove = viewModel.lastMove?.metaMove else { return false }
        return lastMetaMove.start == square || lastMetaMove.end == square
    }
    
    // MARK: - Promotion Dialog
    private var promotionDialogView: some View {
        VStack(spacing: 24) {
            VStack(spacing: 8) {
                Text("Choose Promotion Piece")
                    .font(.headline)
                
                Text("Click a piece or press Q, R, B, or N")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.top)
            
            HStack(spacing: 30) {
                ForEach([Piece.Kind.queen, .rook, .bishop, .knight], id: \.self) { pieceKind in
                    Button(action: {
                        viewModel.completePromotion(to: pieceKind)
                    }) {
                        VStack(spacing: 12) {
                            Image(pieceImageName(for: pieceKind, color: promotingPieceColor))
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 80, height: 80)
                            
                            VStack(spacing: 4) {
                                Text(pieceDisplayName(for: pieceKind))
                                    .font(.system(.body, weight: .medium))
                                    .foregroundColor(.primary)
                                
                                Text("(\(keyboardShortcut(for: pieceKind)))")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .buttonStyle(.plain)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 2)
                    )
                    .scaleEffect(1.0)
                    .onHover { isHovering in
                        withAnimation(.easeInOut(duration: 0.1)) {
                            // Add subtle hover effect if needed
                        }
                    }
                }
            }
            .padding(.horizontal)
            .padding(.bottom)
        }
        .padding(20)
        .frame(minWidth: 600, minHeight: 280)
        .background(Color(.windowBackgroundColor))
        .cornerRadius(16)
        .shadow(radius: 10)
    }
    
    // MARK: - Helper Methods
    private func pieceImageName(for kind: Piece.Kind, color: Piece.Color) -> String {
        let colorPrefix = color == .white ? "w" : "b"
        let pieceSymbol: String
        
        switch kind {
        case .pawn: pieceSymbol = "P"
        case .knight: pieceSymbol = "N"
        case .bishop: pieceSymbol = "B"
        case .rook: pieceSymbol = "R"
        case .queen: pieceSymbol = "Q"
        case .king: pieceSymbol = "K"
        }
        
        return "\(colorPrefix)\(pieceSymbol)"
    }
    
    private func pieceDisplayName(for kind: Piece.Kind) -> String {
        switch kind {
        case .queen: return "Queen"
        case .rook: return "Rook"
        case .bishop: return "Bishop"
        case .knight: return "Knight"
        case .pawn: return "Pawn"
        case .king: return "King"
        }
    }
    
    private func keyboardShortcut(for kind: Piece.Kind) -> String {
        switch kind {
        case .queen: return "Q"
        case .rook: return "R"
        case .bishop: return "B"
        case .knight: return "N"
        case .pawn: return "P"
        case .king: return "K"
        }
    }
    
    /// Converts global coordinates to local board coordinates
    private func convertGlobalToLocal(globalPosition: CGPoint, in geometry: GeometryProxy) -> CGPoint {
        let boardFrame = geometry.frame(in: .global)
        return CGPoint(
            x: globalPosition.x - boardFrame.minX,
            y: globalPosition.y - boardFrame.minY
        )
    }

    /// Calculates the optimal popup width based on available notation area width
    private func calculatePopupWidth(for notationSize: CGSize) -> CGFloat {
        // Account for notation area padding and margins
        let horizontalPadding: CGFloat = 32 // 16px on each side for content padding
        let availableWidth = notationSize.width - horizontalPadding

        // VariationSelectionView constraints: minWidth: maxWidth: 500
        let maxPopupWidth: CGFloat = 500

        // Calculate the maximum width we can use without exceeding bounds
        let maxAllowedWidth = availableWidth * 0.9
        
        return min(maxPopupWidth, maxAllowedWidth)
    }

    /// Calculates the optimal popup height based on available notation area height
    private func calculatePopupHeight(for notationSize: CGSize) -> CGFloat {
        // Account for notation area header and padding
        let headerHeight: CGFloat = 60 // Approximate height of "Game Notation" header and controls
        let verticalPadding: CGFloat = 20 // Top and bottom margins
        let availableHeight = notationSize.height - headerHeight - verticalPadding

        // Calculate popup content height based on number of variations
        let headerContentHeight: CGFloat = 80 // Title and description
        let footerHeight: CGFloat = 60 // Cancel button area
        let dividerHeight: CGFloat = 32 // Two dividers with padding
        let variationRowHeight: CGFloat = 60 // Approximate height per variation row
        let maxScrollHeight: CGFloat = 300 // Maximum scroll area height from VariationSelectionView

        let variationCount = CGFloat(viewModel.navigator.availableVariations.count)
        let variationsHeight = min(maxScrollHeight, variationCount * variationRowHeight)

        let totalContentHeight = headerContentHeight + dividerHeight + variationsHeight + footerHeight

        // Adaptive height calculation for small screens
        let maxAllowedHeight = availableHeight * 0.85
        let minPopupHeight: CGFloat = 200 // Minimum height for usability

        // If calculated height exceeds available space, prioritize scroll area
        if totalContentHeight > maxAllowedHeight {
            let fixedContentHeight = headerContentHeight + dividerHeight + footerHeight
            let availableScrollHeight = max(80, maxAllowedHeight - fixedContentHeight) // Minimum 80px for scroll
            return max(minPopupHeight, fixedContentHeight + availableScrollHeight)
        }

        return max(minPopupHeight, totalContentHeight)
    }
}

// MARK: - Preview
#Preview {
    let session = GameSession()
    ChessGameView(viewModel: ChessGameViewModel(session: session))
        .frame(width: 600, height: 700)
}
