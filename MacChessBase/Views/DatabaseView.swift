//
//  DatabaseView.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/7/27.
//

import SwiftUI

struct DatabaseView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Header
                VStack(spacing: 12) {
                    Image(systemName: "externaldrive.connected.to.line.below.fill")
                        .font(.system(size: 64))
                        .foregroundColor(.blue)
                    
                    Text("Online Database")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Access millions of chess games from online databases")
                        .font(.title3)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // Feature List
                VStack(alignment: .leading, spacing: 16) {
                    FeatureItem(
                        icon: "magnifyingglass",
                        title: "Advanced Search",
                        description: "Search by player, opening, position, and more"
                    )
                    
                    FeatureItem(
                        icon: "square.stack.3d.up.fill",
                        title: "Multi-Game Analysis",
                        description: "Analyze patterns across thousands of games"
                    )
                    
                    FeatureItem(
                        icon: "cloud.fill",
                        title: "Cloud Sync",
                        description: "Access your databases from anywhere"
                    )
                    
                    FeatureItem(
                        icon: "chart.bar.fill",
                        title: "Statistics",
                        description: "Detailed statistics and trends analysis"
                    )
                }
                .padding(.horizontal)
                
                Spacer()
                
                // Coming Soon Badge
                VStack(spacing: 8) {
                    Text("🚧 Coming Soon")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("This feature is currently under development")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.yellow.opacity(0.1))
                .cornerRadius(12)
            }
            .padding()
            .navigationTitle("Database")
        }
    }
}

struct FeatureItem: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.headline)
                
                Text(description)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

#Preview {
    DatabaseView()
}