//
//  DraggedPieceView.swift
//  MacChessBase
//
//  Created by Assistant on 2025/6/7.
//

import SwiftUI
import ChessKit

/// A specialized view for displaying the dragged piece with optimized performance
struct DraggedPieceView: View {
    let piece: Piece
    let position: CGPoint
    let squareSize: CGFloat

    var body: some View {
        Image(pieceImageName(for: piece))
            .resizable()
            .frame(width: squareSize * 0.9, height: squareSize * 0.9)
            .position(position)
            .zIndex(1000)
            .allowsHitTesting(false)
            .shadow(color: .black.opacity(0.3), radius: 6, x: 2, y: 2)
            .scaleEffect(1.05)
            .drawingGroup() // Hardware acceleration
            .animation(.none, value: position) // Disable animation for position updates
    }
    
    private func pieceImageName(for piece: Piece) -> String {
        let colorPrefix = piece.color == .white ? "w" : "b"
        let pieceSymbol: String
        
        switch piece.kind {
        case .pawn: pieceSymbol = "P"
        case .knight: pieceSymbol = "N"
        case .bishop: pieceSymbol = "B"
        case .rook: pieceSymbol = "R"
        case .queen: pieceSymbol = "Q"
        case .king: pieceSymbol = "K"
        }
        
        return "\(colorPrefix)\(pieceSymbol)"
    }
}
