//
//  MainNavigationView.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/7/27.
//

import SwiftUI
import AppKit

struct MainNavigationView: View {
    @EnvironmentObject var sessionManager: GameSessionManager
    @StateObject private var subscriptionService = SubscriptionService.shared
    
    @State private var showingSubscriptionAlert = false
    @State private var alertMessage = ""
    @State private var alertTitle = ""
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerView
            
            // Main Content
            GeometryReader { geometry in
                VStack {
                    Spacer()
                    
                    // Dynamic card sizing based on window size
                    let availableWidth = geometry.size.width - 80 // 40 padding on each side
                    let availableHeight = geometry.size.height - 200 // Account for header and spacing
                    let cardSpacing: CGFloat = 20
                    
                    let cardWidth = max(300, min(500, (availableWidth - cardSpacing) / 2))
                    let cardHeight = max(200, min(350, (availableHeight - cardSpacing) / 2))
                    
                    VStack(spacing: cardSpacing) {
                        HStack(spacing: cardSpacing) {
                            // Chess Analysis Card (Top Left)
                            NavigationCardView(
                                title: "Chess Analysis",
                                description: "Analyze games with engine support and annotations",
                                icon: "crown.fill",
                                isProFeature: false
                            ) {
                                openChessWindow()
                            }
                            .frame(width: cardWidth, height: cardHeight)
                            
                            // Database Card (Top Right)
                            NavigationCardView(
                                title: "Online Database",
                                description: "Access millions of chess games from online databases",
                                icon: "externaldrive.connected.to.line.below.fill",
                                isProFeature: true
                            ) {
                                handleProFeatureAccess("Database") {
                                    openDatabaseWindow()
                                }
                            }
                            .frame(width: cardWidth, height: cardHeight)
                        }
                        
                        HStack(spacing: cardSpacing) {
                            // OCR Card (Bottom Left)
                            NavigationCardView(
                                title: "OCR Recognition",
                                description: "Convert board images to FEN and notation to PGN",
                                icon: "camera.viewfinder",
                                isProFeature: true
                            ) {
                                handleProFeatureAccess("OCR Recognition") {
                                    openOCRWindow()
                                }
                            }
                            .frame(width: cardWidth, height: cardHeight)
                            
                            // Tournament Card (Bottom Right)
                            NavigationCardView(
                                title: "Tournament",
                                description: "Professional tournament management software",
                                icon: "trophy.fill",
                                isProFeature: true
                            ) {
                                handleProFeatureAccess("Tournament Management") {
                                    openTournamentWindow()
                                }
                            }
                            .frame(width: cardWidth, height: cardHeight)
                        }
                    }
                    
                    Spacer()
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color(NSColor.windowBackgroundColor))
        }
        .alert(alertTitle, isPresented: $showingSubscriptionAlert) {
            Button("Upgrade to Pro", role: .none) {
                subscriptionService.upgradeSubscription(to: .pro)
            }
            Button("Maybe Later", role: .cancel) {}
        } message: {
            Text(alertMessage)
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("MacChessBase")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Professional Chess Analysis Suite")
                        .font(.title3)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Subscription Badge
                subscriptionBadge
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            
            Divider()
        }
    }
    
    private var subscriptionBadge: some View {
        HStack(spacing: 8) {
            Image(systemName: subscriptionIcon)
                .foregroundColor(subscriptionColor)
            
            Text(subscriptionText)
                .font(.caption)
                .fontWeight(.semibold)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(subscriptionColor.opacity(0.1))
        .cornerRadius(8)
    }
    
    private var subscriptionIcon: String {
        switch subscriptionService.currentTier {
        case .free:
            return "person.crop.circle"
        case .pro:
            return "star.circle.fill"
        case .expert:
            return "crown.fill"
        }
    }
    
    private var subscriptionColor: Color {
        switch subscriptionService.currentTier {
        case .free:
            return .gray
        case .pro:
            return .orange
        case .expert:
            return .purple
        }
    }
    
    private var subscriptionText: String {
        switch subscriptionService.currentTier {
        case .free:
            return "Free"
        case .pro:
            return "Pro"
        case .expert:
            return "Expert"
        }
    }
    
    private func handleProFeatureAccess(_ featureName: String, action: @escaping () -> Void) {
        if subscriptionService.hasAccess(to: .pro) {
            action()
        } else {
            let alert = subscriptionService.showSubscriptionAlert(for: featureName)
            alertTitle = alert.title
            alertMessage = alert.message
            showingSubscriptionAlert = true
        }
    }
    
    // MARK: - Window Creation Methods
    
    private func openChessWindow() {
        let window = createWindow(
            title: "Chess Analysis",
            size: getDefaultWindowSize(),
            content: ChessView().environmentObject(sessionManager)
        )
        WindowManager.shared.addChildWindow(window)
        window.makeKeyAndOrderFront(nil)
    }
    
    private func openDatabaseWindow() {
        let window = createWindow(
            title: "Online Database",
            size: getDefaultWindowSize(),
            content: DatabaseView()
        )
        WindowManager.shared.addChildWindow(window)
        window.makeKeyAndOrderFront(nil)
    }
    
    private func openOCRWindow() {
        let window = createWindow(
            title: "OCR Recognition",
            size: getDefaultWindowSize(),
            content: OCRView()
        )
        WindowManager.shared.addChildWindow(window)
        window.makeKeyAndOrderFront(nil)
    }
    
    private func openTournamentWindow() {
        let window = createWindow(
            title: "Tournament Management",
            size: getDefaultWindowSize(),
            content: TournamentView()
        )
        WindowManager.shared.addChildWindow(window)
        window.makeKeyAndOrderFront(nil)
    }
    
    private func getDefaultWindowSize() -> NSSize {
        // Get the visible frame of the main screen
        guard let screen = NSScreen.main else {
            return NSSize(width: 1600, height: 1000)
        }
        
        let visibleFrame = screen.visibleFrame
        // Use 90% of the visible screen size for child windows
        let width = visibleFrame.width * 0.9
        let height = visibleFrame.height * 0.9
        print("\(width), \(height)")
        
        return NSSize(width: width, height: height)
    }
    
    private func createWindow<Content: View>(
        title: String,
        size: NSSize,
        content: Content
    ) -> NSWindow {
        let window = NSWindow(
            contentRect: NSRect(origin: .zero, size: size),
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered,
            defer: false
        )
        
        window.title = title
        window.center()
        //window.minSize = NSSize(width: 900, height: 600)
        window.minSize = size
        
        // Use a wrapper view to ensure proper cleanup
        let wrapperView = AnyView(content)
        let hostingController = NSHostingController(rootView: wrapperView)
        
        // Ensure proper cleanup when window is closing
        window.contentViewController = hostingController
        
        // Add window close observation for proper cleanup
        NotificationCenter.default.addObserver(
            forName: NSWindow.willCloseNotification,
            object: window,
            queue: .main
        ) { _ in
            // Explicitly clear the content to prevent crashes
            hostingController.rootView = AnyView(EmptyView())
            window.contentViewController = nil
        }
        
        return window
    }
}

#Preview {
    MainNavigationView()
        .environmentObject(GameSessionManager())
        .frame(width: 800, height: 600)
}
