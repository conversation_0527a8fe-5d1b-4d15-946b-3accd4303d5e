//
//  NavigationCardView.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/7/27.
//

import SwiftUI

struct NavigationCardView: View {
    let title: String
    let description: String
    let icon: String
    let isProFeature: Bool
    let action: () -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 16) {
                // Icon
                Image(systemName: icon)
                    .font(.system(size: 48, weight: .light))
                    .foregroundColor(isProFeature ? .orange : .accentColor)
                
                // Title and Description
                VStack(spacing: 4) {
                    HStack {
                        Text(title)
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        if isProFeature {
                            Text("PRO")
                                .font(.caption)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.orange)
                                .cornerRadius(4)
                        }
                    }
                    
                    Text(description)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .fixedSize(horizontal: false, vertical: true)
                }
            }
            .padding(24)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(NSColor.controlBackgroundColor))
                    .shadow(
                        color: isHovered ? .black.opacity(0.15) : .black.opacity(0.08),
                        radius: isHovered ? 12 : 6,
                        x: 0,
                        y: isHovered ? 6 : 3
                    )
            )
            .scaleEffect(isHovered ? 1.02 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isHovered)
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            isHovered = hovering
        }
    }
}

#Preview {
    HStack(spacing: 20) {
        NavigationCardView(
            title: "Chess Analysis",
            description: "Analyze games with engine support and annotations",
            icon: "crown.fill",
            isProFeature: false,
            action: {}
        )
        .frame(width: 200, height: 160)
        
        NavigationCardView(
            title: "OCR Recognition",
            description: "Convert board images to FEN and notation to PGN",
            icon: "camera.viewfinder",
            isProFeature: true,
            action: {}
        )
        .frame(width: 200, height: 160)
    }
    .padding()
}