//
//  OCRView.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/7/27.
//

import SwiftUI

struct OCRView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Tab Selector
                Picker("OCR Type", selection: $selectedTab) {
                    Text("Board Recognition").tag(0)
                    Text("Notation Recognition").tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                Group {
                    if selectedTab == 0 {
                        BoardRecognitionView()
                    } else {
                        NotationRecognitionView()
                    }
                }
            }
            .navigationTitle("OCR Recognition")
        }
    }
}

struct BoardRecognitionView: View {
    var body: some View {
        VStack(spacing: 30) {
            // Header
            VStack(spacing: 12) {
                Image(systemName: "camera.viewfinder")
                    .font(.system(size: 64))
                    .foregroundColor(.green)
                
                Text("Board Recognition")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Convert chess board images to FEN notation")
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // Feature Preview
            VStack(spacing: 16) {
                HStack(spacing: 20) {
                    VStack {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 120, height: 120)
                            .overlay(
                                Image(systemName: "camera.fill")
                                    .font(.title)
                                    .foregroundColor(.gray)
                            )
                        Text("Chess Board Photo")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Image(systemName: "arrow.right")
                        .font(.title2)
                        .foregroundColor(.blue)
                    
                    VStack {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.blue.opacity(0.1))
                            .frame(width: 120, height: 120)
                            .overlay(
                                VStack {
                                    Text("FEN")
                                        .font(.headline)
                                        .foregroundColor(.blue)
                                    Text("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR")
                                        .font(.caption2)
                                        .multilineTextAlignment(.center)
                                }
                                .padding()
                            )
                        Text("FEN Notation")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // Coming Soon
            ComingSoonView(feature: "Board Recognition")
        }
        .padding()
    }
}

struct NotationRecognitionView: View {
    var body: some View {
        VStack(spacing: 30) {
            // Header
            VStack(spacing: 12) {
                Image(systemName: "doc.text.viewfinder")
                    .font(.system(size: 64))
                    .foregroundColor(.purple)
                
                Text("Notation Recognition")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Convert chess notation images to PGN format")
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // Feature Preview
            VStack(spacing: 16) {
                HStack(spacing: 20) {
                    VStack {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 120, height: 120)
                            .overlay(
                                VStack {
                                    Image(systemName: "doc.text")
                                        .font(.title)
                                        .foregroundColor(.gray)
                                    Text("1.e4 e5\n2.Nf3 Nc6")
                                        .font(.caption2)
                                }
                            )
                        Text("Notation Image")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Image(systemName: "arrow.right")
                        .font(.title2)
                        .foregroundColor(.purple)
                    
                    VStack {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.purple.opacity(0.1))
                            .frame(width: 120, height: 120)
                            .overlay(
                                VStack {
                                    Text("PGN")
                                        .font(.headline)
                                        .foregroundColor(.purple)
                                    Text("[Event \"Game\"]\n1.e4 e5 2.Nf3 Nc6")
                                        .font(.caption2)
                                        .multilineTextAlignment(.center)
                                }
                                .padding()
                            )
                        Text("PGN Format")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // Coming Soon
            ComingSoonView(feature: "Notation Recognition")
        }
        .padding()
    }
}

struct ComingSoonView: View {
    let feature: String
    
    var body: some View {
        VStack(spacing: 8) {
            Text("🚧 Coming Soon")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("\(feature) is currently under development")
                .font(.body)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
    }
}

#Preview {
    OCRView()
}