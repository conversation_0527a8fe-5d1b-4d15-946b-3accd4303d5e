//
//  OptimizedChessSquareView.swift
//  MacChessBase
//
//  Created by Assistant on 2025/6/7.
//

import SwiftUI
import ChessKit

/// An optimized chess square view with better performance for drag operations
struct OptimizedChessSquareView: View {
    let square: Square
    let piece: Piece?
    let isSelected: Bool
    let isPossibleMove: Bool
    let isLastMove: Bool
    let onSquarePressed: () -> Void
    let squareSize: CGFloat
    let currentPlayer: Piece.Color
    
    // Separate these from the main view model to reduce update frequency
    let isDraggedFrom: Bool
    let isReverseDraggedFrom: Bool
    let onDragStart: (Piece, Square, CGPoint) -> Void
    let onDragUpdate: (CGPoint) -> Void
    let onReverseDragUpdate: (CGPoint) -> Void
    let onDragEnd: (Square) -> Void
    
    // Reverse drag support
    let onReverseDragStart: (Square, CGPoint) -> Bool  // Returns true if reverse drag started
    let onReverseDragEnd: (Square) -> Void
    
    // Add canMovePiece function
    let canMovePiece: (Square) -> Bool
    
    // Reverse drag support
    let isReverseDragTarget: Bool
    let isReverseDragSource: Bool
    
    // Visual annotations editing
    let onAnnotationEdit: ((Square) -> Void)?  // For square highlight editing
    let onAnnotationDrag: ((Square, Square) -> Void)?  // For arrow editing
    let onAnnotationDragUpdate: ((Square, Square) -> Void)? // For live preview
    
    @State private var isDropTarget = false
    @State private var isHovered = false
    @State private var annotationDragStart: Square? = nil
    
    private var squareColor: Color {
        let isLight = (square.file.number + square.rank.value) % 2 != 0
        return isLight ? Color.brown.opacity(0.3) : Color.brown.opacity(0.7)
    }
    
    private var overlayColor: Color? {
        if isDropTarget {
            return Color.blue.opacity(0.6)
        } else if isSelected {
            return Color.blue.opacity(0.5)
        } else if isReverseDragTarget {
            return Color.purple.opacity(0.5) // Target square in reverse drag
        } else if isReverseDragSource {
            return Color.orange.opacity(0.4) // Source squares that can move to target
        } else if isPossibleMove {
            return Color.green.opacity(0.4)
        } else if isLastMove {
            return Color.yellow.opacity(0.4)
        } else if isHovered && piece != nil {
            return Color.white.opacity(0.1)
        }
        return nil
    }
    
    var body: some View {
        ZStack {
            // Square background
            Rectangle()
                .fill(squareColor)
            
            // Overlay for selection/highlighting
            if let overlayColor = overlayColor {
                Rectangle()
                    .fill(overlayColor)
            }
            
            // Piece image (only show if not being dragged or reverse dragged)
            if let piece = piece, !isDraggedFrom && !isReverseDraggedFrom {
                Image(pieceImageName(for: piece))
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .padding(4)
                    .scaleEffect(isHovered ? 1.05 : 1.0)
                    .animation(.easeInOut(duration: 0.05), value: isHovered)
            }
            
            // Possible move indicator
            if isPossibleMove && piece == nil {
                Circle()
                    .fill(Color.green.opacity(0.6))
                    .frame(width: 12, height: 12)
                    .scaleEffect(isDropTarget ? 1.3 : 1.0)
                    .animation(.easeInOut(duration: 0.2), value: isDropTarget)
            }
        }
        .onTapGesture {
            // Check for option key press for annotation editing
            if NSEvent.modifierFlags.contains(.option) {
                onAnnotationEdit?(square)
            } else {
                onSquarePressed()
            }
        }
        .onHover { hovering in
            isHovered = hovering
            if hovering {
                // Set appropriate cursor based on state
                if isReverseDragTarget || isReverseDragSource {
                    NSCursor.arrow.set()
                } else if let piece = piece, piece.color == currentPlayer, canMovePiece(square) {
                    NSCursor.openHand.set()
                } else {
                    NSCursor.arrow.set()
                }
            } else {
                NSCursor.arrow.set()
            }
        }
        .gesture(
            DragGesture(coordinateSpace: .named("chessBoard"))
                .onChanged { value in
                    // Check for option key for annotation editing
                    if NSEvent.modifierFlags.contains(.option) {
                        // Start annotation drag if not already started
                        if annotationDragStart == nil {
                            annotationDragStart = square
                        }
                        // Update preview
                        if let startSquare = annotationDragStart {
                            let currentSquare = calculateDropSquareLocal(for: value.location)
                            onAnnotationDragUpdate?(startSquare, currentSquare)
                        }
                        return
                    }
                    
                    // Handle drag start
                    if !isDraggedFrom && !isReverseDraggedFrom {
                        if let piece = piece {
                            if piece.color == currentPlayer {
                                // Normal piece drag for our pieces
                                onDragStart(piece, square, value.location)
                                NSCursor.closedHand.set()
                            } else {
                                // Reverse drag from opponent pieces
                                _ = onReverseDragStart(square, value.location)
                                NSCursor.arrow.set()
                            }
                        } else {
                            // Reverse drag from empty square
                            _ = onReverseDragStart(square, value.location)
                            NSCursor.arrow.set()
                        }
                    }
                    // Handle drag updates
                    else if isDraggedFrom {
                        onDragUpdate(value.location)
                        NSCursor.closedHand.set()
                    }
                    else if isReverseDraggedFrom {
                        onReverseDragUpdate(value.location)
                        NSCursor.arrow.set()
                    }
                }
                .onEnded { value in
                    // Handle annotation drag end
                    if let startSquare = annotationDragStart, NSEvent.modifierFlags.contains(.option) { // .option
                        let endSquare = calculateDropSquareLocal(for: value.location)
                        // Pass to handler, which will decide whether to create an arrow
                        onAnnotationDrag?(startSquare, endSquare)
                        annotationDragStart = nil
                        return
                    }
                    
                    if isDraggedFrom {
                        // Complete normal drag
                        let targetSquare = calculateDropSquareLocal(for: value.location)
                        onDragEnd(targetSquare)
                        NSCursor.arrow.set()
                    } else if isReverseDraggedFrom {
                        // Complete reverse drag
                        let sourceSquare = calculateDropSquareLocal(for: value.location)
                        onReverseDragEnd(sourceSquare)
                        NSCursor.arrow.set()
                    }
                }
        )
    }
    
    private func pieceImageName(for piece: Piece) -> String {
        let colorPrefix = piece.color == .white ? "w" : "b"
        let pieceSymbol: String
        
        switch piece.kind {
        case .pawn: pieceSymbol = "P"
        case .knight: pieceSymbol = "N"
        case .bishop: pieceSymbol = "B"
        case .rook: pieceSymbol = "R"
        case .queen: pieceSymbol = "Q"
        case .king: pieceSymbol = "K"
        }
        
        return "\(colorPrefix)\(pieceSymbol)"
    }
    
    /// Calculates which square a piece was dropped on based on local board coordinates
    private func calculateDropSquareLocal(for location: CGPoint) -> Square {
        // Account for the border (2px on each side)
        let adjustedX = location.x - 2
        let adjustedY = location.y - 2
        
        // Calculate which square was clicked using dynamic square size
        let fileIndex = max(0, min(7, Int(adjustedX / squareSize)))
        let rankIndex = max(0, min(7, Int(adjustedY / squareSize)))
        
        // Convert to chess coordinates (a-h, 1-8)
        let files = ["a", "b", "c", "d", "e", "f", "g", "h"]
        let ranks = [8, 7, 6, 5, 4, 3, 2, 1] // Board is displayed with rank 8 at top
        
        // Check bounds
        guard fileIndex < files.count && rankIndex < ranks.count else {
            return square // Fallback to current square if out of bounds
        }
        
        let file = files[fileIndex]
        let rank = ranks[rankIndex]
        
        return Square("\(file)\(rank)") // Fallback to current square if invalid
    }
}
