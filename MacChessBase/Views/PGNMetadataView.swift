//
//  PGNMetadataView.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/6/2.
//  Refactored by <PERSON> on 2025/7/9 - Simplified architecture, removed data duplication
//

import SwiftUI
import ChessKit

/// Configuration for a PGN metadata field (UI only, no data storage)
struct PGNFieldConfig: Identifiable {
    let id = UUID()
    let key: String
    let displayName: String
    let placeholder: String
    let isRequired: Bool
    let fieldType: FieldType
    
    enum FieldType {
        case text
        case date
        case result
        case multiline
    }
    
    init(key: String, displayName: String, placeholder: String = "", isRequired: Bool = false, fieldType: FieldType = .text) {
        self.key = key
        self.displayName = displayName
        self.placeholder = placeholder
        self.isRequired = isRequired
        self.fieldType = fieldType
    }
}

/// Simplified PGN metadata view that directly binds to ChessGameViewModel
struct PGNMetadataView: View {
    @Binding var isPresented: Bool
    @ObservedObject var viewModel: ChessGameViewModel
    
    // UI field configurations (no data storage)
    private let fieldConfigs: [PGNFieldConfig] = [
        // Required fields
        PGNFieldConfig(key: "event", displayName: "Event", placeholder: "Tournament or match name", isRequired: true),
        PGNFieldConfig(key: "site", displayName: "Site", placeholder: "Location", isRequired: true),
        PGNFieldConfig(key: "date", displayName: "Date", placeholder: "YYYY.MM.DD", isRequired: true, fieldType: .date),
        PGNFieldConfig(key: "round", displayName: "Round", placeholder: "Round number", isRequired: true),
        PGNFieldConfig(key: "white", displayName: "White Player", placeholder: "White player name", isRequired: true),
        PGNFieldConfig(key: "black", displayName: "Black Player", placeholder: "Black player name", isRequired: true),
        PGNFieldConfig(key: "result", displayName: "Result", placeholder: "1-0, 0-1, 1/2-1/2, or *", isRequired: true, fieldType: .result),
        
        // Optional fields
        PGNFieldConfig(key: "whiteTeam", displayName: "White Team", placeholder: "Team or club"),
        PGNFieldConfig(key: "blackTeam", displayName: "Black Team", placeholder: "Team or club"),
        PGNFieldConfig(key: "whiteTitle", displayName: "White Title", placeholder: "GM, IM, FM, etc."),
        PGNFieldConfig(key: "blackTitle", displayName: "Black Title", placeholder: "GM, IM, FM, etc."),
        PGNFieldConfig(key: "whiteElo", displayName: "White Elo", placeholder: "Rating"),
        PGNFieldConfig(key: "blackElo", displayName: "Black Elo", placeholder: "Rating"),
        PGNFieldConfig(key: "whiteFideId", displayName: "White FIDE ID", placeholder: "FIDE ID number"),
        PGNFieldConfig(key: "blackFideId", displayName: "Black FIDE ID", placeholder: "FIDE ID number"),
        PGNFieldConfig(key: "eco", displayName: "ECO Code", placeholder: "Opening classification"),
        PGNFieldConfig(key: "opening", displayName: "Opening", placeholder: "Opening name"),
        PGNFieldConfig(key: "timeControl", displayName: "Time Control", placeholder: "e.g., 90m+30s"),
        PGNFieldConfig(key: "notes", displayName: "Notes", placeholder: "General comments", fieldType: .multiline),
        PGNFieldConfig(key: "fen", displayName: "FEN", placeholder: "Starting position FEN"),
        PGNFieldConfig(key: "setUp", displayName: "Set Up", placeholder: "1 if FEN is used")
    ]
    
    var body: some View {
        VStack(spacing: 0) {
            // Title bar
            HStack {
                Text("Game Information")
                    .font(.title2)
                    .fontWeight(.semibold)
                Spacer()
                
                Button("Cancel") {
                    isPresented = false
                }
                .keyboardShortcut(.escape)
                
                Button("Save") {
                    // Data is already saved directly to game.metadata
                    // No conversion or copying needed
                    isPresented = false
                }
                .buttonStyle(.borderedProminent)
                .keyboardShortcut(.return)
            }
            .padding()
            .background(Color(NSColor.windowBackgroundColor))
            
            Divider()
            
            // Content
            ScrollView {
                VStack(spacing: 20) {
                    // Required Fields Section
                    GroupBox("Required Information") {
                        VStack(alignment: .leading, spacing: 12) {
                            ForEach(requiredFields) { field in
                                buildFieldView(for: field)
                            }
                        }
                        .padding()
                    }
                    
                    // Optional Fields Section
                    GroupBox("Optional Information") {
                        VStack(alignment: .leading, spacing: 12) {
                            ForEach(optionalFields) { field in
                                buildFieldView(for: field)
                            }
                        }
                        .padding()
                    }
                }
                .padding()
            }
        }
        .frame(width: 600, height: 700)
        .onAppear {
            // Set default date if empty
            setDefaultDateIfNeeded()
            // Auto-detect game result if needed
            viewModel.autoDetectGameResult()
        }
    }
    
    // MARK: - Computed Properties
    
    private var requiredFields: [PGNFieldConfig] {
        fieldConfigs.filter(\.isRequired)
    }
    
    private var optionalFields: [PGNFieldConfig] {
        fieldConfigs.filter { !$0.isRequired }
    }
    
    // MARK: - View Builders
    
    @ViewBuilder
    private func buildFieldView(for field: PGNFieldConfig) -> some View {
        HStack {
            Text("\(field.displayName):")
                .frame(width: 120, alignment: .leading)
                .foregroundColor(field.isRequired ? .primary : .secondary)
            
            switch field.fieldType {
            case .result:
                buildResultPicker()
            case .multiline:
                buildMultilineField(for: field)
            case .text, .date:
                buildTextField(for: field)
            }
            
            Spacer()
        }
    }
    
    @ViewBuilder
    private func buildResultPicker() -> some View {
        Picker("Result", selection: viewModel.resultBinding) {
            Text("Game in progress (*)").tag("*")
            Text("White wins (1-0)").tag("1-0")
            Text("Black wins (0-1)").tag("0-1")
            Text("Draw (1/2-1/2)").tag("1/2-1/2")
        }
        .pickerStyle(.menu)
        .labelsHidden()
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    @ViewBuilder
    private func buildMultilineField(for field: PGNFieldConfig) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            TextEditor(text: bindingFor(field.key))
                .frame(minHeight: 60)
                .border(Color.gray.opacity(0.3), width: 1)
                .cornerRadius(4)
        }
    }
    
    @ViewBuilder
    private func buildTextField(for field: PGNFieldConfig) -> some View {
        TextField(field.placeholder, text: bindingFor(field.key))
            .textFieldStyle(.roundedBorder)
    }
    
    // MARK: - Binding Logic
    
    /// Returns the appropriate binding for a field key
    private func bindingFor(_ key: String) -> Binding<String> {
        switch key {
        case "event": return viewModel.eventBinding
        case "site": return viewModel.siteBinding
        case "date": return viewModel.dateBinding
        case "round": return viewModel.roundBinding
        case "white": return viewModel.whiteBinding
        case "black": return viewModel.blackBinding
        case "result": return viewModel.resultBinding
        case "whiteTeam": return viewModel.whiteTeamBinding
        case "blackTeam": return viewModel.blackTeamBinding
        case "whiteTitle": return viewModel.whiteTitleBinding
        case "blackTitle": return viewModel.blackTitleBinding
        case "whiteElo": return viewModel.whiteEloBinding
        case "blackElo": return viewModel.blackEloBinding
        case "whiteFideId": return viewModel.whiteFideIdBinding
        case "blackFideId": return viewModel.blackFideIdBinding
        case "eco": return viewModel.ecoBinding
        case "opening": return viewModel.openingBinding
        case "timeControl": return viewModel.timeControlBinding
        case "notes": return viewModel.notesBinding
        case "fen": return viewModel.fenBinding
        case "setUp": return viewModel.setUpBinding
        default: 
            // Return a constant binding for unknown keys
            return .constant("")
        }
    }
    
    // MARK: - Helper Methods
    
    private func setDefaultDateIfNeeded() {
        let session = viewModel.session
        if session.game.metadata.date.isEmpty {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy.MM.dd"
            session.game.metadata.date = formatter.string(from: Date())
        }
    }
}

//// MARK: - Preview
//#if DEBUG
//#Preview {
//    PGNMetadataView(
//        isPresented: .constant(true),
//        viewModel: ChessGameViewModel()
//    )
//}
//#endif
