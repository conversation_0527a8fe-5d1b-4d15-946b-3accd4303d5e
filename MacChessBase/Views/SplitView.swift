//
//  SplitView.swift
//  MacChessBase
//
//  Created on 2025/6/8.
//

import SwiftUI

/// A resizable split view that allows users to adjust the relative sizes of two views
struct SplitView<TopContent: View, BottomContent: View>: View {
    let topContent: () -> TopContent
    let bottomContent: () -> BottomContent
    let initialRatio: CGFloat
    let minTopHeight: CGFloat
    let minBottomHeight: CGFloat
    
    @State private var splitRatio: CGFloat
    @State private var isDragging = false
    
    init(
        initialRatio: CGFloat = 0.7,
        minTopHeight: CGFloat = 100,
        minBottomHeight: CGFloat = 100,
        @ViewBuilder topContent: @escaping () -> TopContent,
        @ViewBuilder bottomContent: @escaping () -> BottomContent
    ) {
        self.topContent = topContent
        self.bottomContent = bottomContent
        self.initialRatio = initialRatio
        self.minTopHeight = minTopHeight
        self.minBottomHeight = minBottomHeight
        self._splitRatio = State(initialValue: initialRatio)
    }
    
    var body: some View {
        GeometryReader { geometry in
            let totalHeight = max(0, geometry.size.height)

            // Prevent invalid frame dimensions
            if totalHeight <= 0 || !totalHeight.isFinite {
                // Return an EmptyView or some placeholder if the height is invalid
                // This ensures the body always returns some View.
                EmptyView()
            } else {
                // Ensure we have enough height for both minimum requirements
                // Adjusted to use a smaller portion for min heights to avoid issues when totalHeight is small
                let adjustedMinTopHeight = min(minTopHeight, totalHeight * 0.4)
                let adjustedMinBottomHeight = min(minBottomHeight, totalHeight * 0.4)

                // Calculate top and bottom heights
                // Ensure topHeight calculation doesn't result in negative or excessively large values
                let calculatedTopHeight = totalHeight * splitRatio
                let topHeight = max(adjustedMinTopHeight, min(calculatedTopHeight, totalHeight - adjustedMinBottomHeight))
                
                // Ensure bottomHeight is at least its minimum and doesn't make topHeight too small
                let bottomHeight = max(adjustedMinBottomHeight, totalHeight - topHeight)

                VStack(spacing: 0) {
                    // Top content
                    topContent()
                        .frame(height: topHeight)
                        .clipped()

                    // Resizable divider
                    Rectangle()
                        .fill(isDragging ? Color.blue.opacity(0.3) : Color.gray.opacity(0.3))
                        .frame(height: 4)
                        .overlay(
                            Rectangle()
                                .fill(Color.clear)
                                .frame(height: 8) // Larger hit area
                        )
                        .onHover { hovering in
                            if hovering {
                                NSCursor.resizeUpDown.push()
                            } else {
                                NSCursor.pop()
                            }
                        }
                        .gesture(
                            DragGesture(coordinateSpace: .local)
                                .onChanged { value in
                                    if !isDragging {
                                        isDragging = true
                                    }

                                    // Use the previous topHeight (before this drag event) for calculation
                                    // This requires storing the topHeight at the start of the drag or using splitRatio consistently
                                    // For simplicity, let's recalculate based on splitRatio and translation
                                    
                                    let currentTopHeight = totalHeight * splitRatio // Height based on current ratio
                                    let newTopHeightCandidate = currentTopHeight + value.translation.height
                                    
                                    var newRatio = newTopHeightCandidate / totalHeight
                                    
                                    // Clamp newRatio to prevent extreme values and ensure min heights
                                    let minRatio = adjustedMinTopHeight / totalHeight
                                    let maxRatio = (totalHeight - adjustedMinBottomHeight) / totalHeight
                                    
                                    if totalHeight > 0 && (adjustedMinTopHeight + adjustedMinBottomHeight <= totalHeight) { // Ensure there's enough space for min heights
                                       newRatio = max(minRatio, min(maxRatio, newRatio))
                                    } else {
                                       newRatio = splitRatio // or some default if space is insufficient
                                    }

                                    if newRatio.isFinite {
                                        splitRatio = newRatio
                                    }
                                }
                                .onEnded { _ in
                                    isDragging = false
                                }
                        )

                    // Bottom content
                    bottomContent()
                        .frame(height: bottomHeight)
                        .clipped()
                }
            }
        }
    }
}

/// A resizable horizontal split view that allows users to adjust the relative sizes of two views side by side
struct HSplitView<LeftContent: View, RightContent: View>: View {
    let leftContent: () -> LeftContent
    let rightContent: () -> RightContent
    let initialRatio: CGFloat
    let minLeftWidth: CGFloat
    let minRightWidth: CGFloat
    
    @State private var splitRatio: CGFloat
    @State private var isDragging = false
    
    init(
        initialRatio: CGFloat = 0.6,
        minLeftWidth: CGFloat = 200,
        minRightWidth: CGFloat = 200,
        @ViewBuilder leftContent: @escaping () -> LeftContent,
        @ViewBuilder rightContent: @escaping () -> RightContent
    ) {
        self.leftContent = leftContent
        self.rightContent = rightContent
        self.initialRatio = initialRatio
        self.minLeftWidth = minLeftWidth
        self.minRightWidth = minRightWidth
        self._splitRatio = State(initialValue: initialRatio)
    }
    
    var body: some View {
        GeometryReader { geometry in
            let totalWidth = max(0, geometry.size.width)

            // Prevent invalid frame dimensions
            if totalWidth <= 0 || !totalWidth.isFinite {
                EmptyView()
            } else {
                // Ensure we have enough width for both minimum requirements
                let adjustedMinLeftWidth = min(minLeftWidth, totalWidth * 0.4)
                let adjustedMinRightWidth = min(minRightWidth, totalWidth * 0.4)

                // Calculate left and right widths
                let calculatedLeftWidth = totalWidth * splitRatio
                let leftWidth = max(adjustedMinLeftWidth, min(calculatedLeftWidth, totalWidth - adjustedMinRightWidth))
                let rightWidth = max(adjustedMinRightWidth, totalWidth - leftWidth)

                HStack(spacing: 0) {
                    // Left content
                    leftContent()
                        .frame(width: leftWidth)
                        .clipped()

                    // Resizable divider
                    Rectangle()
                        .fill(isDragging ? Color.blue.opacity(0.3) : Color.gray.opacity(0.3))
                        .frame(width: 4)
                        .overlay(
                            Rectangle()
                                .fill(Color.clear)
                                .frame(width: 8) // Larger hit area
                        )
                        .onHover { hovering in
                            if hovering {
                                NSCursor.resizeLeftRight.push()
                            } else {
                                NSCursor.pop()
                            }
                        }
                        .gesture(
                            DragGesture(coordinateSpace: .local)
                                .onChanged { value in
                                    if !isDragging {
                                        isDragging = true
                                    }

                                    let currentLeftWidth = totalWidth * splitRatio
                                    let newLeftWidthCandidate = currentLeftWidth + value.translation.width
                                    
                                    var newRatio = newLeftWidthCandidate / totalWidth
                                    
                                    // Clamp newRatio to prevent extreme values and ensure min widths
                                    let minRatio = adjustedMinLeftWidth / totalWidth
                                    let maxRatio = (totalWidth - adjustedMinRightWidth) / totalWidth
                                    
                                    if totalWidth > 0 && (adjustedMinLeftWidth + adjustedMinRightWidth <= totalWidth) {
                                       newRatio = max(minRatio, min(maxRatio, newRatio))
                                    } else {
                                       newRatio = splitRatio
                                    }

                                    if newRatio.isFinite {
                                        splitRatio = newRatio
                                    }
                                }
                                .onEnded { _ in
                                    isDragging = false
                                }
                        )

                    // Right content
                    rightContent()
                        .frame(width: rightWidth)
                        .clipped()
                }
            }
        }
    }
}

#Preview {
    SplitView(
        initialRatio: 0.7,
        topContent: {
            VStack {
                Text("Top Content")
                    .font(.headline)
                Spacer()
                Text("This is the top area")
                Spacer()
            }
            .background(Color.blue.opacity(0.1))
        },
        bottomContent: {
            VStack {
                Text("Bottom Content")
                    .font(.headline)
                Spacer()
                Text("This is the bottom area")
                Spacer()
            }
            .background(Color.green.opacity(0.1))
        }
    )
    .frame(width: 300, height: 500)
}

#Preview {
    HSplitView(
        initialRatio: 0.6,
        leftContent: {
            VStack {
                Text("Left Content")
                    .font(.headline)
                Spacer()
                Text("This is the left area")
                Spacer()
            }
            .background(Color.blue.opacity(0.1))
        },
        rightContent: {
            VStack {
                Text("Right Content")
                    .font(.headline)
                Spacer()
                Text("This is the right area")
                Spacer()
            }
            .background(Color.green.opacity(0.1))
        }
    )
    .frame(width: 800, height: 500)
}
