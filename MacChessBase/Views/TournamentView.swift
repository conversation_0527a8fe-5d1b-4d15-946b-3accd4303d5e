//
//  TournamentView.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/7/27.
//

import SwiftUI

struct TournamentView: View {
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Header
                VStack(spacing: 12) {
                    Image(systemName: "trophy.fill")
                        .font(.system(size: 64))
                        .foregroundColor(.gold)
                    
                    Text("Tournament Management")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Professional tournament organization software")
                        .font(.title3)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // Feature Categories
                HStack(spacing: 24) {
                    TournamentFeatureCard(
                        icon: "person.3.fill",
                        title: "Player Management",
                        features: ["Registration", "Ratings", "Profiles", "History"]
                    )
                    
                    TournamentFeatureCard(
                        icon: "shuffle",
                        title: "Pairing System",
                        features: ["Swiss System", "Round Robin", "Knockout", "Custom"]
                    )
                    
                    TournamentFeatureCard(
                        icon: "chart.bar.fill",
                        title: "Live Scoring",
                        features: ["Real-time", "Standings", "Statistics", "Reports"]
                    )
                }
                
                // SP98 Reference
                VStack(spacing: 8) {
                    Text("Inspired by SP98")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("Professional-grade tournament management with all the features you need")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                Spacer()
                
                // Coming Soon Badge
                VStack(spacing: 8) {
                    Text("🏆 Coming Soon")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("Full tournament management system in development")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(12)
            }
            .padding()
            .navigationTitle("Tournament")
        }
    }
}

struct TournamentFeatureCard: View {
    let icon: String
    let title: String
    let features: [String]
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 32))
                .foregroundColor(.blue)
            
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(alignment: .leading, spacing: 4) {
                ForEach(features, id: \.self) { feature in
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.green)
                        Text(feature)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Spacer()
                    }
                }
            }
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(12)
    }
}

extension Color {
    static let gold = Color(red: 1.0, green: 0.84, blue: 0.0)
}

#Preview {
    TournamentView()
}