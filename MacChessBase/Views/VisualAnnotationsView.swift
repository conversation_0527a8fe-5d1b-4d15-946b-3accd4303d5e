//
//  VisualAnnotationsView.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/1/27.
//

import SwiftUI
import ChessKit

/// A view that renders visual annotations (square highlights and arrows) on top of the chess board
struct VisualAnnotationsView: View {
    let visualAnnotations: Move.VisualAnnotations
    let squareSize: CGFloat
    let isFlipped: Bool
    
    var body: some View {
        ZStack {
            // Square highlights
            ForEach(Array(visualAnnotations.squareHighlights.enumerated()), id: \.offset) { index, highlight in
                squareHighlightView(for: highlight)
            }
            
            // Arrows
            ForEach(Array(visualAnnotations.arrows.enumerated()), id: \.offset) { index, arrow in
                arrowView(for: arrow)
            }
        }
    }
    
    // MARK: - Square Highlight View
    
    @ViewBuilder
    private func squareHighlightView(for highlight: Move.VisualAnnotations.SquareHighlight) -> some View {
        let transformer = BoardCoordinateTransformer(isFlipped: isFlipped)
        let position = transformer.squareToPosition(highlight.square, squareSize: squareSize)
        
        Circle()
            .stroke(color(for: highlight.color), lineWidth: 6) // Increased thickness from 3 to 6
            .frame(width: 0.95 * squareSize, height: 0.95 * squareSize)
            .position(
                x: position.x + squareSize / 2,
                y: position.y + squareSize / 2
            )
    }
    
    // MARK: - Arrow View
    
    @ViewBuilder
    private func arrowView(for arrow: Move.VisualAnnotations.Arrow) -> some View {
        let transformer = BoardCoordinateTransformer(isFlipped: isFlipped)
        let fromPosition = transformer.squareToPosition(arrow.from, squareSize: squareSize)
        let toPosition = transformer.squareToPosition(arrow.to, squareSize: squareSize)
        
        ZStack {
            // Arrow shaft and outline
            ArrowShape(
                from: CGPoint(
                    x: fromPosition.x + squareSize / 2,
                    y: fromPosition.y + squareSize / 2
                ),
                to: CGPoint(
                    x: toPosition.x + squareSize / 2,
                    y: toPosition.y + squareSize / 2
                )
            )
            .stroke(color(for: arrow.color), lineWidth: 10) // Increased thickness from 4 to 8
            .opacity(0.8)
            
            // Filled arrow head
            ArrowHeadShape(
                from: CGPoint(
                    x: fromPosition.x + squareSize / 2,
                    y: fromPosition.y + squareSize / 2
                ),
                to: CGPoint(
                    x: toPosition.x + squareSize / 2,
                    y: toPosition.y + squareSize / 2
                )
            )
            .fill(color(for: arrow.color))
            .opacity(0.8)
        }
    }
    
    // MARK: - Helper Methods
    
    /// Convert annotation color to SwiftUI Color
    private func color(for annotationColor: Move.VisualAnnotations.AnnotationColor) -> Color {
        switch annotationColor {
        case .red:
            return .red
        case .green:
            return .green
        case .blue:
            return .blue
        }
    }
}

/// Custom shape for drawing arrows
struct ArrowShape: Shape {
    let from: CGPoint
    let to: CGPoint
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        // Calculate arrow direction and length
        let dx = to.x - from.x
        let dy = to.y - from.y
        let length = sqrt(dx * dx + dy * dy)
        
        // Don't draw arrows that are too short
        guard length > 20 else { return path }
        
        // Normalize direction vector
        let unitX = dx / length
        let unitY = dy / length
        
        // Arrow head size proportional to square size but with reasonable limits
        let arrowHeadLength = min(max(length * 0.2, 10), 20)
        
        // Calculate arrow head points
        let arrowTipX = to.x - unitX * 5 // Stop slightly before the target square center
        let arrowTipY = to.y - unitY * 5
        
        let arrowBackX = arrowTipX - unitX * arrowHeadLength
        let arrowBackY = arrowTipY - unitY * arrowHeadLength
        
        // Draw arrow shaft (from start to arrow head base)
        let shaftStartX = from.x + unitX * 5 // Start slightly after the source square center
        let shaftStartY = from.y + unitY * 5
        
        path.move(to: CGPoint(x: shaftStartX, y: shaftStartY))
        path.addLine(to: CGPoint(x: arrowBackX, y: arrowBackY))
        
        return path
    }
}

/// Custom shape for drawing filled arrow heads
struct ArrowHeadShape: Shape {
    let from: CGPoint
    let to: CGPoint
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        // Calculate arrow direction and length
        let dx = to.x - from.x
        let dy = to.y - from.y
        let length = sqrt(dx * dx + dy * dy)
        
        // Don't draw arrows that are too short
        guard length > 20 else { return path }
        
        // Normalize direction vector
        let unitX = dx / length
        let unitY = dy / length
        
        // Arrow head size proportional to square size but with reasonable limits
        let arrowHeadLength = min(max(length * 0.2, 10), 20)
        let arrowHeadWidth = arrowHeadLength * 0.8
        
        // Calculate arrow head points
        let arrowTipX = to.x - unitX * 5 // Stop slightly before the target square center
        let arrowTipY = to.y - unitY * 5
        
        let arrowBackX = arrowTipX - unitX * arrowHeadLength
        let arrowBackY = arrowTipY - unitY * arrowHeadLength
        
        // Perpendicular vector for arrow head width
        let perpX = -unitY
        let perpY = unitX
        
        let arrowLeft = CGPoint(
            x: arrowBackX + perpX * arrowHeadWidth,
            y: arrowBackY + perpY * arrowHeadWidth
        )
        
        let arrowRight = CGPoint(
            x: arrowBackX - perpX * arrowHeadWidth,
            y: arrowBackY - perpY * arrowHeadWidth
        )
        
        // Create filled triangle for arrow head
        path.move(to: CGPoint(x: arrowTipX, y: arrowTipY))
        path.addLine(to: arrowLeft)
        path.addLine(to: arrowRight)
        path.closeSubpath() // This creates a filled triangle
        
        return path
    }
}
