//
//  BoardCoordinateTransformerTests.swift
//  MacChessBaseTests
//
//  Tests for BoardCoordinateTransformer utility class
//

import XCTest
@testable import MacChessBase
@testable import ChessKit

final class BoardCoordinateTransformerTests: XCTestCase {
    
    // MARK: - Board Coordinate Transformer Tests
    
    func testSquareToCoordinateTransform() {
        let transformer = BoardCoordinateTransformer(isFlipped: false)
        let squareSize: CGFloat = 50
        
        // Test a1 square (bottom-left when not flipped)
        let a1Point = transformer.squareToPosition(.a1, squareSize: squareSize)
        XCTAssertEqual(a1Point.x, 0, accuracy: 1.0) // a file = 0
        XCTAssertEqual(a1Point.y, 350, accuracy: 1.0) // rank 1 = 7th row from top
        
        // Test h8 square (top-right when not flipped)
        let h8Point = transformer.squareToPosition(.h8, squareSize: squareSize)
        XCTAssertEqual(h8Point.x, 350, accuracy: 1.0) // h file = 7
        XCTAssertEqual(h8Point.y, 0, accuracy: 1.0) // rank 8 = 0th row from top
        
        // Test e4 square
        let e4Point = transformer.squareToPosition(.e4, squareSize: squareSize)
        XCTAssertEqual(e4Point.x, 200, accuracy: 1.0) // e file = 4
        XCTAssertEqual(e4Point.y, 200, accuracy: 1.0) // rank 4 = 4th row from top
    }
    
    func testSquareToCoordinateTransformFlipped() {
        let transformer = BoardCoordinateTransformer(isFlipped: true)
        let squareSize: CGFloat = 50
        
        // Test a1 square (when flipped, a1 appears at top-right)
        let a1Point = transformer.squareToPosition(.a1, squareSize: squareSize)
        XCTAssertEqual(a1Point.x, 350, accuracy: 1.0) // flipped a file = 7
        XCTAssertEqual(a1Point.y, 0, accuracy: 1.0) // flipped rank 1 = 0
        
        // Test h8 square (when flipped, h8 appears at bottom-left)
        let h8Point = transformer.squareToPosition(.h8, squareSize: squareSize)
        XCTAssertEqual(h8Point.x, 0, accuracy: 1.0) // flipped h file = 0
        XCTAssertEqual(h8Point.y, 350, accuracy: 1.0) // flipped rank 8 = 7
    }
    
    func testPointToSquareTransform() {
        let transformer = BoardCoordinateTransformer(isFlipped: false)
        let squareSize: CGFloat = 50
        
        // Test point in a1 square (using coordinates from square position)
        let a1Square = transformer.positionToSquare(CGPoint(x: 0, y: 350), squareSize: squareSize)
        XCTAssertEqual(a1Square, .a1)
        
        // Test point in h8 square
        let h8Square = transformer.positionToSquare(CGPoint(x: 350, y: 0), squareSize: squareSize)
        XCTAssertEqual(h8Square, .h8)
        
        // Test point in e4 square
        let e4Square = transformer.positionToSquare(CGPoint(x: 200, y: 200), squareSize: squareSize)
        XCTAssertEqual(e4Square, .e4)
    }
    
    func testPointToSquareTransformFlipped() {
        let transformer = BoardCoordinateTransformer(isFlipped: true)
        let squareSize: CGFloat = 50
        
        // Test point in a1 square when flipped
        let a1Square = transformer.positionToSquare(CGPoint(x: 350, y: 0), squareSize: squareSize)
        XCTAssertEqual(a1Square, .a1)
        
        // Test point in h8 square when flipped
        let h8Square = transformer.positionToSquare(CGPoint(x: 0, y: 350), squareSize: squareSize)
        XCTAssertEqual(h8Square, .h8)
    }
    
    func testPointOutsideBoard() {
        let transformer = BoardCoordinateTransformer(isFlipped: false)
        let squareSize: CGFloat = 50
        
        // Test points outside board boundaries
        let outsidePoint1 = transformer.positionToSquare(CGPoint(x: -10, y: 200), squareSize: squareSize)
        XCTAssertNil(outsidePoint1)
        
        let outsidePoint2 = transformer.positionToSquare(CGPoint(x: 410, y: 200), squareSize: squareSize)
        XCTAssertNil(outsidePoint2)
        
        let outsidePoint3 = transformer.positionToSquare(CGPoint(x: 200, y: -10), squareSize: squareSize)
        XCTAssertNil(outsidePoint3)
        
        let outsidePoint4 = transformer.positionToSquare(CGPoint(x: 200, y: 410), squareSize: squareSize)
        XCTAssertNil(outsidePoint4)
    }
    
    // MARK: - ChessKit Extensions Tests
    
    func testFileAndRankLabels() {
        let normalTransformer = BoardCoordinateTransformer(isFlipped: false)
        let flippedTransformer = BoardCoordinateTransformer(isFlipped: true)
        
        // Test file labels
        XCTAssertEqual(normalTransformer.fileLabels, ["a", "b", "c", "d", "e", "f", "g", "h"])
        XCTAssertEqual(flippedTransformer.fileLabels, ["h", "g", "f", "e", "d", "c", "b", "a"])
        
        // Test rank labels
        XCTAssertEqual(normalTransformer.rankLabels, [8, 7, 6, 5, 4, 3, 2, 1])
        XCTAssertEqual(flippedTransformer.rankLabels, [1, 2, 3, 4, 5, 6, 7, 8])
    }
    
    func testPositionExtensions() {
        let position = Position.standard
        
        // Test position extension methods if they exist
        // This would include any custom extensions added to Position
        XCTAssertEqual(position.fen, "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")
    }
    
    func testMoveExtensions() {
        var game = Game()
        game.make(move: "e4", from: MoveTree.minimumIndex)
        
        let moves = game.moves.history(for: game.moves.lastMainVariationIndex)
        XCTAssertFalse(moves.isEmpty)
        
        if let firstMoveIndex = moves.last,
           let move = game.moves.getNodeMove(index: firstMoveIndex) {
            // Test move extension methods if they exist
            XCTAssertEqual(move.metaMove?.san, "e4")
        }
    }
    
    func testGameExtensions() {
        var game = Game()
        
        // Test game extension methods
        game.make(move: "e4", from: MoveTree.minimumIndex)
        game.make(move: "e5", from: game.moves.lastMainVariationIndex)
        
        XCTAssertFalse(game.moves.isEmpty)
        XCTAssertEqual(game.moves.count, 2)
    }
    
    func testSquareExtensions() {
        // Test any Square extensions
        let square = Square.e4
        
        // Test conversion methods if they exist
        XCTAssertEqual(square.file.number, 5) // e file
        XCTAssertEqual(square.rank.value, 4) // 4th rank
    }
    
    func testPieceExtensions() {
        let whitePawn = Piece(.pawn, color: .white, square: .e2)
        
        // Test piece extension methods
        XCTAssertEqual(whitePawn.kind, .pawn)
        XCTAssertEqual(whitePawn.color, .white)
        XCTAssertEqual(whitePawn.square, .e2)
    }
    
    // MARK: - Utility Functions Tests
    
    func testBoardSizeCalculations() {
        let transformer = BoardCoordinateTransformer(isFlipped: false)
        let squareSize: CGFloat = 50
        
        // Test that coordinate transformations work with given square size
        let e4Point = transformer.squareToPosition(.e4, squareSize: squareSize)
        let backToSquare = transformer.positionToSquare(e4Point, squareSize: squareSize)
        XCTAssertEqual(backToSquare, .e4)
    }
    
    func testBoardSizeCalculationsWithDifferentSizes() {
        let transformer = BoardCoordinateTransformer(isFlipped: false)
        
        // Test with different square sizes
        let squareSize1: CGFloat = 100
        let e4Point1 = transformer.squareToPosition(.e4, squareSize: squareSize1)
        let backToSquare1 = transformer.positionToSquare(e4Point1, squareSize: squareSize1)
        XCTAssertEqual(backToSquare1, .e4)
        
        let squareSize2: CGFloat = 40
        let e4Point2 = transformer.squareToPosition(.e4, squareSize: squareSize2)
        let backToSquare2 = transformer.positionToSquare(e4Point2, squareSize: squareSize2)
        XCTAssertEqual(backToSquare2, .e4)
    }
    
    // MARK: - Edge Cases Tests
    
    func testBoardEdgeSquares() {
        let transformer = BoardCoordinateTransformer(isFlipped: false)
        let squareSize: CGFloat = 50
        
        // Test all corner squares
        let corners = [Square.a1, Square.a8, Square.h1, Square.h8]
        
        for corner in corners {
            let point = transformer.squareToPosition(corner, squareSize: squareSize)
            let backToSquare = transformer.positionToSquare(point, squareSize: squareSize)
            XCTAssertEqual(backToSquare, corner)
        }
    }
    
    func testAllSquareTransformations() {
        let transformer = BoardCoordinateTransformer(isFlipped: false)
        let squareSize: CGFloat = 50
        
        // Test all 64 squares
        for file in Square.File.allCases {
            for rank in Square.Rank.range {
                let square = Square("\(file)\(rank)")
                let point = transformer.squareToPosition(square, squareSize: squareSize)
                let backToSquare = transformer.positionToSquare(point, squareSize: squareSize)
                XCTAssertEqual(backToSquare, square, "Failed for square \(square)")
            }
        }
    }
    
    func testTransformationConsistency() {
        let normalTransformer = BoardCoordinateTransformer(isFlipped: false)
        let flippedTransformer = BoardCoordinateTransformer(isFlipped: true)
        let squareSize: CGFloat = 50
        let boardSize = squareSize * 8
        
        // Test that flipping twice returns to original
        for file in Square.File.allCases {
            for rank in Square.Rank.range {
                let square = Square("\(file)\(rank)")
                
                let normalPoint = normalTransformer.squareToPosition(square, squareSize: squareSize)
                let flippedPoint = flippedTransformer.squareToPosition(square, squareSize: squareSize)
                
                // Flipped coordinates should be mirror image
                XCTAssertEqual(normalPoint.x + flippedPoint.x, boardSize - squareSize, accuracy: 1.0)
                XCTAssertEqual(normalPoint.y + flippedPoint.y, boardSize - squareSize, accuracy: 1.0)
            }
        }
    }
    
    // MARK: - Performance Tests
    
    func testCoordinateTransformPerformance() {
        let transformer = BoardCoordinateTransformer(isFlipped: false)
        let squareSize: CGFloat = 50
        
        measure {
            for _ in 0..<100 {
                for file in Square.File.allCases {
                    for rank in Square.Rank.range {
                        let square = Square("\(file)\(rank)")
                        let point = transformer.squareToPosition(square, squareSize: squareSize)
                        _ = transformer.positionToSquare(point, squareSize: squareSize)
                    }
                }
            }
        }
    }
    
    func testPointToSquarePerformance() {
        let transformer = BoardCoordinateTransformer(isFlipped: false)
        let squareSize: CGFloat = 50
        let testPoints = [
            CGPoint(x: 25, y: 25),
            CGPoint(x: 125, y: 125),
            CGPoint(x: 225, y: 225),
            CGPoint(x: 325, y: 325),
            CGPoint(x: 375, y: 375)
        ]
        
        measure {
            for _ in 0..<100 {
                for point in testPoints {
                    _ = transformer.positionToSquare(point, squareSize: squareSize)
                }
            }
        }
    }
    
    // MARK: - Validation Tests
    
    func testCoordinateRangeValidation() {
        let transformer = BoardCoordinateTransformer(isFlipped: false)
        let squareSize: CGFloat = 50
        
        // Test points at exact boundaries
        let boundaryPoints = [
            CGPoint(x: 0, y: 0),
            CGPoint(x: 0, y: 400),
            CGPoint(x: 400, y: 0),
            CGPoint(x: 400, y: 400)
        ]
        
        for point in boundaryPoints {
            let square = transformer.positionToSquare(point, squareSize: squareSize)
            // Boundary points should either return nil or a valid square
            if let square = square {
                XCTAssertTrue(Square.File.allCases.contains(square.file))
                XCTAssertTrue(Square.Rank.range.contains(square.rank.value))
            }
        }
    }
    
    func testNegativeCoordinates() {
        let transformer = BoardCoordinateTransformer(isFlipped: false)
        let squareSize: CGFloat = 50
        
        let negativePoints = [
            CGPoint(x: -1, y: 200),
            CGPoint(x: 200, y: -1),
            CGPoint(x: -1, y: -1)
        ]
        
        for point in negativePoints {
            let square = transformer.positionToSquare(point, squareSize: squareSize)
            XCTAssertNil(square, "Negative coordinates should return nil")
        }
    }
    
    func testVeryLargeCoordinates() {
        let transformer = BoardCoordinateTransformer(isFlipped: false)
        let squareSize: CGFloat = 50
        
        let largePoints = [
            CGPoint(x: 1000, y: 200),
            CGPoint(x: 200, y: 1000),
            CGPoint(x: 1000, y: 1000)
        ]
        
        for point in largePoints {
            let square = transformer.positionToSquare(point, squareSize: squareSize)
            XCTAssertNil(square, "Very large coordinates should return nil")
        }
    }
}
