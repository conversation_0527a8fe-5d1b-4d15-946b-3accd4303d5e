//
//  ChessFileManagerTests.swift
//  MacChessBaseTests
//
//  Created by <PERSON> on 2025/7/9.
//

import XCTest
import UniformTypeIdentifiers
@testable import MacChessBase
@testable import ChessKit

@MainActor
final class ChessFileManagerTests: XCTestCase {
    
    var fileManager: ChessFileManager!
    var tempDirectory: URL!
    
    override func setUp() {
        super.setUp()
        fileManager = ChessFileManager()
        
        // Create a temporary directory for test files
        tempDirectory = FileManager.default.temporaryDirectory
            .appendingPathComponent("ChessFileManagerTests")
            .appendingPathComponent(UUID().uuidString)
        
        do {
            try FileManager.default.createDirectory(at: tempDirectory, withIntermediateDirectories: true)
        } catch {
            XCTFail("Failed to create temp directory: \(error)")
        }
    }
    
    override func tearDown() {
        // Clean up temp directory
        try? FileManager.default.removeItem(at: tempDirectory)
        fileManager = nil
        tempDirectory = nil
        super.tearDown()
    }
    
    // MARK: - File Type Validation Tests
    
    func testValidFileTypes() {
        let pgnURL = tempDirectory.appendingPathComponent("test.pgn")
        let fenURL = tempDirectory.appendingPathComponent("test.fen")
        
        XCTAssertTrue(fileManager.isValidFileType(url: pgnURL))
        XCTAssertTrue(fileManager.isValidFileType(url: fenURL))
        
        // Test case insensitivity
        let pgnUpperURL = tempDirectory.appendingPathComponent("test.PGN")
        let fenUpperURL = tempDirectory.appendingPathComponent("test.FEN")
        
        XCTAssertTrue(fileManager.isValidFileType(url: pgnUpperURL))
        XCTAssertTrue(fileManager.isValidFileType(url: fenUpperURL))
    }
    
    func testInvalidFileTypes() {
        let invalidURLs = [
            tempDirectory.appendingPathComponent("test.txt"),
            tempDirectory.appendingPathComponent("test.doc"),
            tempDirectory.appendingPathComponent("test.jpg"),
            tempDirectory.appendingPathComponent("test")
        ]
        
        for url in invalidURLs {
            XCTAssertFalse(fileManager.isValidFileType(url: url))
        }
    }
    
    // MARK: - PGN File Reading Tests
    
    func testReadValidPGNFile() throws {
        let pgnContent = """
        [Event "Test Tournament"]
        [Site "Test Site"]
        [Date "2023.01.01"]
        [Round "1"]
        [White "Player One"]
        [Black "Player Two"]
        [Result "1-0"]
        
        1. e4 e5 2. Nf3 Nc6 3. Bb5 a6 4. Ba4 Nf6 5. O-O Be7 1-0
        """
        
        let pgnURL = tempDirectory.appendingPathComponent("test.pgn")
        try pgnContent.write(to: pgnURL, atomically: true, encoding: .utf8)
        
        let game = try fileManager.readGame(from: pgnURL)
        
        XCTAssertEqual(game.metadata.event, "Test Tournament")
        XCTAssertEqual(game.metadata.white, "Player One")
        XCTAssertEqual(game.metadata.black, "Player Two")
        XCTAssertEqual(game.metadata.result, "1-0")
    }
    
    func testReadPGNContentFromFile() throws {
        let pgnContent = """
        [Event "Test Tournament"]
        [Site "Test Site"]
        [Date "2023.01.01"]
        [Round "1"]
        [White "Player One"]
        [Black "Player Two"]
        [Result "1-0"]
        
        1. e4 e5 2. Nf3 Nc6 1-0
        """
        
        let pgnURL = tempDirectory.appendingPathComponent("test.pgn")
        try pgnContent.write(to: pgnURL, atomically: true, encoding: .utf8)
        
        let readContent = try fileManager.readPGNContent(from: pgnURL)
        XCTAssertEqual(readContent, pgnContent)
    }
    
    func testReadInvalidPGNFile() throws {
        let invalidPGN = "This is not a valid PGN file"
        let pgnURL = tempDirectory.appendingPathComponent("invalid.pgn")
        try invalidPGN.write(to: pgnURL, atomically: true, encoding: .utf8)
        
        XCTAssertThrowsError(try fileManager.readGame(from: pgnURL)) { error in
            XCTAssertEqual(error as? ChessFileManager.FileError, .invalidPGN)
        }
    }
    
    // MARK: - FEN File Reading Tests
    
    func testReadValidFENFile() throws {
        let fenContent = "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1"
        let fenURL = tempDirectory.appendingPathComponent("test.fen")
        try fenContent.write(to: fenURL, atomically: true, encoding: .utf8)
        
        let game = try fileManager.readGame(from: fenURL)
        
        // The game should start with the position from the FEN
        XCTAssertEqual(game.tags.fen, fenContent)
        XCTAssertEqual(game.tags.setUp, "1")
    }
    
    func testReadStandardStartingPositionFEN() throws {
        let standardFEN = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
        let fenURL = tempDirectory.appendingPathComponent("standard.fen")
        try standardFEN.write(to: fenURL, atomically: true, encoding: .utf8)
        
        let game = try fileManager.readGame(from: fenURL)
        
        // For standard position, FEN and SetUp tags should not be set
        XCTAssertTrue(game.tags.fen.isEmpty)
        XCTAssertTrue(game.tags.setUp.isEmpty)
    }
    
    func testReadInvalidFENFile() throws {
        let invalidFEN = "this is not a valid FEN"
        let fenURL = tempDirectory.appendingPathComponent("invalid.fen")
        try invalidFEN.write(to: fenURL, atomically: true, encoding: .utf8)
        
        XCTAssertThrowsError(try fileManager.readGame(from: fenURL)) { error in
            XCTAssertEqual(error as? ChessFileManager.FileError, .invalidFEN)
        }
    }
    
    // MARK: - File Error Tests
    
    func testReadNonexistentFile() {
        let nonexistentURL = tempDirectory.appendingPathComponent("nonexistent.pgn")
        
        XCTAssertThrowsError(try fileManager.readGame(from: nonexistentURL)) { error in
            if case ChessFileManager.FileError.readingFailed = error {
                // Expected
            } else {
                XCTFail("Expected readingFailed error, got \(error)")
            }
        }
    }
    
    func testReadEmptyFile() throws {
        let emptyURL = tempDirectory.appendingPathComponent("empty.pgn")
        try "".write(to: emptyURL, atomically: true, encoding: .utf8)
        
        XCTAssertThrowsError(try fileManager.readGame(from: emptyURL)) { error in
            XCTAssertEqual(error as? ChessFileManager.FileError, .emptyContent)
        }
    }
    
    func testReadFileWithOnlyWhitespace() throws {
        let whitespaceURL = tempDirectory.appendingPathComponent("whitespace.pgn")
        try "   \n\t  \r\n  ".write(to: whitespaceURL, atomically: true, encoding: .utf8)
        
        XCTAssertThrowsError(try fileManager.readGame(from: whitespaceURL)) { error in
            XCTAssertEqual(error as? ChessFileManager.FileError, .emptyContent)
        }
    }
    
    func testReadInvalidFileType() {
        let invalidURL = tempDirectory.appendingPathComponent("test.txt")
        
        XCTAssertThrowsError(try fileManager.readGame(from: invalidURL)) { error in
            XCTAssertEqual(error as? ChessFileManager.FileError, .invalidFileType)
        }
    }
    
    // MARK: - File Writing Tests
    
    func testSaveGameToPGNFile() throws {
        var game = Game()
        // Add minimal metadata to ensure PGN is not empty
        game.metadata = Game.Metadata(result: "*")
        
        let pgnURL = tempDirectory.appendingPathComponent("saved.pgn")
        
        try fileManager.saveGame(game, to: pgnURL)
        
        XCTAssertTrue(FileManager.default.fileExists(atPath: pgnURL.path))
        
        // Verify content
        let savedContent = try String(contentsOf: pgnURL, encoding: .utf8)
        XCTAssertFalse(savedContent.isEmpty)
        XCTAssertTrue(savedContent.contains("[Result \"*\"]"))
    }
    
    func testSaveGameWithMetadata() throws {
        var game = Game()
        game.metadata = Game.Metadata(
            white: "Test White",
            black: "Test Black",
            event: "Test Event",
            site: "Test Site",
            date: "2023.01.01",
            round: "1",
            result: "1-0"
        )
        
        let pgnURL = tempDirectory.appendingPathComponent("with_metadata.pgn")
        
        try fileManager.saveGame(game, to: pgnURL)
        
        let savedContent = try String(contentsOf: pgnURL, encoding: .utf8)
        XCTAssertTrue(savedContent.contains("[White \"Test White\"]"))
        XCTAssertTrue(savedContent.contains("[Black \"Test Black\"]"))
        XCTAssertTrue(savedContent.contains("[Event \"Test Event\"]"))
        XCTAssertTrue(savedContent.contains("[Result \"1-0\"]"))
    }
    
    func testSaveGameWithCustomFEN() throws {
        var game = Game()
        let customFEN = "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1"
        game.tags.fen = customFEN
        game.tags.setUp = "1"
        
        let pgnURL = tempDirectory.appendingPathComponent("custom_fen.pgn")
        
        try fileManager.saveGame(game, to: pgnURL)
        
        let savedContent = try String(contentsOf: pgnURL, encoding: .utf8)
        XCTAssertTrue(savedContent.contains("[FEN \"\(customFEN)\"]"))
        XCTAssertTrue(savedContent.contains("[SetUp \"1\"]"))
    }
    
    func testSavePGNContent() throws {
        let pgnContent = "[Event \"Test\"]\n\n1. e4 e5 *"
        let pgnURL = tempDirectory.appendingPathComponent("content.pgn")
        
        try fileManager.savePGNContent(pgnContent, to: pgnURL)
        
        let savedContent = try String(contentsOf: pgnURL, encoding: .utf8)
        XCTAssertEqual(savedContent, pgnContent)
    }
    
    func testSaveToReadOnlyLocation() {
        let game = Game()
        let readOnlyURL = URL(fileURLWithPath: "/")
            .appendingPathComponent("readonly.pgn")
        
        XCTAssertThrowsError(try fileManager.saveGame(game, to: readOnlyURL)) { error in
            if case ChessFileManager.FileError.writingFailed = error {
                // Expected
            } else {
                XCTFail("Expected writingFailed error, got \(error)")
            }
        }
    }
    
    // MARK: - Clipboard Tests
    
    func testCopyPGNToClipboard() {
        let pgnContent = "[Event \"Test\"]\n\n1. e4 e5 *"
        
        let success = fileManager.copyPGNToClipboard(pgnContent)
        XCTAssertTrue(success)
        
        // Verify clipboard content
        let pasteboard = NSPasteboard.general
        let clipboardContent = pasteboard.string(forType: .string)
        XCTAssertEqual(clipboardContent, pgnContent)
    }
    
    func testCopyGameToClipboard() {
        var game = Game()
        // Add some metadata to ensure PGN is not empty
        game.metadata = Game.Metadata(
            white: "Player 1",
            black: "Player 2",
            result: "*"
        )
        
        let success = fileManager.copyGameToClipboard(game)
        XCTAssertTrue(success)
        
        // Verify clipboard contains PGN
        let pasteboard = NSPasteboard.general
        let clipboardContent = pasteboard.string(forType: .string)
        XCTAssertNotNil(clipboardContent)
        XCTAssertFalse(clipboardContent!.isEmpty)
        XCTAssertTrue(clipboardContent!.contains("[White \"Player 1\"]"))
    }
    
    func testCopyEmptyPGNToClipboard() {
        let success = fileManager.copyPGNToClipboard("")
        XCTAssertFalse(success)
    }
    
    func testReadGameFromClipboard() {
        // Set up clipboard with valid PGN
        let pgnContent = """
        [Event "Test"]
        [White "Player1"]
        [Black "Player2"]
        [Result "*"]
        
        1. e4 e5 *
        """
        
        let pasteboard = NSPasteboard.general
        pasteboard.declareTypes([.string], owner: nil)
        pasteboard.setString(pgnContent, forType: .string)
        
        let game = fileManager.readGameFromClipboard()
        XCTAssertNotNil(game)
        XCTAssertEqual(game?.metadata.event, "Test")
    }
    
    func testReadFENFromClipboard() {
        // Set up clipboard with valid FEN
        let fenContent = "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1"
        
        let pasteboard = NSPasteboard.general
        pasteboard.declareTypes([.string], owner: nil)
        pasteboard.setString(fenContent, forType: .string)
        
        let game = fileManager.readGameFromClipboard()
        XCTAssertNotNil(game)
        XCTAssertEqual(game?.tags.fen, fenContent)
        XCTAssertEqual(game?.tags.setUp, "1")
    }
    
    func testReadInvalidContentFromClipboard() {
        // Set up clipboard with invalid content
        let invalidContent = "This is not PGN or FEN"
        
        let pasteboard = NSPasteboard.general
        pasteboard.declareTypes([.string], owner: nil)
        pasteboard.setString(invalidContent, forType: .string)
        
        let game = fileManager.readGameFromClipboard()
        XCTAssertNil(game)
    }
    
    func testReadPGNFromEmptyClipboard() {
        // Clear clipboard
        let pasteboard = NSPasteboard.general
        pasteboard.clearContents()
        
        let pgnContent = fileManager.readPGNFromClipboard()
        XCTAssertNil(pgnContent)
    }
    
    // MARK: - Filename Generation Tests
    
    func testGenerateSuggestedFilenameForGame() {
        var game = Game()
        game.metadata = Game.Metadata(
            white: "Magnus Carlsen",
            black: "Hikaru Nakamura",
            event: "Speed Chess Championship",
            date: "2023.12.25"
        )
        
        let filename = fileManager.generateSuggestedFilename(for: game)
        XCTAssertEqual(filename, "Magnus_Carlsen - Hikaru_Nakamura_Speed_Chess_Championship_2023-12-25")
    }
    
    func testGenerateSuggestedFilenameForGameWithMetadata() {
        var game = Game()
        game.metadata = Game.Metadata(
            white: "Test Player",
            black: "Another Player",
            event: "Test Tournament"
        )
        
        let filename = fileManager.generateSuggestedFilename(for: game)
        XCTAssertEqual(filename, "Test_Player - Another_Player_Test_Tournament")
    }
    
    func testGenerateSuggestedFilenameWithSpecialCharacters() {
        var game = Game()
        game.metadata = Game.Metadata(
            white: "Player/One",
            black: "Player:Two",
            event: "Tournament*2023"
        )
        
        let filename = fileManager.generateSuggestedFilename(for: game)
        XCTAssertEqual(filename, "Player-One - Player-Two_Tournament2023")
    }
    
    func testGenerateSuggestedFilenameWithEmptyFields() {
        let game = Game()
        
        let filename = fileManager.generateSuggestedFilename(for: game)
        XCTAssertEqual(filename, "chess_game")
    }
    
    func testGenerateSuggestedFilenameWithOnlyOnePlayer() {
        var game = Game()
        game.metadata = Game.Metadata(white: "Solo Player")
        
        let filename = fileManager.generateSuggestedFilename(for: game)
        XCTAssertEqual(filename, "Solo_Player - ?")
    }
    
    // MARK: - Utility Tests
    
    func testSupportedFileTypes() {
        XCTAssertEqual(ChessFileManager.SupportedFileType.pgn.fileExtension, "pgn")
        XCTAssertEqual(ChessFileManager.SupportedFileType.fen.fileExtension, "fen")
        
        XCTAssertEqual(ChessFileManager.SupportedFileType.pgn.description, "Portable Game Notation")
        XCTAssertEqual(ChessFileManager.SupportedFileType.fen.description, "Forsyth-Edwards Notation")
        
        XCTAssertNotNil(ChessFileManager.SupportedFileType.pgn.utType)
        XCTAssertNotNil(ChessFileManager.SupportedFileType.fen.utType)
    }
    
    func testFileErrorDescriptions() {
        let errors: [ChessFileManager.FileError] = [
            .invalidFileType,
            .readingFailed("test error"),
            .writingFailed("test error"),
            .invalidPGN,
            .invalidFEN,
            .emptyContent
        ]
        
        for error in errors {
            XCTAssertNotNil(error.errorDescription)
            XCTAssertFalse(error.errorDescription!.isEmpty)
        }
    }
    
    func testSharedInstance() {
        let instance1 = ChessFileManager.shared
        let instance2 = ChessFileManager.shared
        
        XCTAssertTrue(instance1 === instance2)
    }
    
    // MARK: - Integration Tests
    
    func testRoundTripSaveAndLoad() throws {
        // Create a game with some metadata
        var originalGame = Game()
        originalGame.metadata = Game.Metadata(
            white: "Test White",
            black: "Test Black",
            event: "Test Event",
            result: "1-0"
        )
        
        // Save the game
        let pgnURL = tempDirectory.appendingPathComponent("roundtrip.pgn")
        try fileManager.saveGame(originalGame, to: pgnURL)
        
        // Load the game back
        let loadedGame = try fileManager.readGame(from: pgnURL)
        
        // Verify they match
        XCTAssertEqual(loadedGame.metadata.white, originalGame.metadata.white)
        XCTAssertEqual(loadedGame.metadata.black, originalGame.metadata.black)
        XCTAssertEqual(loadedGame.metadata.event, originalGame.metadata.event)
        XCTAssertEqual(loadedGame.metadata.result, originalGame.metadata.result)
    }
    
    func testClipboardRoundTrip() {
        var originalGame = Game()
        // Add some metadata to ensure PGN is not empty
        originalGame.metadata = Game.Metadata(
            white: "Test Player 1",
            black: "Test Player 2",
            result: "*"
        )
        
        // Copy to clipboard
        let copySuccess = fileManager.copyGameToClipboard(originalGame)
        XCTAssertTrue(copySuccess)
        
        // Read from clipboard
        let loadedGame = fileManager.readGameFromClipboard()
        XCTAssertNotNil(loadedGame)
        
        // Both should have the same metadata
        XCTAssertEqual(loadedGame?.metadata.white, originalGame.metadata.white)
        XCTAssertEqual(loadedGame?.metadata.black, originalGame.metadata.black)
        XCTAssertEqual(loadedGame?.metadata.result, originalGame.metadata.result)
    }
    
    // MARK: - Performance Tests
    
    func testFileReadingPerformance() throws {
        // Create a large PGN file
        var largePGN = """
        [Event "Performance Test"]
        [White "Player1"]
        [Black "Player2"]
        [Result "*"]
        
        """
        
        // Add many moves
        for i in 1...1000 {
            largePGN += "\(i). e4 e5 "
        }
        largePGN += "*"
        
        let pgnURL = tempDirectory.appendingPathComponent("large.pgn")
        try largePGN.write(to: pgnURL, atomically: true, encoding: .utf8)
        
        measure {
            do {
                _ = try fileManager.readGame(from: pgnURL)
            } catch {
                XCTFail("Performance test failed: \(error)")
            }
        }
    }
    
    func testFileWritingPerformance() {
        let game = Game()
        
        measure {
            let pgnURL = tempDirectory.appendingPathComponent("perf_\(UUID().uuidString).pgn")
            do {
                try fileManager.saveGame(game, to: pgnURL)
            } catch {
                XCTFail("Performance test failed: \(error)")
            }
        }
    }
    
    func testFilenameGenerationPerformance() {
        var game = Game()
        game.metadata = Game.Metadata(
            white: "Very Long Player Name With Many Words",
            black: "Another Very Long Player Name With Special Characters!@#$%",
            event: "Super Long Tournament Name With Lots Of Detail 2023 World Championship Final"
        )
        
        measure {
            for _ in 0..<1000 {
                _ = fileManager.generateSuggestedFilename(for: game)
            }
        }
    }
}
