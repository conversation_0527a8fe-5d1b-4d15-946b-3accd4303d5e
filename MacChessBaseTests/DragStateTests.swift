//
//  DragStateTests.swift
//  MacChessBaseTests
//
//  Created by <PERSON> on 2025/7/9.
//

import XCTest
import SwiftUI
@testable import MacChessBase
@testable import ChessKit

@MainActor
final class DragStateTests: XCTestCase {
    
    var dragState: DragState!
    
    override func setUp() {
        super.setUp()
        dragState = DragState()
    }
    
    override func tearDown() {
        dragState = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testInitialState() {
        XCTAssertNil(dragState.draggedPiece)
        XCTAssertEqual(dragState.dragPosition, .zero)
        XCTAssertNil(dragState.reverseDraggedPiece)
        XCTAssertEqual(dragState.reverseDragPosition, .zero)
    }
    
    // MARK: - Normal Drag Tests
    
    func testStartDrag() {
        let piece = Piece(.pawn, color: .white, square: .a1)
        let square = Square.e2
        let position = CGPoint(x: 100, y: 200)
        
        dragState.startDrag(piece: piece, from: square, at: position)
        
        XCTAssertNotNil(dragState.draggedPiece)
        XCTAssertEqual(dragState.draggedPiece?.piece, piece)
        XCTAssertEqual(dragState.draggedPiece?.fromSquare, square)
        XCTAssertEqual(dragState.dragPosition, position)
    }
    
    func testUpdateDrag() {
        let piece = Piece(.pawn, color: .white, square: .a1)
        let square = Square.e2
        let initialPosition = CGPoint(x: 100, y: 200)
        let updatedPosition = CGPoint(x: 150, y: 250)
        
        dragState.startDrag(piece: piece, from: square, at: initialPosition)
        dragState.updateDrag(to: updatedPosition)
        
        XCTAssertEqual(dragState.dragPosition, updatedPosition)
        // Piece and square should remain unchanged
        XCTAssertEqual(dragState.draggedPiece?.piece, piece)
        XCTAssertEqual(dragState.draggedPiece?.fromSquare, square)
    }
    
    func testEndDrag() {
        let piece = Piece(.pawn, color: .white, square: .a1)
        let square = Square.e2
        let position = CGPoint(x: 100, y: 200)
        
        dragState.startDrag(piece: piece, from: square, at: position)
        dragState.endDrag()
        
        XCTAssertNil(dragState.draggedPiece)
        XCTAssertEqual(dragState.dragPosition, .zero)
    }
    
    func testIsDraggedFrom() {
        let piece = Piece(.pawn, color: .white, square: .a1)
        let square = Square.e2
        let otherSquare = Square.e4
        let position = CGPoint(x: 100, y: 200)
        
        // Initially no piece is being dragged
        XCTAssertFalse(dragState.isDraggedFrom(square))
        
        dragState.startDrag(piece: piece, from: square, at: position)
        
        // Now the piece is being dragged from the square
        XCTAssertTrue(dragState.isDraggedFrom(square))
        XCTAssertFalse(dragState.isDraggedFrom(otherSquare))
        
        dragState.endDrag()
        
        // After ending drag, no piece is being dragged
        XCTAssertFalse(dragState.isDraggedFrom(square))
    }
    
    // MARK: - Reverse Drag Tests
    
    func testStartReverseDrag() {
        let piece = Piece(.pawn, color: .black, square: .a1)
        let square = Square.e7
        let position = CGPoint(x: 300, y: 400)
        
        dragState.startReverseDrag(piece: piece, from: square, at: position)
        
        XCTAssertNotNil(dragState.reverseDraggedPiece)
        XCTAssertEqual(dragState.reverseDraggedPiece?.piece, piece)
        XCTAssertEqual(dragState.reverseDraggedPiece?.fromSquare, square)
        XCTAssertEqual(dragState.reverseDragPosition, position)
    }
    
    func testUpdateReverseDrag() {
        let piece = Piece(.pawn, color: .black, square: .a1)
        let square = Square.e7
        let initialPosition = CGPoint(x: 300, y: 400)
        let updatedPosition = CGPoint(x: 350, y: 450)
        
        dragState.startReverseDrag(piece: piece, from: square, at: initialPosition)
        dragState.updateReverseDrag(to: updatedPosition)
        
        XCTAssertEqual(dragState.reverseDragPosition, updatedPosition)
        // Piece and square should remain unchanged
        XCTAssertEqual(dragState.reverseDraggedPiece?.piece, piece)
        XCTAssertEqual(dragState.reverseDraggedPiece?.fromSquare, square)
    }
    
    func testEndReverseDrag() {
        let piece = Piece(.pawn, color: .black, square: .a1)
        let square = Square.e7
        let position = CGPoint(x: 300, y: 400)
        
        dragState.startReverseDrag(piece: piece, from: square, at: position)
        dragState.endReverseDrag()
        
        XCTAssertNil(dragState.reverseDraggedPiece)
        XCTAssertEqual(dragState.reverseDragPosition, .zero)
    }
    
    func testIsReverseDraggedFrom() {
        let piece = Piece(.pawn, color: .black, square: .a1)
        let square = Square.e7
        let otherSquare = Square.e5
        let position = CGPoint(x: 300, y: 400)
        
        // Initially no piece is being reverse dragged
        XCTAssertFalse(dragState.isReverseDraggedFrom(square))
        
        dragState.startReverseDrag(piece: piece, from: square, at: position)
        
        // Now the piece is being reverse dragged from the square
        XCTAssertTrue(dragState.isReverseDraggedFrom(square))
        XCTAssertFalse(dragState.isReverseDraggedFrom(otherSquare))
        
        dragState.endReverseDrag()
        
        // After ending reverse drag, no piece is being reverse dragged
        XCTAssertFalse(dragState.isReverseDraggedFrom(square))
    }
    
    // MARK: - Cancel Drag Tests
    
    func testCancelDragClearsNormalDrag() {
        let piece = Piece(.pawn, color: .white, square: .a1)
        let square = Square.e2
        let position = CGPoint(x: 100, y: 200)
        
        dragState.startDrag(piece: piece, from: square, at: position)
        dragState.cancelDrag()
        
        XCTAssertNil(dragState.draggedPiece)
        XCTAssertEqual(dragState.dragPosition, .zero)
    }
    
    func testCancelDragClearsReverseDrag() {
        let piece = Piece(.pawn, color: .black, square: .a1)
        let square = Square.e7
        let position = CGPoint(x: 300, y: 400)
        
        dragState.startReverseDrag(piece: piece, from: square, at: position)
        dragState.cancelDrag()
        
        XCTAssertNil(dragState.reverseDraggedPiece)
        XCTAssertEqual(dragState.reverseDragPosition, .zero)
    }
    
    func testCancelDragClearsBothDrags() {
        let piece1 = Piece(.pawn, color: .white, square: .a1)
        let square1 = Square.e2
        let position1 = CGPoint(x: 100, y: 200)
        
        let piece2 = Piece(.pawn, color: .black, square: .a1)
        let square2 = Square.e7
        let position2 = CGPoint(x: 300, y: 400)
        
        dragState.startDrag(piece: piece1, from: square1, at: position1)
        dragState.startReverseDrag(piece: piece2, from: square2, at: position2)
        
        dragState.cancelDrag()
        
        XCTAssertNil(dragState.draggedPiece)
        XCTAssertEqual(dragState.dragPosition, .zero)
        XCTAssertNil(dragState.reverseDraggedPiece)
        XCTAssertEqual(dragState.reverseDragPosition, .zero)
    }
    
    // MARK: - Simultaneous Drag Tests
    
    func testSimultaneousDragOperations() {
        let normalPiece = Piece(.rook, color: .white, square: .a1)
        let normalSquare = Square.a1
        let normalPosition = CGPoint(x: 100, y: 100)
        
        let reversePiece = Piece(.rook, color: .black, square: .a1)
        let reverseSquare = Square.a8
        let reversePosition = CGPoint(x: 200, y: 200)
        
        // Start both drag operations
        dragState.startDrag(piece: normalPiece, from: normalSquare, at: normalPosition)
        dragState.startReverseDrag(piece: reversePiece, from: reverseSquare, at: reversePosition)
        
        // Both should be active
        XCTAssertNotNil(dragState.draggedPiece)
        XCTAssertNotNil(dragState.reverseDraggedPiece)
        XCTAssertTrue(dragState.isDraggedFrom(normalSquare))
        XCTAssertTrue(dragState.isReverseDraggedFrom(reverseSquare))
        
        // Update positions
        let newNormalPosition = CGPoint(x: 150, y: 150)
        let newReversePosition = CGPoint(x: 250, y: 250)
        
        dragState.updateDrag(to: newNormalPosition)
        dragState.updateReverseDrag(to: newReversePosition)
        
        XCTAssertEqual(dragState.dragPosition, newNormalPosition)
        XCTAssertEqual(dragState.reverseDragPosition, newReversePosition)
        
        // End one drag operation
        dragState.endDrag()
        
        XCTAssertNil(dragState.draggedPiece)
        XCTAssertNotNil(dragState.reverseDraggedPiece)
        
        // End the other
        dragState.endReverseDrag()
        
        XCTAssertNil(dragState.reverseDraggedPiece)
    }
    
    // MARK: - Edge Cases Tests
    
    func testMultipleStartDragOperations() {
        let piece1 = Piece(.pawn, color: .white, square: .a1)
        let square1 = Square.e2
        let position1 = CGPoint(x: 100, y: 200)
        
        let piece2 = Piece(.knight, color: .white, square: .a1)
        let square2 = Square.g1
        let position2 = CGPoint(x: 300, y: 400)
        
        dragState.startDrag(piece: piece1, from: square1, at: position1)
        dragState.startDrag(piece: piece2, from: square2, at: position2)
        
        // Second start should replace the first
        XCTAssertEqual(dragState.draggedPiece?.piece, piece2)
        XCTAssertEqual(dragState.draggedPiece?.fromSquare, square2)
        XCTAssertEqual(dragState.dragPosition, position2)
        
        // Should no longer be dragged from first square
        XCTAssertFalse(dragState.isDraggedFrom(square1))
        XCTAssertTrue(dragState.isDraggedFrom(square2))
    }
    
    func testEndDragWithoutStartDrag() {
        // Ending drag without starting should not crash
        XCTAssertNoThrow(dragState.endDrag())
        XCTAssertNoThrow(dragState.endReverseDrag())
        XCTAssertNoThrow(dragState.cancelDrag())
        
        // State should remain clean
        XCTAssertNil(dragState.draggedPiece)
        XCTAssertNil(dragState.reverseDraggedPiece)
    }
    
    func testUpdateDragWithoutStartDrag() {
        let position = CGPoint(x: 100, y: 200)
        
        // Updating position without starting drag should work but have no effect on piece
        XCTAssertNoThrow(dragState.updateDrag(to: position))
        XCTAssertNoThrow(dragState.updateReverseDrag(to: position))
        
        XCTAssertNil(dragState.draggedPiece)
        XCTAssertNil(dragState.reverseDraggedPiece)
        XCTAssertEqual(dragState.dragPosition, position)
        XCTAssertEqual(dragState.reverseDragPosition, position)
    }
    
    // MARK: - Different Piece Types Tests
    
    func testDragAllPieceTypes() {
        let pieceTypes: [Piece.Kind] = [.pawn, .rook, .knight, .bishop, .queen, .king]
        let colors: [Piece.Color] = [.white, .black]
        
        for kind in pieceTypes {
            for color in colors {
                let piece = Piece(kind, color: color, square: .a1)
                let square = Square.e4
                let position = CGPoint(x: 200, y: 200)
                
                dragState.startDrag(piece: piece, from: square, at: position)
                
                XCTAssertEqual(dragState.draggedPiece?.piece.kind, kind)
                XCTAssertEqual(dragState.draggedPiece?.piece.color, color)
                XCTAssertTrue(dragState.isDraggedFrom(square))
                
                dragState.endDrag()
            }
        }
    }
    
    func testDragFromAllSquares() {
        let piece = Piece(.pawn, color: .white, square: .a1)
        let position = CGPoint(x: 100, y: 100)
        
        for file in Square.File.allCases {
            for rank in Square.Rank.range {
                let square = Square("\(file)\(rank)")
                
                dragState.startDrag(piece: piece, from: square, at: position)
                
                XCTAssertTrue(dragState.isDraggedFrom(square))
                
                // Should not be dragged from other squares
                let otherSquare = Square.e4
                if square != otherSquare {
                    XCTAssertFalse(dragState.isDraggedFrom(otherSquare))
                }
                
                dragState.endDrag()
            }
        }
    }
    
    // MARK: - Position Tests
    
    func testPositionPrecision() {
        let piece = Piece(.queen, color: .white, square: .a1)
        let square = Square.d4
        let precisePosition = CGPoint(x: 123.456, y: 789.012)
        
        dragState.startDrag(piece: piece, from: square, at: precisePosition)
        
        XCTAssertEqual(dragState.dragPosition.x, precisePosition.x, accuracy: 0.001)
        XCTAssertEqual(dragState.dragPosition.y, precisePosition.y, accuracy: 0.001)
    }
    
    func testNegativePositions() {
        let piece = Piece(.bishop, color: .black, square: .a1)
        let square = Square.c3
        let negativePosition = CGPoint(x: -50, y: -100)
        
        dragState.startDrag(piece: piece, from: square, at: negativePosition)
        
        XCTAssertEqual(dragState.dragPosition, negativePosition)
        XCTAssertTrue(dragState.isDraggedFrom(square))
    }
    
    func testLargePositions() {
        let piece = Piece(.king, color: .white, square: .a1)
        let square = Square.h8
        let largePosition = CGPoint(x: 10000, y: 20000)
        
        dragState.startDrag(piece: piece, from: square, at: largePosition)
        
        XCTAssertEqual(dragState.dragPosition, largePosition)
        XCTAssertTrue(dragState.isDraggedFrom(square))
    }
    
    // MARK: - Performance Tests
    
    func testDragPerformance() {
        let piece = Piece(.pawn, color: .white, square: .a1)
        let square = Square.e2
        
        measure {
            for i in 0..<1000 {
                let position = CGPoint(x: Double(i), y: Double(i))
                dragState.startDrag(piece: piece, from: square, at: position)
                dragState.updateDrag(to: position)
                dragState.endDrag()
            }
        }
    }
    
    func testSimultaneousDragPerformance() {
        let normalPiece = Piece(.rook, color: .white, square: .a1)
        let reversePiece = Piece(.rook, color: .black, square: .a1)
        let normalSquare = Square.a1
        let reverseSquare = Square.a8
        
        measure {
            for i in 0..<100 {
                let position = CGPoint(x: Double(i), y: Double(i))
                dragState.startDrag(piece: normalPiece, from: normalSquare, at: position)
                dragState.startReverseDrag(piece: reversePiece, from: reverseSquare, at: position)
                dragState.cancelDrag()
            }
        }
    }
    
    // MARK: - ObservableObject Tests
    
    func testPublishedProperties() {
        // Test that the published properties can be observed
        let piece = Piece(.pawn, color: .white, square: .a1)
        let square = Square.e2
        let position = CGPoint(x: 100, y: 200)
        
        let expectation = XCTestExpectation(description: "Published property change")
        expectation.expectedFulfillmentCount = 3 // draggedPiece, dragPosition, and potentially others
        
        // Note: In a real app test, you'd subscribe to the publisher
        // For unit tests, we just verify the values change correctly
        
        XCTAssertNil(dragState.draggedPiece)
        
        dragState.startDrag(piece: piece, from: square, at: position)
        
        XCTAssertNotNil(dragState.draggedPiece)
        XCTAssertEqual(dragState.dragPosition, position)
        
        expectation.fulfill()
        expectation.fulfill()
        expectation.fulfill()
        
        wait(for: [expectation], timeout: 1.0)
    }
}
