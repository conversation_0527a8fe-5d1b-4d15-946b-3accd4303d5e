//
//  FENValidationTests.swift
//  MacChessBaseTests
//
//  Comprehensive tests for FEN validation system
//

import XCTest
@testable import MacChessBase
@testable import ChessKit

final class FENValidationTests: XCTestCase {

    // MARK: - Basic FEN Structure Tests
    
    func testValidStandardFEN() {
        let standardFEN = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
        XCTAssertTrue(FENValidator.isValidFEN(standardFEN))
    }
    
    func testInvalidFENStructure() {
        // Missing fields
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq"))
        
        // Too many fields
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1 extra"))
        
        // Empty string
        XCTAssertFalse(FENValidator.isValidFEN(""))
    }
    
    // MARK: - Piece Placement Tests
    
    func testValidPiecePlacement() {
        // Standard position
        XCTAssertTrue(FENValidator.isValidPiecePlacement("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR"))
        
        // Custom position
        XCTAssertTrue(FENValidator.isValidPiecePlacement("r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R"))
        
        // Empty board
        XCTAssertTrue(FENValidator.isValidPiecePlacement("8/8/8/8/8/8/8/8"))
    }
    
    func testInvalidPiecePlacement() {
        // Too many ranks
        XCTAssertFalse(FENValidator.isValidPiecePlacement("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR/8"))
        
        // Too few ranks
        XCTAssertFalse(FENValidator.isValidPiecePlacement("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP"))
        
        // Invalid characters
        XCTAssertFalse(FENValidator.isValidPiecePlacement("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKX2R"))
        
        // Rank too long
        XCTAssertFalse(FENValidator.isValidPiecePlacement("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR1"))
        
        // Rank too short
        XCTAssertFalse(FENValidator.isValidPiecePlacement("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKB"))
        
        // Consecutive numbers
        XCTAssertFalse(FENValidator.isValidPiecePlacement("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNB23KNR"))
    }
    
    // MARK: - Active Color Tests
    
    func testValidActiveColor() {
        XCTAssertTrue(FENValidator.isValidActiveColor("w"))
        XCTAssertTrue(FENValidator.isValidActiveColor("b"))
    }
    
    func testInvalidActiveColor() {
        XCTAssertFalse(FENValidator.isValidActiveColor("W"))
        XCTAssertFalse(FENValidator.isValidActiveColor("B"))
        XCTAssertFalse(FENValidator.isValidActiveColor("white"))
        XCTAssertFalse(FENValidator.isValidActiveColor("black"))
        XCTAssertFalse(FENValidator.isValidActiveColor(""))
        XCTAssertFalse(FENValidator.isValidActiveColor("x"))
    }
    
    // MARK: - Castling Rights Tests
    
    func testValidCastlingRights() {
        XCTAssertTrue(FENValidator.isValidCastlingRights("KQkq"))
        XCTAssertTrue(FENValidator.isValidCastlingRights("Kq"))
        XCTAssertTrue(FENValidator.isValidCastlingRights("Q"))
        XCTAssertTrue(FENValidator.isValidCastlingRights("k"))
        XCTAssertTrue(FENValidator.isValidCastlingRights("-"))
    }
    
    func testInvalidCastlingRights() {
        XCTAssertFalse(FENValidator.isValidCastlingRights("KQkqK")) // Duplicate
        XCTAssertFalse(FENValidator.isValidCastlingRights("KQkqX")) // Invalid character
        XCTAssertFalse(FENValidator.isValidCastlingRights("KQkq-")) // Mix of - and letters
        XCTAssertFalse(FENValidator.isValidCastlingRights("ABCD")) // Invalid letters
    }
    
    // MARK: - En Passant Tests
    
    func testValidEnPassant() {
        XCTAssertTrue(FENValidator.isValidEnPassant("-"))
        XCTAssertTrue(FENValidator.isValidEnPassant("e3"))
        XCTAssertTrue(FENValidator.isValidEnPassant("a6"))
        XCTAssertTrue(FENValidator.isValidEnPassant("h3"))
        XCTAssertTrue(FENValidator.isValidEnPassant("d6"))
    }
    
    func testInvalidEnPassant() {
        XCTAssertFalse(FENValidator.isValidEnPassant("e1")) // Wrong rank
        XCTAssertFalse(FENValidator.isValidEnPassant("e2")) // Wrong rank
        XCTAssertFalse(FENValidator.isValidEnPassant("e4")) // Wrong rank
        XCTAssertFalse(FENValidator.isValidEnPassant("e5")) // Wrong rank
        XCTAssertFalse(FENValidator.isValidEnPassant("e7")) // Wrong rank
        XCTAssertFalse(FENValidator.isValidEnPassant("e8")) // Wrong rank
        XCTAssertFalse(FENValidator.isValidEnPassant("i3")) // Invalid file
        XCTAssertFalse(FENValidator.isValidEnPassant("e33")) // Invalid format
        XCTAssertFalse(FENValidator.isValidEnPassant("")) // Empty
    }
    
    // MARK: - Move Counter Tests
    
    func testValidMoveCounters() {
        XCTAssertTrue(FENValidator.isValidHalfmove("0"))
        XCTAssertTrue(FENValidator.isValidHalfmove("50"))
        XCTAssertTrue(FENValidator.isValidHalfmove("100"))
        
        XCTAssertTrue(FENValidator.isValidFullmove("1"))
        XCTAssertTrue(FENValidator.isValidFullmove("50"))
        XCTAssertTrue(FENValidator.isValidFullmove("100"))
    }
    
    func testInvalidMoveCounters() {
        XCTAssertFalse(FENValidator.isValidHalfmove("-1"))
        XCTAssertFalse(FENValidator.isValidHalfmove("abc"))
        XCTAssertFalse(FENValidator.isValidHalfmove(""))
        
        XCTAssertFalse(FENValidator.isValidFullmove("0"))
        XCTAssertFalse(FENValidator.isValidFullmove("-1"))
        XCTAssertFalse(FENValidator.isValidFullmove("abc"))
        XCTAssertFalse(FENValidator.isValidFullmove(""))
    }
    
    // MARK: - Chess Logic Validation Tests
    
    func testKingCountValidation() {
        // Valid: exactly one king per side
        XCTAssertTrue(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))
        
        // Invalid: no white king
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQ1BNR w KQkq - 0 1"))
        
        // Invalid: no black king
        XCTAssertFalse(FENValidator.isValidFEN("rnbq1bnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))
        
        // Invalid: two white kings
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPKPPP/RNBQKBNR w KQkq - 0 1"))
        
        // Invalid: two black kings
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/ppppkppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))
    }
    
    func testPawnPlacementValidation() {
        // Valid: pawns not on first or last rank
        XCTAssertTrue(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))
        
        // Invalid: white pawn on first rank
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKPNR w KQkq - 0 1"))
        
        // Invalid: black pawn on eighth rank
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkpnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))
        
        // Invalid: white pawn on eighth rank
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkPnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))
        
        // Invalid: black pawn on first rank
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKpNR w KQkq - 0 1"))
    }
    
    func testPieceCountValidation() {
        // Valid: standard piece counts
        XCTAssertTrue(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))
        
        // Invalid: too many white pieces (17 pieces)
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNRQ w KQkq - 0 1"))
        
        // Invalid: too many black pieces (17 pieces)
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnrq/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))
        
        // Invalid: too many white pawns (9 pawns)
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPPP/RNBQKBNR w KQkq - 0 1"))
        
        // Invalid: too many black pawns (9 pawns)
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/ppppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))
    }
    
    func testCastlingLogicValidation() {
        // Valid: kings and rooks in correct positions
        XCTAssertTrue(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))
        
        // Invalid: white king moved but castling rights still available
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQ1KNR w KQkq - 0 1"))
        
        // Invalid: kingside rook moved but castling rights still available
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBN1 w KQkq - 0 1"))
    }
    
    func testEnPassantLogicValidation() {
        // Valid: en passant with correct pawn placement
        XCTAssertTrue(FENValidator.isValidFEN("rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 2"))
        
        // Invalid: en passant target but no pawn
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/pppp1ppp/8/4p3/8/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 2"))
        
        // Invalid: en passant for wrong turn
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e3 0 2"))
    }
    
    func testCheckLogicValidation() {
        // Valid: no opponent king in check
        XCTAssertTrue(FENValidator.isValidFEN("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))
        
        // Invalid: opponent king in check (this is a simplified test)
        XCTAssertFalse(FENValidator.isValidFEN("rnbqkbnr/pppp1ppp/8/8/8/8/PPPP1PPP/RNBKQBNR w KQkq - 0 1"))
    }
    
    // MARK: - Edge Cases
    
    func testEdgeCases() {
        // Empty board with kings
        XCTAssertTrue(FENValidator.isValidFEN("8/8/8/8/3k4/8/3K4/8 w - - 0 1"))
        
        // Minimum valid game
        XCTAssertTrue(FENValidator.isValidFEN("8/8/8/8/3k4/8/3K4/8 w - - 0 1"))
        
        // Maximum halfmove clock
        XCTAssertTrue(FENValidator.isValidFEN("8/8/8/8/3k4/8/3K4/8 w - - 99 1"))
        
        // Very high fullmove number
        XCTAssertTrue(FENValidator.isValidFEN("8/8/8/8/3k4/8/3K4/8 w - - 0 999"))
    }
    
    func testCheckValidation() {
        // Valid: active side not in check
        XCTAssertTrue(FENValidator.isValidChessLogic( "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"))
        
        // Invalid: non-active side's king in check
        XCTAssertFalse(FENValidator.isValidChessLogic("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKRN1 w KQkq - 0 1"))
        
        // Valid: active side in check (legal position)
        XCTAssertTrue(FENValidator.isValidChessLogic("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQK2R b KQkq - 0 1"))
    }
    
    // MARK: - Edge Cases and Complex Positions
    
    func testComplexValidPositions() {
        // Middle game position
        XCTAssertTrue(FENValidator.isValidChessLogic("r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4"))
        
        // Endgame position
        XCTAssertTrue(FENValidator.isValidChessLogic("8/8/8/8/8/8/4k3/6K1 w - - 0 50"))
        
        // Position with en passant
        XCTAssertTrue(FENValidator.isValidChessLogic("rnbqkbnr/ppp1p1pp/8/3pPp2/8/8/PPPP1PPP/RNBQKBNR w KQkq f6 0 3"))
        
        // Position without castling rights
        XCTAssertTrue(FENValidator.isValidChessLogic("r3k2r/8/8/8/8/8/8/R3K2R w - - 0 1"))
    }
    
    func testComplexInvalidPositions() {
        // King face to king (impossible)
        XCTAssertFalse(FENValidator.isValidChessLogic("8/8/8/8/8/8/4k3/4K1 w - - 0 50"))
        
        // King next to king (impossible)
        XCTAssertFalse(FENValidator.isValidChessLogic("8/8/8/8/8/8/4kK2/8 w - - 0 1"))
        
        // Promoted pieces causing too many pieces
        XCTAssertFalse(FENValidator.isValidChessLogic("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQQQQQKBNR w KQkq - 0 1"))
    }
    
    // MARK: - Performance Tests
    
    func testFENValidationPerformance() {
        let standardFEN = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
        
        measure {
            for _ in 0..<1000 {
                _ = FENValidator.isValidFEN(standardFEN)
            }
        }
    }
    
    func testComplexFENValidationPerformance() {
        let complexFEN = "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4"
        
        measure {
            for _ in 0..<1000 {
                _ = FENValidator.isValidFEN(complexFEN)
            }
        }
    }
    
    // MARK: - Performance Tests
    
    func testValidationPerformance() {
        let testFEN = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
        
        measure {
            for _ in 0..<1000 {
                _ = FENValidator.isValidFEN(testFEN)
            }
        }
    }
    
    func testComplexValidationPerformance() {
        let complexFEN = "r1bqk1nr/pppp1ppp/2n5/2b1p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4"
        
        measure {
            for _ in 0..<1000 {
                _ = FENValidator.isValidFEN(complexFEN)
            }
        }
    }
}
