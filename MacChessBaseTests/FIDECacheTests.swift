//
//  FIDECacheTests.swift
//  MacChessBaseTests
//
//  Created by AI Assistant on 2025/8/8.
//

import XCTest
@testable import MacChessBase

@MainActor
final class FIDECacheTests: XCTestCase {

    var fideService: FIDEService!

    override func setUp() {
        super.setUp()
        fideService = FIDEService.shared
        // Clear any existing cache before each test
        fideService.clearFIDEDataCache()
    }

    override func tearDown() {
        // Clean up after each test
        fideService.clearFIDEDataCache()
        fideService = nil
        super.tearDown()
    }

    // MARK: - FIDEPlayerData Tests

    func testFIDEPlayerDataCreation() {
        let playerData = FIDEPlayerData(
            fideId: "1503014",
            federation: "Norway",
            photoData: nil,
            photoBase64: nil,
            fetchDate: Date()
        )

        XCTAssertEqual(playerData.fideId, "1503014")
        XCTAssertEqual(playerData.federation, "Norway")
        XCTAssertNil(playerData.photoData)
        XCTAssertNil(playerData.photoBase64)
        XCTAssertTrue(playerData.isValid) // Should be valid when just created
    }

    func testFIDEPlayerDataExpiration() {
        // Create expired data
        let expiredDate = Date().addingTimeInterval(-25 * 60 * 60) // 25 hours ago
        let expiredData = FIDEPlayerData(
            fideId: "1503014",
            federation: "Norway",
            photoData: nil,
            photoBase64: nil,
            fetchDate: expiredDate
        )

        XCTAssertFalse(expiredData.isValid)
        XCTAssertTrue(expiredData.isExpired)

        // Create fresh data (valid)
        let freshData = FIDEPlayerData(
            fideId: "1503014",
            federation: "Norway",
            photoData: nil,
            photoBase64: nil,
            fetchDate: Date()
        )

        XCTAssertTrue(freshData.isValid)
        XCTAssertFalse(freshData.isExpired)
    }

    // MARK: - FIDEService Cache Tests

    func testCacheFIDEData() {
        let playerData = FIDEPlayerData(
            fideId: "1503014",
            federation: "Norway",
            photoData: nil,
            photoBase64: nil,
            fetchDate: Date()
        )

        // Initially no cached data
        XCTAssertNil(fideService.getCachedFIDEData(for: "1503014"))

        // Cache the data
        fideService.cacheFIDEData(playerData)

        // Should now return cached data
        let cachedData = fideService.getCachedFIDEData(for: "1503014")
        XCTAssertNotNil(cachedData)
        XCTAssertEqual(cachedData?.fideId, "1503014")
        XCTAssertEqual(cachedData?.federation, "Norway")
    }

    func testGetCachedFIDEDataWithExpiredData() {
        // Create expired data
        let expiredDate = Date().addingTimeInterval(-25 * 60 * 60) // 25 hours ago
        let expiredData = FIDEPlayerData(
            fideId: "1503014",
            federation: "Norway",
            photoData: nil,
            photoBase64: nil,
            fetchDate: expiredDate
        )

        // Cache the expired data
        fideService.cacheFIDEData(expiredData)

        // Should return nil for expired data and remove it from cache
        let cachedData = fideService.getCachedFIDEData(for: "1503014")
        XCTAssertNil(cachedData)
    }

    func testCleanupExpiredFIDEData() {
        // Create a mix of valid and expired data
        let validData = FIDEPlayerData(
            fideId: "1503014",
            federation: "Norway",
            photoData: nil,
            photoBase64: nil,
            fetchDate: Date()
        )

        let expiredData = FIDEPlayerData(
            fideId: "14101109",
            federation: "India",
            photoData: nil,
            photoBase64: nil,
            fetchDate: Date().addingTimeInterval(-25 * 60 * 60) // 25 hours ago
        )

        // Cache both
        fideService.cacheFIDEData(validData)
        fideService.cacheFIDEData(expiredData)

        // Clean up expired data
        fideService.cleanupExpiredFIDEData()

        // Valid data should still be there
        XCTAssertNotNil(fideService.getCachedFIDEData(for: "1503014"))

        // Expired data should be gone
        XCTAssertNil(fideService.getCachedFIDEData(for: "14101109"))
    }

    func testClearFIDEDataCache() {
        let playerData = FIDEPlayerData(
            fideId: "1503014",
            federation: "Norway",
            photoData: nil,
            photoBase64: nil,
            fetchDate: Date()
        )

        // Cache some data
        fideService.cacheFIDEData(playerData)
        XCTAssertNotNil(fideService.getCachedFIDEData(for: "1503014"))

        // Clear cache
        fideService.clearFIDEDataCache()

        // Should be empty now
        XCTAssertNil(fideService.getCachedFIDEData(for: "1503014"))
    }

    func testEmptyFIDEIdHandling() {
        // Should return nil for empty FIDE ID
        XCTAssertNil(fideService.getCachedFIDEData(for: ""))
        XCTAssertNil(fideService.getCachedFIDEData(for: "   "))
    }

    // MARK: - Performance Tests

    func testCachePerformance() {
        measure {
            for i in 0..<1000 {
                let playerData = FIDEPlayerData(
                    fideId: "\(i)",
                    federation: "Test Country",
                    photoData: nil,
                    photoBase64: nil,
                    fetchDate: Date()
                )
                fideService.cacheFIDEData(playerData)
                _ = fideService.getCachedFIDEData(for: "\(i)")
            }
        }
    }
}