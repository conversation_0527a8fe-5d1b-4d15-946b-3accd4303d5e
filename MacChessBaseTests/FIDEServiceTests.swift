////
////  FIDEServiceTests.swift
////  MacChessBaseTests
////
////  Created by <PERSON> on 2025/7/9.
////
////  Not In use because FIDEService only support few features
//
//import XCTest
//@testable import MacChessBase
//
//@MainActor
//final class FIDEServiceTests: XCTestCase {
//    
//    var fideService: FIDEService!
//    
//    override func setUp() {
//        super.setUp()
//        fideService = FIDEService()
//    }
//    
//    override func tearDown() {
//        fideService = nil
//        super.tearDown()
//    }
//    
//    // MARK: - FIDE ID Validation Tests
//    
//    func testValidFideIdFormat() async throws {
//        // Test with known valid FIDE IDs (these should be numeric)
//        let validIds = ["1503014", "14101109", "8602883"]
//        
//        for fideId in validIds {
//            // This should not throw for format validation
//            do {
//                _ = try await fideService.fetchPlayerInfo(fideId: fideId)
//            } catch FIDEService.FIDEError.invalidFideId {
//                XCTFail("Valid FIDE ID \(fideId) was rejected")
//            } catch {
//                // Other errors (network, parsing) are acceptable for this test
//                continue
//            }
//        }
//    }
//    
//    func testInvalidFideIdFormat() async {
//        let invalidIds = ["", "abc123", "12a34", "!@#$%", "123abc"]
//        
//        for invalidId in invalidIds {
//            do {
//                _ = try await fideService.fetchPlayerInfo(fideId: invalidId)
//                XCTFail("Invalid FIDE ID \(invalidId) should have thrown invalidFideId error")
//            } catch FIDEService.FIDEError.invalidFideId {
//                // Expected
//                continue
//            } catch {
//                XCTFail("Expected invalidFideId error for \(invalidId), got \(error)")
//            }
//        }
//    }
//    
//    // MARK: - Federation Code Mapping Tests
//    
//    func testFederationCodeToCountryName() {
//        // Test common federation codes
//        XCTAssertEqual(fideService.federationCodeToCountryName("USA"), "United States")
//        XCTAssertEqual(fideService.federationCodeToCountryName("NOR"), "Norway")
//        XCTAssertEqual(fideService.federationCodeToCountryName("IND"), "India")
//        XCTAssertEqual(fideService.federationCodeToCountryName("CHN"), "China")
//        XCTAssertEqual(fideService.federationCodeToCountryName("RUS"), "Russia")
//        XCTAssertEqual(fideService.federationCodeToCountryName("GER"), "Germany")
//        
//        // Test case insensitivity
//        XCTAssertEqual(fideService.federationCodeToCountryName("usa"), "United States")
//        XCTAssertEqual(fideService.federationCodeToCountryName("Usa"), "United States")
//        
//        // Test unknown code returns the code itself
//        XCTAssertEqual(fideService.federationCodeToCountryName("XYZ"), "XYZ")
//    }
//    
//    func testFederationCodeToFlag() {
//        // Test common flags
//        XCTAssertNotNil(fideService.federationCodeToFlag("USA"))
//        XCTAssertNotNil(fideService.federationCodeToFlag("NOR"))
//        XCTAssertNotNil(fideService.federationCodeToFlag("IND"))
//        XCTAssertNotNil(fideService.federationCodeToFlag("CHN"))
//        
//        // Test case insensitivity
//        XCTAssertEqual(fideService.federationCodeToFlag("USA"), fideService.federationCodeToFlag("usa"))
//        
//        // Test unknown code returns nil
//        XCTAssertNil(fideService.federationCodeToFlag("XYZ"))
//        
//        // Test flag format (should be 2 Unicode characters)
//        if let usFlag = fideService.federationCodeToFlag("USA") {
//            XCTAssertEqual(usFlag.count, 2)
//        }
//    }
//    
//    // MARK: - HTML Parsing Tests
//    
//    func testHTMLTextExtraction() {
//        let testHTML = "<div class=\"profile-top-title\">Magnus Carlsen</div>"
//        let extractedText = fideService.extractTextFromHTML(testHTML)
//        XCTAssertEqual(extractedText, "Magnus Carlsen")
//        
//        // Test with empty HTML
//        XCTAssertNil(fideService.extractTextFromHTML(""))
//        XCTAssertNil(fideService.extractTextFromHTML("<div></div>"))
//        
//        // Test with nested HTML
//        let nestedHTML = "<div><strong>Test</strong> Player</div>"
//        let nestedText = fideService.extractTextFromHTML(nestedHTML)
//        XCTAssertEqual(nestedText, "Test Player")
//    }
//    
//    func testRegexExtraction() {
//        // Test rating extraction
//        let ratingHTML = "Standard: 2830"
//        let rating = fideService.extractMatch(from: ratingHTML, pattern: #"(\d{4})"#)
//        XCTAssertEqual(rating, "2830")
//        
//        // Test federation extraction
//        let federationHTML = "<h5>National Rank USA</h5>"
//        let federation = fideService.extractMatch(from: federationHTML, pattern: #"<h5>National Rank\s+([A-Z]{3})</h5>"#)
//        XCTAssertEqual(federation, "USA")
//        
//        // Test title extraction
//        let titleHTML = "Title: </td> <td> GM"
//        let title = fideService.extractMatch(from: titleHTML, pattern: #"([A-Z]{2,3})"#)
//        XCTAssertEqual(title, "GM")
//    }
//    
//    // MARK: - Debug Methods Tests
//    
//    func testDebugParseHTML() {
//        let testHTML = """
//        <div class="profile-top-title">Test Player</div>
//        <h5>National Rank USA</h5>
//        Standard: 2700
//        Title: </td> <td> GM
//        """
//        
//        let player = fideService.debugParseHTML(testHTML)
//        XCTAssertEqual(player.fideId, "TEST123")
//        XCTAssertEqual(player.name, "Test Player")
//        XCTAssertEqual(player.federation, "USA")
//        XCTAssertEqual(player.rating, 2700)
//        XCTAssertEqual(player.title, "GM")
//    }
//    
//    func testDebugParseFederation() {
//        // Test National Rank format
//        let nationalRankHTML = "<h5>National Rank NOR</h5>"
//        let federation1 = fideService.debugParseFederation(nationalRankHTML)
//        XCTAssertEqual(federation1, "NOR")
//        
//        // Test table format
//        let tableHTML = "Federation: </td> <td> IND"
//        let federation2 = fideService.debugParseFederation(tableHTML)
//        XCTAssertEqual(federation2, "IND")
//        
//        // Test flag format
//        let flagHTML = "class=\"flag-ger\""
//        let federation3 = fideService.debugParseFederation(flagHTML)
//        XCTAssertEqual(federation3, "GER")
//        
//        // Test no match
//        let noMatchHTML = "No federation info here"
//        let federation4 = fideService.debugParseFederation(noMatchHTML)
//        XCTAssertNil(federation4)
//    }
//    
//    // MARK: - Error Handling Tests
//    
//    func testFIDEErrorDescriptions() {
//        let errors: [FIDEService.FIDEError] = [
//            .invalidFideId,
//            .networkError(NSError(domain: "Test", code: 1, userInfo: nil)),
//            .parseError,
//            .playerNotFound,
//            .invalidURL
//        ]
//        
//        for error in errors {
//            XCTAssertNotNil(error.errorDescription)
//            XCTAssertFalse(error.errorDescription!.isEmpty)
//        }
//    }
//    
//    // MARK: - Integration Tests (Mock-based)
//    
//    func testPlayerInfoStructure() {
//        let player = FIDEService.FIDEPlayer(
//            fideId: "1503014",
//            name: "Magnus Carlsen",
//            federation: "NOR",
//            rating: 2830,
//            title: "GM"
//        )
//        
//        XCTAssertEqual(player.fideId, "1503014")
//        XCTAssertEqual(player.name, "Magnus Carlsen")
//        XCTAssertEqual(player.federation, "NOR")
//        XCTAssertEqual(player.rating, 2830)
//        XCTAssertEqual(player.title, "GM")
//    }
//    
//    func testKnownFideIds() {
//        // Test the test FIDE IDs dictionary
//        XCTAssertFalse(FIDEService.testFideIds.isEmpty)
//        XCTAssertEqual(FIDEService.testFideIds.count, 5)
//        XCTAssertEqual(FIDEService.testFideIds["1503014"], "Magnus Carlsen (NOR)")
//    }
//    
//    // MARK: - Performance Tests
//    
//    func testFederationMappingPerformance() {
//        measure {
//            for _ in 0..<1000 {
//                _ = fideService.federationCodeToCountryName("USA")
//                _ = fideService.federationCodeToFlag("NOR")
//            }
//        }
//    }
//    
//    func testHTMLParsingPerformance() {
//        let testHTML = """
//        <div class="profile-top-title">Test Player</div>
//        <h5>National Rank USA</h5>
//        Standard: 2700
//        Title: </td> <td> GM
//        """
//        
//        measure {
//            for _ in 0..<100 {
//                _ = fideService.debugParseHTML(testHTML)
//            }
//        }
//    }
//    
//    // MARK: - Edge Cases Tests
//    
//    func testEmptyAndNilInputs() {
//        // Test federation mapping with empty string
//        XCTAssertEqual(fideService.federationCodeToCountryName(""), "")
//        XCTAssertNil(fideService.federationCodeToFlag(""))
//        
//        // Test HTML extraction with whitespace
//        XCTAssertNil(fideService.extractTextFromHTML("   "))
//        XCTAssertNil(fideService.extractTextFromHTML("\t\n\r"))
//    }
//    
//    func testSpecialCharactersInHTML() {
//        let htmlWithSpecialChars = "<div>José Raúl Capablanca</div>"
//        let extracted = fideService.extractTextFromHTML(htmlWithSpecialChars)
//        XCTAssertEqual(extracted, "José Raúl Capablanca")
//        
//        let htmlWithAmpersands = "<div>Smith &amp; Jones</div>"
//        let extractedAmp = fideService.extractTextFromHTML(htmlWithAmpersands)
//        XCTAssertEqual(extractedAmp, "Smith &amp; Jones")
//    }
//    
//    func testCaseInsensitiveRegexMatching() {
//        // Test that regex patterns work with different cases
//        let lowerCaseHTML = "<h5>national rank usa</h5>"
//        let pattern = #"<h5>National Rank\s+([A-Z]{3})</h5>"#
//        let match = fideService.extractMatch(from: lowerCaseHTML, pattern: pattern)
//        // Should not match due to case sensitivity in pattern
//        XCTAssertNil(match)
//    }
//}
//
//// MARK: - Test Extensions
//
//extension FIDEServiceTests {
//    /// Helper method to make extractTextFromHTML testable
//    private func extractTextFromHTML(_ html: String) -> String? {
//        return fideService.extractTextFromHTML(html)
//    }
//    
//    /// Helper method to make extractMatch testable
//    private func extractMatch(from string: String, pattern: String) -> String? {
//        return fideService.extractMatch(from: string, pattern: pattern)
//    }
//}
//
//// Make private methods accessible for testing
//extension FIDEService {
//    func extractTextFromHTML(_ html: String) -> String? {
//        let pattern = #"<[^>]*>"#
//        let cleanText = html.replacingOccurrences(of: pattern, with: "", options: .regularExpression)
//        let trimmed = cleanText.trimmingCharacters(in: .whitespacesAndNewlines)
//        return trimmed.isEmpty ? nil : trimmed
//    }
//    
//    func extractMatch(from string: String, pattern: String) -> String? {
//        guard let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive) else {
//            return nil
//        }
//        
//        let range = NSRange(location: 0, length: string.utf16.count)
//        guard let match = regex.firstMatch(in: string, options: [], range: range) else {
//            return nil
//        }
//        
//        if match.numberOfRanges > 1 {
//            let captureRange = match.range(at: 1)
//            if captureRange.location != NSNotFound,
//               let range = Range(captureRange, in: string) {
//                return String(string[range])
//            }
//        }
//        
//        return nil
//    }
//}
