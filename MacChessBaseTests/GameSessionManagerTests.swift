//
//  GameSessionManagerTests.swift
//  MacChessBaseTests
//
//  Tests for game session management
//

import XCTest
@testable import MacChessBase
@testable import ChessKit

final class GameSessionManagerTests: XCTestCase {
    
    var sessionManager: GameSessionManager!
    
    @MainActor
    override func setUp() {
        super.setUp()
        sessionManager = GameSessionManager()
    }
    
    override func tearDown() {
        sessionManager = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    @MainActor
    func testInitialState() {
        XCTAssertEqual(sessionManager.sessions.count, 1)
        XCTAssertNotNil(sessionManager.activeSession)
        XCTAssertEqual(sessionManager.activeSession?.name, "New Game")
    }
    
    // MARK: - Session Creation Tests
    
    @MainActor
    func testCreateNewSession() {
        let initialCount = sessionManager.sessions.count
        sessionManager.createNewSession()
        
        XCTAssertEqual(sessionManager.sessions.count, initialCount + 1)
        XCTAssertEqual(sessionManager.activeSession?.name, "New Game 2")
    }
    
    @MainActor
    func testCreateMultipleSessions() {
        sessionManager.createNewSession()
        sessionManager.createNewSession()
        sessionManager.createNewSession()
        
        XCTAssertEqual(sessionManager.sessions.count, 4)
        XCTAssertEqual(sessionManager.activeSession?.name, "New Game 4")
    }
    
    // MARK: - Session Selection Tests
    
    @MainActor
    func testSelectSession() {
        // Create multiple sessions
        sessionManager.createNewSession()
        sessionManager.createNewSession()
        
        let firstSession = sessionManager.sessions[0]
        let secondSession = sessionManager.sessions[1]
        
        // Select first session
        sessionManager.setActiveSession(firstSession)
        XCTAssertEqual(sessionManager.activeSession?.id, firstSession.id)
        
        // Select second session
        sessionManager.setActiveSession(secondSession)
        XCTAssertEqual(sessionManager.activeSession?.id, secondSession.id)
    }
    
    @MainActor
    func testSelectNonexistentSession() {
        let nonexistentSession = GameSession()
        
        sessionManager.setActiveSession(nonexistentSession)
        
        // Active session should remain unchanged
        XCTAssertNotEqual(sessionManager.activeSession?.id, nonexistentSession.id)
    }
    
    // MARK: - Session Removal Tests
    
    @MainActor
    func testRemoveSession() {
        // Create multiple sessions
        sessionManager.createNewSession()
        sessionManager.createNewSession()
        
        let sessionToRemove = sessionManager.sessions[1]
        let initialCount = sessionManager.sessions.count
        
        sessionManager.removeSession(sessionToRemove)
        
        XCTAssertEqual(sessionManager.sessions.count, initialCount - 1)
        XCTAssertFalse(sessionManager.sessions.contains { $0.id == sessionToRemove.id })
    }
    
    @MainActor
    func testRemoveActiveSession() {
        // Create multiple sessions
        sessionManager.createNewSession()
        sessionManager.createNewSession()
        
        let activeSession = sessionManager.activeSession!
        let initialCount = sessionManager.sessions.count
        
        sessionManager.removeSession(activeSession)
        
        XCTAssertEqual(sessionManager.sessions.count, initialCount - 1)
        XCTAssertNotEqual(sessionManager.activeSession?.id, activeSession.id)
        XCTAssertNotNil(sessionManager.activeSession) // Should select another session
    }
    
    @MainActor
    func testRemoveLastSession() {
        // Remove all sessions except one
        while sessionManager.sessions.count > 1 {
            sessionManager.removeSession(sessionManager.sessions.last!)
        }
        
        let lastSession = sessionManager.sessions.first!
        sessionManager.removeSession(lastSession)
        
        // Don't remove the last session, just reset it
        XCTAssertEqual(sessionManager.sessions.count, 1)
        XCTAssertNotNil(sessionManager.activeSession)
        XCTAssertEqual(sessionManager.activeSession?.id, lastSession.id)
    }
    
    // MARK: - Session Renaming Tests
    
    @MainActor
    func testRenameSession() {
        let session = sessionManager.activeSession!
        let newName = "Custom Game Name"
        
        sessionManager.activeSession?.name = newName
        
        XCTAssertEqual(session.name, newName)
    }
    
    // MARK: - Session State Tests
    
    @MainActor
    func testSessionGameState() {
        let session = sessionManager.activeSession!

        // Make some moves in the game using the view model's API
        let viewModel = sessionManager.getViewModel(for: session)
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)

        // Verify session reflects game state
        XCTAssertFalse(session.game.moves.isEmpty)
        XCTAssertEqual(session.moveCount, 2)
    }
    
    @MainActor
    func testSessionIndependence() {
        // Create multiple sessions
        sessionManager.createNewSession()
        sessionManager.createNewSession()
        
        let firstSession = sessionManager.sessions[0]
        let secondSession = sessionManager.sessions[1]
        
        // Make different moves in each session
        sessionManager.setActiveSession(firstSession)
        let firstViewModel = sessionManager.getViewModel(for: firstSession)
        firstViewModel.attemptMove(from: Square.e2, to: Square.e4)
        firstViewModel.attemptMove(from: Square.e7, to: Square.e5)

        sessionManager.setActiveSession(secondSession)
        let secondViewModel = sessionManager.getViewModel(for: secondSession)
        secondViewModel.attemptMove(from: Square.g1, to: Square.f3)
        
        // Verify sessions are independent
        XCTAssertNotEqual(firstSession.moveCount, secondSession.moveCount)
        XCTAssertEqual(firstSession.moveCount, 2)
        XCTAssertEqual(secondSession.moveCount, 1)
    }
    
    // MARK: - Edge Cases Tests
    
    @MainActor
    func testConcurrentSessionOperations() {
        // Test rapid session creation and removal
        for _ in 0..<10 {
            sessionManager.createNewSession()
        }
        
        XCTAssertEqual(sessionManager.sessions.count, 11)
        
        // Remove sessions rapidly
        while sessionManager.sessions.count > 3 {
            sessionManager.removeSession(sessionManager.sessions.last!)
        }
        
        XCTAssertEqual(sessionManager.sessions.count, 3)
        XCTAssertNotNil(sessionManager.activeSession)
    }
    
    // MARK: - Performance Tests
    
    @MainActor
    func testSessionCreationPerformance() {
        measure {
            for _ in 0..<100 {
                sessionManager.createNewSession()
            }
        }
    }
    
    @MainActor
    func testSessionSwitchingPerformance() {
        // Create multiple sessions
        for _ in 0..<50 {
            sessionManager.createNewSession()
        }
        
        measure {
            for session in sessionManager.sessions {
                sessionManager.setActiveSession(session)
            }
        }
    }

    // MARK: - ViewModel Management Tests

    @MainActor
    func testViewModelCaching() {
        let session = sessionManager.activeSession!

        // Get ViewModel twice for the same session
        let viewModel1 = sessionManager.getViewModel(for: session)
        let viewModel2 = sessionManager.getViewModel(for: session)

        // Should return the same instance
        XCTAssertTrue(viewModel1 === viewModel2)
    }

    @MainActor
    func testViewModelSwitching() {
        // Create multiple sessions
        sessionManager.createNewSession()
        sessionManager.createNewSession()

        let firstSession = sessionManager.sessions[0]
        let secondSession = sessionManager.sessions[1]

        // Get ViewModels for different sessions
        let firstViewModel = sessionManager.getViewModel(for: firstSession)
        let secondViewModel = sessionManager.getViewModel(for: secondSession)

        // Should be different instances
        XCTAssertFalse(firstViewModel === secondViewModel)

        // Getting the first ViewModel again should return the same instance as the second
        // (because it's the current one)
        let firstViewModelAgain = sessionManager.getViewModel(for: firstSession)
        XCTAssertFalse(firstViewModel === firstViewModelAgain)
    }

    @MainActor
    func testUIStatePersistence() {
        // Create multiple sessions
        sessionManager.createNewSession()
        sessionManager.createNewSession()

        let firstSession = sessionManager.sessions[0]
        let secondSession = sessionManager.sessions[1]

        // Modify UI state in first session
        let firstViewModel = sessionManager.getViewModel(for: firstSession)
        firstSession.isBoardFlipped = true
        firstSession.currentAnnotationColor = .red

        // Switch to second session
        let secondViewModel = sessionManager.getViewModel(for: secondSession)
        XCTAssertFalse(secondSession.isBoardFlipped) // Should be default
        XCTAssertEqual(secondSession.currentAnnotationColor, .green) // Should be default

        // Switch back to first session
        let firstViewModelAgain = sessionManager.getViewModel(for: firstSession)
        XCTAssertTrue(firstSession.isBoardFlipped) // Should be restored
        XCTAssertEqual(firstSession.currentAnnotationColor, .red) // Should be restored
    }

    @MainActor
    func testActiveViewModelProperty() {
        let session = sessionManager.activeSession!
        let activeViewModel = sessionManager.activeViewModel
        let directViewModel = sessionManager.getViewModel(for: session)

        XCTAssertNotNil(activeViewModel)
        XCTAssertTrue(activeViewModel === directViewModel)
    }
}
