//
//  PerformanceTests.swift
//  MacChessBaseTests
//
//  Performance tests for critical MacChessBase components
//

import XCTest
@testable import MacChessBase
@testable import ChessKit

final class PerformanceTests: XCTestCase {
    
    // MARK: - Game Performance Tests (Non-MainActor)
    
    func testGameCreationPerformance() {
        measure {
            for _ in 0..<100 {
                let game = Game()
                _ = game.startingPosition
            }
        }
    }
    
    func testMoveExecutionPerformance() {
        measure {
            for _ in 0..<100 {
                var tempGame = Game()
                tempGame.make(move: "e4", from: MoveTree.minimumIndex)
                tempGame.make(move: "e5", from: tempGame.moves.lastMainVariationIndex)
                tempGame.make(move: "Nf3", from: tempGame.moves.lastMainVariationIndex)
                tempGame.make(move: "Nc6", from: tempGame.moves.lastMainVariationIndex)
            }
        }
    }
    
    func testComplexGamePerformance() {
        measure {
            for _ in 0..<100 {
                var game = Game()
                
                // Create a complex game with variations
                game.make(move: "e4", from: MoveTree.minimumIndex)
                game.make(move: "e5", from: game.moves.lastMainVariationIndex)
                
                let e5Index = game.moves.history(for: game.moves.lastMainVariationIndex).first {
                    game.moves.getNodeMove(index: $0)?.metaMove?.san == "e5"
                }!
                
                // Add variations
                game.make(move: "d6", from: e5Index)
                game.make(move: "Nf3", from: e5Index)
                game.make(move: "Nc6", from: game.moves.lastMainVariationIndex)
                game.make(move: "Bc4", from: game.moves.lastMainVariationIndex)
                game.make(move: "Be7", from: game.moves.lastMainVariationIndex)
            }
        }
    }
    
    // MARK: - Position Analysis Performance
    
    func testPositionEvaluationPerformance() {
        let positions = [
            Position.standard,
            Position(fen: "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1")!,
            Position(fen: "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4")!,
            Position(fen: "rnbq1rk1/ppp1ppbp/3p1np1/8/2PP4/2N1PN2/PP2BPPP/R1BQKR2 b Q - 0 6")!
        ]
        
        measure {
            for _ in 0..<1000 {
                for position in positions {
                    let board = Board(position: position)
                    _ = board.checkStateTest(for: position.sideToMove)
                    _ = position.hasInsufficientMaterial
                }
            }
        }
    }
    
    func testLegalMovesGenerationPerformance() {
        let board = Board(position: .standard)
        
        measure {
            for _ in 0..<100 {
                for file in Square.File.allCases {
                    for rank in Square.Rank.range {
                        let square = Square("\(file)\(rank)")
                        _ = board.legalMoves(forPieceAt: square)
                    }
                }
            }
        }
    }
    
    // MARK: - PGN Processing Performance
    
    func testPGNParsingPerformance() {
        let largePGN = """
        [Event "Test Tournament"]
        [Site "Test Location"]
        [Date "2023.01.01"]
        [Round "1"]
        [White "Player1"]
        [Black "Player2"]
        [Result "1-0"]
        
        1. e4 e5 2. Nf3 Nc6 3. Bc4 Be7 4. d3 d6 5. Bg5 Bg4 6. h3 Bh5 7. Nc3 a6 8. a4 b5 9. axb5 axb5 10. Bd5 Ra5
        """
        
        measure {
            for _ in 0..<100 {
                _ = Game(pgn: largePGN)
            }
        }
    }
    
    func testPGNGenerationPerformance() {
        var game = Game()
        
        // Create a complex game
        let moves = ["e4", "e5", "Nf3", "Nc6", "Bc4", "Be7", "d3", "d6", "Bg5", "Bg4", "h3", "Bh5", "Nc3", "a6", "a4", "b5"]
        
        for i in 0..<moves.count {
            let moveIndex = i == 0 ? MoveTree.minimumIndex : game.moves.lastMainVariationIndex
            game.make(move: moves[i], from: moveIndex)
        }
        
        measure {
            for _ in 0..<100 {
                _ = game.pgn
            }
        }
    }
    
    // MARK: - FEN Processing Performance
    
    func testFENParsingPerformance() {
        let testFENs = [
            "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
            "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1",
            "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4",
            "rnbq1rk1/ppp1ppbp/3p1np1/8/2PP4/2N1PN2/PP2BPPP/R1BQKR2 b Q - 0 6",
            "r2q1rk1/ppp2ppp/2n1bn2/2b1p3/3pP3/3P1N2/PPP1BPPP/RNBQ1RK1 w - - 0 8"
        ]
        
        measure {
            for _ in 0..<100 {
                for fen in testFENs {
                    _ = Position(fen: fen)
                }
            }
        }
    }
    
    func testFENValidationPerformance() {
        let testFENs = [
            "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
            "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1",
            "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4",
            "invalid fen string",
            "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq",
            "rnbq1knr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
        ]
        
        measure {
            for _ in 0..<100 {
                for fen in testFENs {
                    _ = FENValidator.isValidFEN(fen)
                }
            }
        }
    }
    
    // MARK: - Coordinate Transformation Performance
    
    func testBoardCoordinateTransformationPerformance() {
        let boardSize: CGFloat = 400
        let transformer = BoardCoordinateTransformer(isFlipped: false)
        
        measure {
            for _ in 0..<100 {
                for file in Square.File.allCases {
                    for rank in Square.Rank.range {
                        let square = Square("\(file)\(rank)")
                        let position = transformer.squareToPosition(square, squareSize: boardSize)
                        _ = transformer.positionToSquare(position, squareSize: boardSize)
                    }
                }
            }
        }
    }
    
    // MARK: - Memory Performance Tests
    
    func testMemoryUsageWithLargeGames() {
        measure {
            var games: [Game] = []
            
            // Create many games with complex move sequences
            for _ in 0..<100 {
                var game = Game()
                
                // Create deep move sequences
                let moves = ["e4", "e5", "Nf3", "Nc6", "Bc4", "Be7", "d3", "d6", "Bg5", "Bg4", "h3", "Bh5", "Nc3", "a6"]
                
                for i in 0..<moves.count {
                    let moveIndex = i == 0 ? MoveTree.minimumIndex : game.moves.lastMainVariationIndex
                    game.make(move: moves[i], from: moveIndex)
                }
                
                games.append(game)
            }
            
            // Access all games to ensure they're in memory
            for game in games {
                _ = game.pgn
            }
            
            games.removeAll()
        }
    }
    
    // MARK: - Stress Tests
    
    func testRapidGameCreationAndDestruction() {
        measure {
            for _ in 0..<100 {
                var game = Game()
                game.make(move: "e4", from: MoveTree.minimumIndex)
                game.make(move: "e5", from: game.moves.lastMainVariationIndex)
                _ = game.pgn
                // Game should be deallocated here
            }
        }
    }
}
