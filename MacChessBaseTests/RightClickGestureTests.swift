//
//  RightClickGestureTests.swift
//  MacChessBaseTests
//
//  Tests for right-click gesture recognition functionality
//

import XCTest
import SwiftUI
import AppKit
@testable import MacChessBase

final class RightClickGestureTests: XCTestCase {
    
    // MARK: - RightClickGestureView Tests
    
    func testRightClickGestureViewInitialization() {
        var rightClickTriggered = false
        
        let gestureView = RightClickGestureView {
            rightClickTriggered = true
        }
        
        // Test that the view can be created successfully
        XCTAssertNotNil(gestureView)
        
        // Test the closure hasn't been triggered yet
        XCTAssertFalse(rightClickTriggered)
    }
    
    func testRightClickGestureCoordinatorInitialization() {
        var rightClickTriggered = false
        
        let gestureView = RightClickGestureView {
            rightClickTriggered = true
        }
        
        let coordinator = gestureView.makeCoordinator()
        
        XCTAssertNotNil(coordinator)
        XCTAssertTrue(coordinator is RightClickGestureView.Coordinator)
    }
    
    func testRightClickGestureCoordinatorHandleRightClick() {
        var rightClickTriggered = false
        var triggerCount = 0
        
        let coordinator = RightClickGestureView.Coordinator(onRightClick: {
            rightClickTriggered = true
            triggerCount += 1
        })
        
        // Create a mock gesture recognizer
        let mockGestureRecognizer = NSClickGestureRecognizer()
        
        // Trigger the right-click handler
        coordinator.handleRightClick(mockGestureRecognizer)
        
        // Verify the callback was triggered
        XCTAssertTrue(rightClickTriggered)
        XCTAssertEqual(triggerCount, 1)
        
        // Trigger again to test multiple calls
        coordinator.handleRightClick(mockGestureRecognizer)
        XCTAssertEqual(triggerCount, 2)
    }
    
    func testRightClickGestureCallbackClosure() {
        var capturedValue = ""
        
        let gestureView = RightClickGestureView {
            capturedValue = "callback executed"
        }
        
        let coordinator = gestureView.makeCoordinator()
        
        // Verify initial state
        XCTAssertEqual(capturedValue, "")
        
        // Trigger callback
        coordinator.handleRightClick(NSClickGestureRecognizer())
        
        // Verify callback was executed
        XCTAssertEqual(capturedValue, "callback executed")
    }
    
    func testMultipleRightClickGestureViews() {
        var leftClickCount = 0
        var rightClickCount = 0
        
        let leftGestureView = RightClickGestureView {
            leftClickCount += 1
        }
        
        let rightGestureView = RightClickGestureView {
            rightClickCount += 1
        }
        
        let leftCoordinator = leftGestureView.makeCoordinator()
        let rightCoordinator = rightGestureView.makeCoordinator()
        
        // Simulate clicks on both views
        leftCoordinator.handleRightClick(NSClickGestureRecognizer())
        leftCoordinator.handleRightClick(NSClickGestureRecognizer())
        
        rightCoordinator.handleRightClick(NSClickGestureRecognizer())
        
        XCTAssertEqual(leftClickCount, 2)
        XCTAssertEqual(rightClickCount, 1)
    }
    
    func testRightClickGestureIntegration() {
        var clicksReceived: [String] = []
        
        let gestureView = RightClickGestureView {
            clicksReceived.append("right-click")
        }
        
        let coordinator = gestureView.makeCoordinator()
        
        // Simulate right-click through coordinator
        coordinator.handleRightClick(NSClickGestureRecognizer())
        coordinator.handleRightClick(NSClickGestureRecognizer())
        coordinator.handleRightClick(NSClickGestureRecognizer())
        
        XCTAssertEqual(clicksReceived.count, 3)
        XCTAssertEqual(clicksReceived, ["right-click", "right-click", "right-click"])
    }
    
    // MARK: - Performance Tests
    
    func testRightClickGesturePerformance() {
        var clickCount = 0
        let gestureView = RightClickGestureView {
            clickCount += 1
        }
        
        let coordinator = gestureView.makeCoordinator()
        let mockGestureRecognizer = NSClickGestureRecognizer()
        
        measure {
            for _ in 0..<1000 {
                coordinator.handleRightClick(mockGestureRecognizer)
            }
        }
        
        // The measure block runs multiple times (typically 10), so we expect more than 1000 clicks
        XCTAssertGreaterThanOrEqual(clickCount, 1000)
    }
    
    func testRightClickGestureViewCreationPerformance() {
        measure {
            for _ in 0..<100 {
                let gestureView = RightClickGestureView { }
                _ = gestureView.makeCoordinator()
            }
        }
    }
}

// MARK: - Test Extensions

extension RightClickGestureTests {
    
    func testRightClickGestureMemoryManagement() {
        weak var weakCoordinator: RightClickGestureView.Coordinator?
        
        autoreleasepool {
            let gestureView = RightClickGestureView { }
            let coordinator = gestureView.makeCoordinator()
            
            weakCoordinator = coordinator
            XCTAssertNotNil(weakCoordinator)
        }
        
        // After autoreleasepool, objects should be deallocated
        // Note: This test might be flaky due to ARC behavior
        // but it's useful to check for obvious memory leaks
    }
    
    func testRightClickGestureCoordinatorRetainsClosure() {
        var callbackExecuted = false
        
        // Create coordinator with callback
        let coordinator = RightClickGestureView.Coordinator(onRightClick: {
            callbackExecuted = true
        })
        
        // Even if we don't hold a reference to the original closure,
        // the coordinator should retain it
        coordinator.handleRightClick(NSClickGestureRecognizer())
        
        XCTAssertTrue(callbackExecuted)
    }
    
    func testRightClickGestureCoordinatorIsNSObject() {
        let coordinator = RightClickGestureView.Coordinator(onRightClick: { })
        
        // Verify coordinator is NSObject (required for target-action)
        XCTAssertTrue(coordinator is NSObject)
    }
    
    func testRightClickGestureHandleRightClickSelector() {
        // Verify the selector exists and is accessible
        let selector = #selector(RightClickGestureView.Coordinator.handleRightClick(_:))
        XCTAssertNotNil(selector)
        
        // Verify coordinator responds to the selector
        let coordinator = RightClickGestureView.Coordinator(onRightClick: { })
        XCTAssertTrue(coordinator.responds(to: selector))
    }
}
