# MacChessBase

A professional native macOS chess analysis suite built with Swift and SwiftUI, featuring multi-module architecture with card-based navigation and comprehensive chess functionality powered by the enhanced ChessKit-Swift library. MacChessBase provides professional chess analysis tools comparable to ChessBase, database management capabilities, OCR recognition, and tournament organization software.

## Project Overview

MacChessBase has evolved from a single-view chess analysis application into a comprehensive professional chess suite. Built from the ground up as a native Mac application with multi-window architecture, it provides intuitive card-based navigation, powerful analysis tools, and seamless integration with the macOS ecosystem.

**Current Version:** Release 1.4.0  
**Status:** Multi-module professional chess suite with card-based navigation, subscription system, multi-game PGN import, chess board screenshot functionality, and modular architecture supporting Chess Analysis, Database, OCR, and Tournament Management

## Multi-Module Architecture (Release 1.3.0)

### **Card-Based Navigation Interface**
- Professional 2x2 card layout replacing single-view interface
- Four distinct modules: Chess Analysis, Database, OCR Recognition, Tournament Management
- Dynamic card sizing that adapts to main window dimensions
- Multi-window system with independent windows for each module
- Subscription-gated Pro features with upgrade prompts

### **Module Overview**
- **Chess Analysis** (Free): Complete chess analysis functionality with all existing features
- **Online Database** (Pro): Database management and multi-game collection support
- **OCR Recognition** (Pro): Board and notation image-to-text conversion capabilities  
- **Tournament Management** (Pro): Professional tournament organization software inspired by SP98

### **Window Management System**
- Centralized WindowManager singleton for lifecycle coordination
- Main window closing automatically closes all child windows
- Screen-optimized sizing (main: 98%, child: 90% of visible area)
- Dynamic window sizing with content-appropriate minimum dimensions
- Chess interface optimized with 1200x600 minimum size for optimal board display
- Memory-safe NSHostingController implementation with proper cleanup

## Chess Analysis Module Features

### **Interactive Chess Board**
- Visual chess board with piece drag-and-drop
- Board orientation with flip functionality  
- Click-to-move and drag-and-drop piece movement
- Reverse drag support (drag from target to source)
- Legal move highlighting and validation
- Last move highlighting and visual feedback
- Pawn promotion dialog with keyboard shortcuts

### **Chess Board Screenshot System**
- **Dual Export Modes**: Clean board diagrams or full interface screenshots
- **Copy to Clipboard**: Instant sharing with other applications
- **Save to File**: Professional PNG/JPEG export with timestamp naming
- **High Resolution**: 800x800 output with 2x scaling for crisp images
- **Context Menu Integration**: Right-click access with organized submenus

### **Game Analysis & Notation**
- Complete PGN import/export capabilities
- FEN support for position setup
- Clipboard integration for games
- Interactive notation view with clickable moves
- Complex variation support (create, edit, navigate)
- Move tree management with promotion/deletion
- Text comments and move assessments
- Position evaluations and annotations

### **Advanced Position Editor**
- Full-featured position editor with comprehensive validation
- Visual consistency with main chess board (unified brown color scheme)
- **Screenshot Functionality**: Professional diagram generation capabilities
  - Copy position screenshots to clipboard for instant sharing
  - Save screenshots to file with PNG/JPEG export options
  - Clean output optimized for formal presentation (square corners, no coordinates)
  - High-resolution output (2x scale) for crisp printing and sharing
  - Horizontal button layout with multi-line text support
  - Automatic timestamp-based file naming (PositionEditor_timestamp.png)
- Real-time FEN validation with user-friendly error messages
- Initial move number control with stepper interface
- Support for custom starting positions
- Automatic FEN and SetUp tag management

### **Visual Annotations**
- Multi-color square highlighting system
- Colored arrow annotations on board
- Green, red, blue annotation support
- Right-click context menus for annotation editing

### **Stockfish Engine Integration**
- Real-time position analysis
- Multi-PV analysis with multiple best move candidates
- Configurable engine settings (depth, threads, hash size)
- Analysis display with evaluation and principal variations
- Performance metrics (nodes per second, analysis time)
- Engine management (start, stop, pause, resume)

### **Multi-Game Management**
- Game sessions with sidebar management
- Multiple concurrent game sessions
- Session creation, renaming, removal
- Active session tracking with visual indicators
- Auto-naming based on player names and metadata
- **Multi-Game PGN Import** *(New in Release 1.4.0)*: Intelligent detection and handling of multi-game PGN files
  - Automatic detection of single vs. multiple games in PGN input
  - Smart routing: single games use undo/redo, multiple games create separate sessions
  - Universal support for both file and clipboard imports
  - Intelligent session naming based on PGN metadata or game indices
  - Performance optimization: multi-game imports skip undo/redo for better speed

### **File Operations**
- Support for .pgn and .fen files
- Save/Save As operations with metadata editing
- Auto-suggest filenames based on game content
- File loading with automatic format detection
- Import error handling with user-friendly alerts

### **Game Metadata Management**
- Comprehensive PGN tags (players, ratings, titles, FIDE IDs)
- Tournament data (event, site, date, round)
- Game details (result, time control, ECO code, opening)
- Technical tags (FEN, SetUp for custom positions)
- Full-featured metadata editor dialog

### **Advanced Move Editing**
- "Delete Before Move" functionality for game restructuring
- Create new starting positions from any point in a game
- Variation promotion and management
- Move deletion with descendant handling

### **Native Undo/Redo System** *(Enhanced in Release 1.2.0)*
- Full macOS UndoManager integration with system-level undo/redo
- Complete undo/redo support for all chess operations
- Memory-optimized state management with 7 specialized undo state types
- Encapsulated index resolution API eliminating code duplication
- Enhanced reliability with non-optional path fields
- Position restoration with automatic position dictionary synchronization
- Reliable integer indexing system compatible with native undo/redo operations
- Support for complex operations including move editing, variation promotion, and game restructuring
- Position editor changes and clipboard import operations (PGN/FEN) with intelligent change detection

### **Native macOS Integration**
- Menu bar integration with native commands
- Comprehensive keyboard shortcuts including native Cmd+Z/Cmd+Shift+Z undo/redo
- Responsive layout with split views
- Dark mode support
- Three-panel layout (board, notation, analysis)
- Resizable and collapsible panels

### **Audio & Clock Features**
- Move sounds and audio feedback
- Visual clocks for both players
- Clock orientation based on board flip
- Player information display on clocks

## Technical Architecture

### **Dependencies**
This project uses an enhanced **ChessKit-Swift** library, included locally with major enhancements:

- **Enhanced Move System**: Rich move representation with assessments and comments
- **Advanced PGN Parser**: Tokenizer-based parser with full annotation support and multi-game capabilities
- **Multi-Game PGN Support**: Intelligent parsing of single and multiple games with enhanced boundary detection
- **Comprehensive FEN Validation**: Complete position validation with chess logic
- **135+ NAGs Support**: Full Numerical Annotation Glyph system
- **Visual Annotations**: Square highlights and arrow annotations
- **Time Annotations**: Clock and elapsed move time support

### **Technical Stack**
- **Language**: Swift
- **UI Framework**: SwiftUI
- **Chess Logic**: Enhanced ChessKit-Swift (local package)
- **Target Platform**: macOS 13.0+
- **Architecture**: MVVM with refactored GameSession pattern
- **Engine**: Stockfish integration via UCI protocol
- **Indexing**: Integer-based MoveTree with optimized undo/redo compatibility

### **Project Structure**
- **Multi-Module Architecture**: Card-based navigation system with four distinct modules *(Release 1.3.0)*
- **Multi-Window System**: WindowManager singleton coordinating independent module windows
- **Subscription Integration**: Three-tier feature access (Free/Pro/Expert) with validation system
- **Memory-Safe Implementation**: NSHostingController pattern with proper cleanup observers
- **Dynamic UI System**: GeometryReader-based responsive layout for adaptive card sizing
- **MVVM Architecture**: Clean separation with GameSession for state management
- **Modular Services**: Separate managers for engine, sound, file operations, sessions, and windows
- **Native Undo/Redo Infrastructure**: macOS UndoManager integration with 7 specialized undo state types, memory efficiency, and encapsulated API
- **Comprehensive Testing**: 183 tests ensuring system reliability and optimization correctness
- **Robust Error Handling**: User-friendly validation and import error management

## Enhanced ChessKit Features

The included ChessKit library has been significantly enhanced with:

- **Move Structure Revolution**: Separated `MetaMove` and `PositionComment` components
- **Assessment System Expansion**: From 9 basic to 135+ standardized assessments
- **Advanced Comment System**: Text, time, and visual annotations
- **Integer-Based MoveTree**: Reliable sequential indexing system (0, 1, 2, ...) with native undo/redo compatibility
- **Native Undo/Redo System**: macOS UndoManager integration with 7 specialized undo state types and memory efficiency *(Release 1.2.0)*
- **Encapsulated Index Resolution**: Clean API eliminating code duplication *(Release 1.2.0)*
- **Comprehensive FEN Validation**: Complete position validation with chess logic
- **PGN Parser Rewrite**: Robust tokenizer-based parsing with full annotation support

### **Release History (2025)**
- ✅ **Multi-Module Architecture**: Card-based navigation with Chess Analysis, Database, OCR, and Tournament Management *(Release 1.3.0)*
- ✅ **Multi-Window System**: Independent windows with coordinated lifecycle management *(Release 1.3.0)*
- ✅ **Chess Board Screenshot System**: Dual-mode export (clean/highlighted) with clipboard and file save *(Release 1.3.0)*  
- ✅ **Position Editor Screenshots**: Professional diagram generation with visual consistency *(Release 1.3.0)*
- ✅ **Extended Undo/Redo**: Full support for position editor and clipboard import operations *(Release 1.3.0)*
- ✅ **Native Undo/Redo Implementation**: Full macOS UndoManager integration with system-level shortcuts *(Release 1.2.0)*
- ✅ **Modular UI Architecture**: Separated ContentView from ChessView for future expansion *(Release 1.2.0)*  
- ✅ **UndoState Memory Optimization**: Reduced memory footprint by eliminating redundant fields *(Release 1.2.0)*
- ✅ **Encapsulated Index Resolution**: Clean API design eliminating code duplication *(Release 1.2.0)*
- ✅ **Architecture Refactoring**: GameSession separation from ChessGameViewModel *(Release 1.2.0)*
- ✅ **Enhanced Position Editor**: FEN validation, import error handling, delete before move functionality *(Release 1.1.0)*
- ✅ **Move Notation Reliability**: Fixed display issues for games starting with black to move
- ✅ **PGN Export Quality**: Corrected variation formatting and move number display
- ✅ **Position Editor Robustness**: Enhanced stability with custom starting positions
- ✅ **Edge Case Handling**: Improved behavior with non-standard game configurations
- ✅ **Code Simplification**: Streamlined move number tracking for better maintainability

## Getting Started

The application is fully functional and ready to use. Simply build and run the project in Xcode. All dependencies are included locally, so no external package downloads are required.

### **Requirements**
- macOS 13.0 or later
- Xcode 14.0 or later
- Swift 5.7 or later

## Documentation

Comprehensive documentation is available in the project:

- **FEATURES.md**: Complete feature documentation
- **chesskit-swift/CUSTOM_README.md**: Enhanced ChessKit API guide
- **chesskit-swift/CHANGELOG.md**: Version history and changes
- **TODO.md**: Development roadmap and planning
- **CLAUDE.md**: Comprehensive development history and context

## Development Status

**Current Version:** Release 1.3.0  
**Status:** Production-ready with multi-module professional chess suite architecture

The application has evolved from a basic chess library to a professional-grade chess analysis application with advanced editing capabilities, comprehensive validation systems, and native macOS integration.

## Future Development

Planned enhancements include:
- Visual annotation editing with command+click interface
- Database features for advanced search capabilities
- Tournament management systems
- Multiple engine support
- iOS companion app for face-to-face play

---

*MacChessBase - Professional Chess Analysis, Native on Mac*
