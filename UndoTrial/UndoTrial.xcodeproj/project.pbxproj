// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		8E3753932E2D26DB00883589 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8E3753772E2D26D800883589 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8E37537E2E2D26D800883589;
			remoteInfo = UndoTrial;
		};
		8E37539D2E2D26DB00883589 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8E3753772E2D26D800883589 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8E37537E2E2D26D800883589;
			remoteInfo = UndoTrial;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		8E37537F2E2D26D800883589 /* UndoTrial.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = UndoTrial.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8E3753922E2D26DB00883589 /* UndoTrialTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = UndoTrialTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8E37539C2E2D26DB00883589 /* UndoTrialUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = UndoTrialUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		8E3753812E2D26D800883589 /* UndoTrial */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = UndoTrial;
			sourceTree = "<group>";
		};
		8E3753952E2D26DB00883589 /* UndoTrialTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = UndoTrialTests;
			sourceTree = "<group>";
		};
		8E37539F2E2D26DB00883589 /* UndoTrialUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = UndoTrialUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		8E37537C2E2D26D800883589 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E37538F2E2D26DB00883589 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E3753992E2D26DB00883589 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		8E3753762E2D26D800883589 = {
			isa = PBXGroup;
			children = (
				8E3753812E2D26D800883589 /* UndoTrial */,
				8E3753952E2D26DB00883589 /* UndoTrialTests */,
				8E37539F2E2D26DB00883589 /* UndoTrialUITests */,
				8E3753802E2D26D800883589 /* Products */,
			);
			sourceTree = "<group>";
		};
		8E3753802E2D26D800883589 /* Products */ = {
			isa = PBXGroup;
			children = (
				8E37537F2E2D26D800883589 /* UndoTrial.app */,
				8E3753922E2D26DB00883589 /* UndoTrialTests.xctest */,
				8E37539C2E2D26DB00883589 /* UndoTrialUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8E37537E2E2D26D800883589 /* UndoTrial */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E3753A62E2D26DC00883589 /* Build configuration list for PBXNativeTarget "UndoTrial" */;
			buildPhases = (
				8E37537B2E2D26D800883589 /* Sources */,
				8E37537C2E2D26D800883589 /* Frameworks */,
				8E37537D2E2D26D800883589 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				8E3753812E2D26D800883589 /* UndoTrial */,
			);
			name = UndoTrial;
			packageProductDependencies = (
			);
			productName = UndoTrial;
			productReference = 8E37537F2E2D26D800883589 /* UndoTrial.app */;
			productType = "com.apple.product-type.application";
		};
		8E3753912E2D26DB00883589 /* UndoTrialTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E3753A92E2D26DC00883589 /* Build configuration list for PBXNativeTarget "UndoTrialTests" */;
			buildPhases = (
				8E37538E2E2D26DB00883589 /* Sources */,
				8E37538F2E2D26DB00883589 /* Frameworks */,
				8E3753902E2D26DB00883589 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8E3753942E2D26DB00883589 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8E3753952E2D26DB00883589 /* UndoTrialTests */,
			);
			name = UndoTrialTests;
			packageProductDependencies = (
			);
			productName = UndoTrialTests;
			productReference = 8E3753922E2D26DB00883589 /* UndoTrialTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		8E37539B2E2D26DB00883589 /* UndoTrialUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E3753AC2E2D26DC00883589 /* Build configuration list for PBXNativeTarget "UndoTrialUITests" */;
			buildPhases = (
				8E3753982E2D26DB00883589 /* Sources */,
				8E3753992E2D26DB00883589 /* Frameworks */,
				8E37539A2E2D26DB00883589 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8E37539E2E2D26DB00883589 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8E37539F2E2D26DB00883589 /* UndoTrialUITests */,
			);
			name = UndoTrialUITests;
			packageProductDependencies = (
			);
			productName = UndoTrialUITests;
			productReference = 8E37539C2E2D26DB00883589 /* UndoTrialUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8E3753772E2D26D800883589 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					8E37537E2E2D26D800883589 = {
						CreatedOnToolsVersion = 16.2;
					};
					8E3753912E2D26DB00883589 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 8E37537E2E2D26D800883589;
					};
					8E37539B2E2D26DB00883589 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 8E37537E2E2D26D800883589;
					};
				};
			};
			buildConfigurationList = 8E37537A2E2D26D800883589 /* Build configuration list for PBXProject "UndoTrial" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8E3753762E2D26D800883589;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 8E3753802E2D26D800883589 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8E37537E2E2D26D800883589 /* UndoTrial */,
				8E3753912E2D26DB00883589 /* UndoTrialTests */,
				8E37539B2E2D26DB00883589 /* UndoTrialUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8E37537D2E2D26D800883589 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E3753902E2D26DB00883589 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E37539A2E2D26DB00883589 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8E37537B2E2D26D800883589 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E37538E2E2D26DB00883589 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E3753982E2D26DB00883589 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		8E3753942E2D26DB00883589 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8E37537E2E2D26D800883589 /* UndoTrial */;
			targetProxy = 8E3753932E2D26DB00883589 /* PBXContainerItemProxy */;
		};
		8E37539E2E2D26DB00883589 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8E37537E2E2D26D800883589 /* UndoTrial */;
			targetProxy = 8E37539D2E2D26DB00883589 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		8E3753A42E2D26DC00883589 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		8E3753A52E2D26DC00883589 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		8E3753A72E2D26DC00883589 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = UndoTrial/UndoTrial.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"UndoTrial/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.UndoTrial;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		8E3753A82E2D26DC00883589 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = UndoTrial/UndoTrial.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"UndoTrial/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.UndoTrial;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		8E3753AA2E2D26DC00883589 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.UndoTrialTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/UndoTrial.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/UndoTrial";
			};
			name = Debug;
		};
		8E3753AB2E2D26DC00883589 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.UndoTrialTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/UndoTrial.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/UndoTrial";
			};
			name = Release;
		};
		8E3753AD2E2D26DC00883589 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.UndoTrialUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = UndoTrial;
			};
			name = Debug;
		};
		8E3753AE2E2D26DC00883589 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.UndoTrialUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = UndoTrial;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8E37537A2E2D26D800883589 /* Build configuration list for PBXProject "UndoTrial" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E3753A42E2D26DC00883589 /* Debug */,
				8E3753A52E2D26DC00883589 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E3753A62E2D26DC00883589 /* Build configuration list for PBXNativeTarget "UndoTrial" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E3753A72E2D26DC00883589 /* Debug */,
				8E3753A82E2D26DC00883589 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E3753A92E2D26DC00883589 /* Build configuration list for PBXNativeTarget "UndoTrialTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E3753AA2E2D26DC00883589 /* Debug */,
				8E3753AB2E2D26DC00883589 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E3753AC2E2D26DC00883589 /* Build configuration list for PBXNativeTarget "UndoTrialUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E3753AD2E2D26DC00883589 /* Debug */,
				8E3753AE2E2D26DC00883589 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8E3753772E2D26D800883589 /* Project object */;
}
