<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>colors</key>
	<array/>
	<key>images</key>
	<array>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Projects/MacChessBase/MacChessBase/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameBB</string>
			<key>relativePath</key>
			<string>./bB.imageset</string>
			<key>swiftSymbol</key>
			<string>bB</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Projects/MacChessBase/MacChessBase/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameBK</string>
			<key>relativePath</key>
			<string>./bK.imageset</string>
			<key>swiftSymbol</key>
			<string>bK</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Projects/MacChessBase/MacChessBase/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameBN</string>
			<key>relativePath</key>
			<string>./bN.imageset</string>
			<key>swiftSymbol</key>
			<string>bN</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Projects/MacChessBase/MacChessBase/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameBP</string>
			<key>relativePath</key>
			<string>./bP.imageset</string>
			<key>swiftSymbol</key>
			<string>bP</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Projects/MacChessBase/MacChessBase/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameBQ</string>
			<key>relativePath</key>
			<string>./bQ.imageset</string>
			<key>swiftSymbol</key>
			<string>bQ</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Projects/MacChessBase/MacChessBase/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameBR</string>
			<key>relativePath</key>
			<string>./bR.imageset</string>
			<key>swiftSymbol</key>
			<string>bR</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Projects/MacChessBase/MacChessBase/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameWB</string>
			<key>relativePath</key>
			<string>./wB.imageset</string>
			<key>swiftSymbol</key>
			<string>wB</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Projects/MacChessBase/MacChessBase/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameWK</string>
			<key>relativePath</key>
			<string>./wK.imageset</string>
			<key>swiftSymbol</key>
			<string>wK</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Projects/MacChessBase/MacChessBase/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameWN</string>
			<key>relativePath</key>
			<string>./wN.imageset</string>
			<key>swiftSymbol</key>
			<string>wN</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Projects/MacChessBase/MacChessBase/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameWP</string>
			<key>relativePath</key>
			<string>./wP.imageset</string>
			<key>swiftSymbol</key>
			<string>wP</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Projects/MacChessBase/MacChessBase/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameWQ</string>
			<key>relativePath</key>
			<string>./wQ.imageset</string>
			<key>swiftSymbol</key>
			<string>wQ</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Projects/MacChessBase/MacChessBase/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameWR</string>
			<key>relativePath</key>
			<string>./wR.imageset</string>
			<key>swiftSymbol</key>
			<string>wR</string>
		</dict>
	</array>
	<key>symbols</key>
	<array/>
</dict>
</plist>
