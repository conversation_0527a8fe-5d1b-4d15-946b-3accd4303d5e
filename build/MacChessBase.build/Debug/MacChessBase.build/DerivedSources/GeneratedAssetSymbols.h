#import <Foundation/Foundation.h>

#if __has_attribute(swift_private)
#define AC_SWIFT_PRIVATE __attribute__((swift_private))
#else
#define AC_SWIFT_PRIVATE
#endif

/// The "bB" asset catalog image resource.
static NSString * const ACImageNameBB AC_SWIFT_PRIVATE = @"bB";

/// The "bK" asset catalog image resource.
static NSString * const ACImageNameBK AC_SWIFT_PRIVATE = @"bK";

/// The "bN" asset catalog image resource.
static NSString * const ACImageNameBN AC_SWIFT_PRIVATE = @"bN";

/// The "bP" asset catalog image resource.
static NSString * const ACImageNameBP AC_SWIFT_PRIVATE = @"bP";

/// The "bQ" asset catalog image resource.
static NSString * const ACImageNameBQ AC_SWIFT_PRIVATE = @"bQ";

/// The "bR" asset catalog image resource.
static NSString * const ACImageNameBR AC_SWIFT_PRIVATE = @"bR";

/// The "wB" asset catalog image resource.
static NSString * const ACImageNameWB AC_SWIFT_PRIVATE = @"wB";

/// The "wK" asset catalog image resource.
static NSString * const ACImageNameWK AC_SWIFT_PRIVATE = @"wK";

/// The "wN" asset catalog image resource.
static NSString * const ACImageNameWN AC_SWIFT_PRIVATE = @"wN";

/// The "wP" asset catalog image resource.
static NSString * const ACImageNameWP AC_SWIFT_PRIVATE = @"wP";

/// The "wQ" asset catalog image resource.
static NSString * const ACImageNameWQ AC_SWIFT_PRIVATE = @"wQ";

/// The "wR" asset catalog image resource.
static NSString * const ACImageNameWR AC_SWIFT_PRIVATE = @"wR";

#undef AC_SWIFT_PRIVATE
