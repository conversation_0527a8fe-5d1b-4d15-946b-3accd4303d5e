/Users/<USER>/Projects/MacChessBase/MacChessBase/App/ContentView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/App/MacChessBaseApp.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/EngineManager.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/FIDEService.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/FileManager.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/SoundManager.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/SubscriptionService.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/WindowManager.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Utils/BoardCoordinateTransformer.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Utils/ChessKitExtensions.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/DragState.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSession.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSessionManager.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/PositionEditorViewModel.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/ChessBoardView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/ChessClockView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/ChessGameView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/ChessView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/DatabaseView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/DraggedPieceView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/EngineAnalysisView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/EngineSettingsView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/InteractiveMoveNotationView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/MainNavigationView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/MoveEditMenuView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/NavigationCardView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/OCRView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/OptimizedChessSquareView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/PGNMetadataView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/PositionEditorView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/SplitView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/TournamentView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/VariationCreationDialogView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/VariationCreationView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/VariationSelectionView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/VisualAnnotationsView.swift
/Users/<USER>/Projects/MacChessBase/MacChessBase/Item.swift
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/DerivedSources/GeneratedAssetSymbols.swift
