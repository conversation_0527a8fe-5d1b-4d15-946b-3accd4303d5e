"/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/EngineManager.swift":
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager.swiftconstvalues"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager.d"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager.dia"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager.swiftdeps"
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager.o"
"/Users/<USER>/Projects/MacChessBase/MacChessBase/App/ContentView.swift":
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView.o"
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView.swiftconstvalues"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView.dia"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView.swiftdeps"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView.d"
"/Users/<USER>/Projects/MacChessBase/MacChessBase/App/MacChessBaseApp.swift":
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp.swiftdeps"
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp.swiftconstvalues"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp.dia"
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp.o"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp.d"
