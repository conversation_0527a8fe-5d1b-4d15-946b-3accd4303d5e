"/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/FileManager.swift":
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager.swiftconstvalues"
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager.o"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager.d"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager.swiftdeps"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager.dia"
"/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/FIDEService.swift":
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService.d"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService.swiftdeps"
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService.swiftconstvalues"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService.dia"
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService.o"
"/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/SoundManager.swift":
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager.swiftconstvalues"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager.dia"
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager.o"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager.d"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager.swiftdeps"
