"/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift":
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel.swiftdeps"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel.d"
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel.o"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel.dia"
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel.swiftconstvalues"
"/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/DragState.swift":
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState.dia"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState.swiftdeps"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState.d"
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState.swiftconstvalues"
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState.o"
"/Users/<USER>/Projects/MacChessBase/MacChessBase/Utils/ChessKitExtensions.swift":
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions.o"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions.d"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions.swiftdeps"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions.dia"
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions.swiftconstvalues"
