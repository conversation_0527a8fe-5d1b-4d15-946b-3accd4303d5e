/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SubscriptionService.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/WindowManager.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/BoardCoordinateTransformer.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSession.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSessionManager.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorViewModel.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessBoardView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessClockView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DatabaseView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DraggedPieceView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineAnalysisView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineSettingsView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/InteractiveMoveNotationView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MainNavigationView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MoveEditMenuView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/NavigationCardView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OCRView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OptimizedChessSquareView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PGNMetadataView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SplitView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/TournamentView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationDialogView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationSelectionView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VisualAnnotationsView.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/Item.swiftconstvalues
/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues
