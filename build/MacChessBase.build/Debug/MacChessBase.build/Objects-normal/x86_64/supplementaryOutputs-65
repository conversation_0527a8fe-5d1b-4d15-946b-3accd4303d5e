"/Users/<USER>/Projects/MacChessBase/MacChessBase/App/ContentView.swift":
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ContentView.o"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ContentView.swiftdeps"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ContentView.dia"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ContentView.d"
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ContentView.swiftconstvalues"
"/Users/<USER>/Projects/MacChessBase/MacChessBase/App/MacChessBaseApp.swift":
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/MacChessBaseApp.o"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/MacChessBaseApp.dia"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/MacChessBaseApp.swiftdeps"
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/MacChessBaseApp.swiftconstvalues"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/MacChessBaseApp.d"
"/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/EngineManager.swift":
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/EngineManager.o"
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/EngineManager.swiftconstvalues"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/EngineManager.d"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/EngineManager.swiftdeps"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/EngineManager.dia"
