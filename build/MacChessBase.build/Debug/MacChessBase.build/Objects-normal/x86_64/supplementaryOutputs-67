"/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/WindowManager.swift":
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/WindowManager.swiftconstvalues"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/WindowManager.dia"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/WindowManager.d"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/WindowManager.swiftdeps"
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/WindowManager.o"
"/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/SubscriptionService.swift":
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/SubscriptionService.swiftdeps"
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/SubscriptionService.swiftconstvalues"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/SubscriptionService.d"
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/SubscriptionService.o"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/SubscriptionService.dia"
"/Users/<USER>/Projects/MacChessBase/MacChessBase/Utils/BoardCoordinateTransformer.swift":
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/BoardCoordinateTransformer.d"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/BoardCoordinateTransformer.dia"
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/BoardCoordinateTransformer.o"
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/BoardCoordinateTransformer.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/BoardCoordinateTransformer.swiftdeps"
