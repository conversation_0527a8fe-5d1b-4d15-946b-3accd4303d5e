"/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift":
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ChessGameViewModel.dia"
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ChessGameViewModel.swiftconstvalues"
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ChessGameViewModel.o"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ChessGameViewModel.d"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ChessGameViewModel.swiftdeps"
"/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/DragState.swift":
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/DragState.swiftconstvalues"
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/DragState.d"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/DragState.swiftdeps"
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/DragState.o"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/DragState.dia"
"/Users/<USER>/Projects/MacChessBase/MacChessBase/Utils/ChessKitExtensions.swift":
  dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ChessKitExtensions.d"
  const-values: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ChessKitExtensions.swiftconstvalues"
  object: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ChessKitExtensions.o"
  swift-dependencies: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ChessKitExtensions.swiftdeps"
  diagnostics: "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/x86_64/ChessKitExtensions.dia"
