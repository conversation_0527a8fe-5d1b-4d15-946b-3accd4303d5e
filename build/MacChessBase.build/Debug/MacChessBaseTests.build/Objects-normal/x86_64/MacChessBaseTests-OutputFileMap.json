{"": {"diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/MacChessBaseTests-master.dia", "emit-module-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/MacChessBaseTests-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/MacChessBaseTests-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/MacChessBaseTests-master.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/ChessGameViewModelTests.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessGameViewModelTests.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessGameViewModelTests.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessGameViewModelTests.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessGameViewModelTests.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessGameViewModelTests.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessGameViewModelTests.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessGameViewModelTests.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessGameViewModelTests~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/ChessKitExtensionsTests.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessKitExtensionsTests.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessKitExtensionsTests.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessKitExtensionsTests.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessKitExtensionsTests.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessKitExtensionsTests.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessKitExtensionsTests.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessKitExtensionsTests.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/ChessKitExtensionsTests~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/EngineManagerTests.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/EngineManagerTests.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/EngineManagerTests.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/EngineManagerTests.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/EngineManagerTests.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/EngineManagerTests.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/EngineManagerTests.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/EngineManagerTests.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/EngineManagerTests~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/FENValidationTests.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/FENValidationTests.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/FENValidationTests.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/FENValidationTests.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/FENValidationTests.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/FENValidationTests.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/FENValidationTests.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/FENValidationTests.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/FENValidationTests~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/GameSessionManagerTests.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/GameSessionManagerTests.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/GameSessionManagerTests.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/GameSessionManagerTests.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/GameSessionManagerTests.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/GameSessionManagerTests.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/GameSessionManagerTests.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/GameSessionManagerTests.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/GameSessionManagerTests~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/MacChessBaseTests.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/MacChessBaseTests.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/MacChessBaseTests.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/MacChessBaseTests.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/MacChessBaseTests.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/MacChessBaseTests.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/MacChessBaseTests.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/MacChessBaseTests.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/MacChessBaseTests~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/PerformanceTests.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PerformanceTests.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PerformanceTests.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PerformanceTests.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PerformanceTests.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PerformanceTests.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PerformanceTests.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PerformanceTests.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PerformanceTests~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/PositionEditorViewModelTests.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PositionEditorViewModelTests.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PositionEditorViewModelTests.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PositionEditorViewModelTests.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PositionEditorViewModelTests.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PositionEditorViewModelTests.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PositionEditorViewModelTests.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PositionEditorViewModelTests.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/PositionEditorViewModelTests~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/SimplifiedPerformanceTests.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/SimplifiedPerformanceTests.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/SimplifiedPerformanceTests.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/SimplifiedPerformanceTests.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/SimplifiedPerformanceTests.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/SimplifiedPerformanceTests.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/SimplifiedPerformanceTests.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/SimplifiedPerformanceTests.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBaseTests.build/Objects-normal/x86_64/SimplifiedPerformanceTests~partial.swiftmodule"}}