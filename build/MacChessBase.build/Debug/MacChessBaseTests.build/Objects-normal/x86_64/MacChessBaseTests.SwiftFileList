/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/ChessGameViewModelTests.swift
/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/ChessKitExtensionsTests.swift
/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/EngineManagerTests.swift
/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/FENValidationTests.swift
/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/GameSessionManagerTests.swift
/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/MacChessBaseTests.swift
/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/PerformanceTests.swift
/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/PositionEditorViewModelTests.swift
/Users/<USER>/Projects/MacChessBase/MacChessBaseTests/SimplifiedPerformanceTests.swift
