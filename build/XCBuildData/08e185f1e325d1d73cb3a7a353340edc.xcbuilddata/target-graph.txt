Target dependency graph (4 targets)
Target 'MacChessBaseTests' in project 'Mac<PERSON>hessBase'
➜ Explicit dependency on target '<PERSON><PERSON><PERSON>sB<PERSON>' in project 'MacChessBase'
➜ Explicit dependency on target 'ChessKit' in project 'ChessKit'
Target 'MacChessBase' in project '<PERSON><PERSON><PERSON>sB<PERSON>'
➜ Explicit dependency on target 'Chess<PERSON><PERSON>' in project 'ChessKit'
Target 'ChessKit' in project 'ChessK<PERSON>'
➜ Explicit dependency on target 'ChessKit' in project 'ChessKit'
Target 'ChessKit' in project 'ChessK<PERSON>' (no dependencies)