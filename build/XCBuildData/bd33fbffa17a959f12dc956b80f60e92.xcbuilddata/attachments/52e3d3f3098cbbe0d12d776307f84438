{"": {"diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBase-master.dia", "emit-module-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBase-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBase-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBase-master.swiftdeps"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/App/ContentView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ContentView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/App/MacChessBaseApp.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MacChessBaseApp~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Item.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/Item.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/Item.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/Item.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/Item.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/Item.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/Item.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/Item.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/Item~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/EngineManager.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineManager~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/FIDEService.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FIDEService~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/FileManager.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/FileManager~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/SoundManager.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SoundManager~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/SubscriptionService.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SubscriptionService.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SubscriptionService.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SubscriptionService.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SubscriptionService.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SubscriptionService.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SubscriptionService.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SubscriptionService.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SubscriptionService~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Services/WindowManager.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/WindowManager.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/WindowManager.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/WindowManager.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/WindowManager.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/WindowManager.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/WindowManager.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/WindowManager.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/WindowManager~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Utils/BoardCoordinateTransformer.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/BoardCoordinateTransformer.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/BoardCoordinateTransformer.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/BoardCoordinateTransformer.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/BoardCoordinateTransformer.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/BoardCoordinateTransformer.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/BoardCoordinateTransformer.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/BoardCoordinateTransformer.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/BoardCoordinateTransformer~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Utils/ChessKitExtensions.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessKitExtensions~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameViewModel~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/DragState.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DragState~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSession.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSession.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSession.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSession.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSession.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSession.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSession.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSession.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSession~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSessionManager.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSessionManager.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSessionManager.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSessionManager.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSessionManager.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSessionManager.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSessionManager.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSessionManager.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GameSessionManager~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/PositionEditorViewModel.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorViewModel.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorViewModel.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorViewModel.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorViewModel.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorViewModel.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorViewModel~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/ChessBoardView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessBoardView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessBoardView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessBoardView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessBoardView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessBoardView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessBoardView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessBoardView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessBoardView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/ChessClockView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessClockView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessClockView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessClockView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessClockView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessClockView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessClockView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessClockView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessClockView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/ChessGameView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessGameView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/ChessView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/ChessView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/DatabaseView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DatabaseView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DatabaseView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DatabaseView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DatabaseView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DatabaseView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DatabaseView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DatabaseView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DatabaseView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/DraggedPieceView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DraggedPieceView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DraggedPieceView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DraggedPieceView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DraggedPieceView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DraggedPieceView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DraggedPieceView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DraggedPieceView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/DraggedPieceView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/EngineAnalysisView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineAnalysisView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineAnalysisView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineAnalysisView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineAnalysisView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineAnalysisView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineAnalysisView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineAnalysisView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineAnalysisView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/EngineSettingsView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineSettingsView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineSettingsView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineSettingsView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineSettingsView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineSettingsView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineSettingsView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineSettingsView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/EngineSettingsView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/InteractiveMoveNotationView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/InteractiveMoveNotationView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/InteractiveMoveNotationView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/InteractiveMoveNotationView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/InteractiveMoveNotationView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/InteractiveMoveNotationView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/InteractiveMoveNotationView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/InteractiveMoveNotationView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/InteractiveMoveNotationView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/MainNavigationView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MainNavigationView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MainNavigationView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MainNavigationView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MainNavigationView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MainNavigationView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MainNavigationView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MainNavigationView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MainNavigationView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/MoveEditMenuView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MoveEditMenuView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MoveEditMenuView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MoveEditMenuView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MoveEditMenuView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MoveEditMenuView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MoveEditMenuView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MoveEditMenuView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/MoveEditMenuView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/NavigationCardView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/NavigationCardView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/NavigationCardView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/NavigationCardView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/NavigationCardView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/NavigationCardView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/NavigationCardView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/NavigationCardView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/NavigationCardView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/OCRView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OCRView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OCRView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OCRView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OCRView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OCRView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OCRView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OCRView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OCRView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/OptimizedChessSquareView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OptimizedChessSquareView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OptimizedChessSquareView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OptimizedChessSquareView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OptimizedChessSquareView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OptimizedChessSquareView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OptimizedChessSquareView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OptimizedChessSquareView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/OptimizedChessSquareView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/PGNMetadataView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PGNMetadataView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PGNMetadataView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PGNMetadataView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PGNMetadataView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PGNMetadataView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PGNMetadataView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PGNMetadataView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PGNMetadataView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/PositionEditorView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/PositionEditorView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/SplitView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SplitView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SplitView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SplitView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SplitView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SplitView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SplitView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SplitView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/SplitView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/TournamentView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/TournamentView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/TournamentView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/TournamentView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/TournamentView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/TournamentView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/TournamentView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/TournamentView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/TournamentView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/VariationCreationDialogView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationDialogView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationDialogView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationDialogView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationDialogView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationDialogView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationDialogView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationDialogView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationDialogView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/VariationCreationView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationCreationView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/VariationSelectionView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationSelectionView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationSelectionView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationSelectionView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationSelectionView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationSelectionView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationSelectionView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationSelectionView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VariationSelectionView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/MacChessBase/Views/VisualAnnotationsView.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VisualAnnotationsView.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VisualAnnotationsView.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VisualAnnotationsView.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VisualAnnotationsView.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VisualAnnotationsView.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VisualAnnotationsView.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VisualAnnotationsView.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/VisualAnnotationsView~partial.swiftmodule"}, "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Projects/MacChessBase/build/MacChessBase.build/Debug/MacChessBase.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}}