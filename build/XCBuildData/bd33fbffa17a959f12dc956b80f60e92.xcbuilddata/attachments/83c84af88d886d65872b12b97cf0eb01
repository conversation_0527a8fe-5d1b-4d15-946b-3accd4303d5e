{"": {"diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SwiftSoup-master.dia", "emit-module-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SwiftSoup-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SwiftSoup-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SwiftSoup-master.swiftdeps"}, "/Users/<USER>/SwiftSoup/Sources/ArrayExt.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ArrayExt.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ArrayExt.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ArrayExt.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ArrayExt.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ArrayExt.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ArrayExt.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ArrayExt.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ArrayExt~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Attribute.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attribute.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attribute.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attribute.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attribute.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attribute.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attribute.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attribute.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attribute~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Attributes.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attributes.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attributes.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attributes.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attributes.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attributes.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attributes.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attributes.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Attributes~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/BinarySearch.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BinarySearch.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BinarySearch.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BinarySearch.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BinarySearch.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BinarySearch.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BinarySearch.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BinarySearch.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BinarySearch~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/BooleanAttribute.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BooleanAttribute.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BooleanAttribute.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BooleanAttribute.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BooleanAttribute.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BooleanAttribute.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BooleanAttribute.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BooleanAttribute.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/BooleanAttribute~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/CharacterExt.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterExt.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterExt.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterExt.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterExt.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterExt.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterExt.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterExt.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterExt~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/CharacterReader.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterReader.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterReader.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterReader.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterReader.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterReader.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterReader.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterReader.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CharacterReader~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Cleaner.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Cleaner.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Cleaner.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Cleaner.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Cleaner.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Cleaner.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Cleaner.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Cleaner.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Cleaner~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Collector.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Collector.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Collector.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Collector.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Collector.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Collector.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Collector.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Collector.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Collector~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/CombiningEvaluator.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CombiningEvaluator.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CombiningEvaluator.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CombiningEvaluator.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CombiningEvaluator.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CombiningEvaluator.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CombiningEvaluator.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CombiningEvaluator.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CombiningEvaluator~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Comment.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Comment.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Comment.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Comment.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Comment.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Comment.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Comment.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Comment.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Comment~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Connection.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Connection.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Connection.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Connection.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Connection.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Connection.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Connection.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Connection.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Connection~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/CssSelector.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CssSelector.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CssSelector.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CssSelector.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CssSelector.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CssSelector.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CssSelector.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CssSelector.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/CssSelector~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/DataNode.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataNode.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataNode.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataNode.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataNode.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataNode.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataNode.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataNode.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataNode~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/DataUtil.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataUtil.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataUtil.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataUtil.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataUtil.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataUtil.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataUtil.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataUtil.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DataUtil~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Document.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Document.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Document.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Document.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Document.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Document.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Document.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Document.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Document~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/DocumentType.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DocumentType.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DocumentType.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DocumentType.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DocumentType.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DocumentType.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DocumentType.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DocumentType.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/DocumentType~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Element.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Element.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Element.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Element.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Element.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Element.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Element.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Element.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Element~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Elements.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Elements.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Elements.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Elements.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Elements.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Elements.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Elements.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Elements.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Elements~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Entities.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Entities.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Entities.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Entities.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Entities.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Entities.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Entities.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Entities.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Entities~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Evaluator.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Evaluator.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Evaluator.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Evaluator.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Evaluator.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Evaluator.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Evaluator.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Evaluator.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Evaluator~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Exception.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Exception.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Exception.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Exception.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Exception.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Exception.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Exception.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Exception.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Exception~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/FormElement.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/FormElement.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/FormElement.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/FormElement.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/FormElement.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/FormElement.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/FormElement.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/FormElement.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/FormElement~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/HtmlTreeBuilder.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilder.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilder.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilder.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilder.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilder.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilder.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilder.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilder~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/HtmlTreeBuilderState.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilderState.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilderState.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilderState.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilderState.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilderState.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilderState.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilderState.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HtmlTreeBuilderState~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/HttpStatusException.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HttpStatusException.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HttpStatusException.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HttpStatusException.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HttpStatusException.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HttpStatusException.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HttpStatusException.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HttpStatusException.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/HttpStatusException~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Mutex.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Mutex.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Mutex.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Mutex.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Mutex.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Mutex.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Mutex.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Mutex.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Mutex~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Node.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Node.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Node.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Node.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Node.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Node.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Node.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Node.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Node~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/NodeTraversor.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeTraversor.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeTraversor.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeTraversor.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeTraversor.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeTraversor.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeTraversor.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeTraversor.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeTraversor~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/NodeVisitor.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeVisitor.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeVisitor.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeVisitor.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeVisitor.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeVisitor.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeVisitor.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeVisitor.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/NodeVisitor~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/OrderedSet.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/OrderedSet.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/OrderedSet.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/OrderedSet.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/OrderedSet.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/OrderedSet.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/OrderedSet.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/OrderedSet.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/OrderedSet~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/ParseError.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseError.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseError.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseError.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseError.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseError.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseError.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseError.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseError~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/ParseErrorList.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseErrorList.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseErrorList.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseErrorList.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseErrorList.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseErrorList.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseErrorList.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseErrorList.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseErrorList~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/ParseSettings.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseSettings.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseSettings.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseSettings.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseSettings.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseSettings.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseSettings.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseSettings.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParseSettings~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Parser.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Parser.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Parser.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Parser.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Parser.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Parser.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Parser.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Parser.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Parser~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/ParsingStrings.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParsingStrings.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParsingStrings.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParsingStrings.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParsingStrings.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParsingStrings.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParsingStrings.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParsingStrings.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/ParsingStrings~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Pattern.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Pattern.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Pattern.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Pattern.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Pattern.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Pattern.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Pattern.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Pattern.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Pattern~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/QueryParser.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/QueryParser.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/QueryParser.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/QueryParser.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/QueryParser.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/QueryParser.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/QueryParser.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/QueryParser.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/QueryParser~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/SerializationException.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SerializationException.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SerializationException.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SerializationException.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SerializationException.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SerializationException.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SerializationException.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SerializationException.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SerializationException~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/SimpleDictionary.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SimpleDictionary.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SimpleDictionary.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SimpleDictionary.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SimpleDictionary.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SimpleDictionary.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SimpleDictionary.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SimpleDictionary.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SimpleDictionary~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/StreamReader.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StreamReader.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StreamReader.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StreamReader.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StreamReader.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StreamReader.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StreamReader.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StreamReader.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StreamReader~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/String.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/String.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/String.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/String.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/String.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/String.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/String.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/String.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/String~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/StringBuilder.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringBuilder.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringBuilder.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringBuilder.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringBuilder.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringBuilder.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringBuilder.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringBuilder.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringBuilder~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/StringUtil.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringUtil.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringUtil.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringUtil.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringUtil.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringUtil.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringUtil.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringUtil.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StringUtil~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/StructuralEvaluator.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StructuralEvaluator.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StructuralEvaluator.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StructuralEvaluator.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StructuralEvaluator.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StructuralEvaluator.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StructuralEvaluator.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StructuralEvaluator.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/StructuralEvaluator~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/SwiftSoup.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SwiftSoup.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SwiftSoup.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SwiftSoup.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SwiftSoup.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SwiftSoup.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SwiftSoup.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SwiftSoup.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/SwiftSoup~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Tag.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tag.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tag.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tag.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tag.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tag.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tag.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tag.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tag~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/TextNode.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TextNode.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TextNode.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TextNode.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TextNode.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TextNode.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TextNode.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TextNode.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TextNode~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Token.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Token.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Token.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Token.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Token.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Token.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Token.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Token.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Token~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/TokenQueue.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokenQueue.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokenQueue.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokenQueue.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokenQueue.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokenQueue.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokenQueue.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokenQueue.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokenQueue~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Tokeniser.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tokeniser.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tokeniser.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tokeniser.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tokeniser.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tokeniser.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tokeniser.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tokeniser.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Tokeniser~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/TokeniserState.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokeniserState.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokeniserState.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokeniserState.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokeniserState.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokeniserState.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokeniserState.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokeniserState.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TokeniserState~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/TreeBuilder.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TreeBuilder.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TreeBuilder.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TreeBuilder.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TreeBuilder.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TreeBuilder.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TreeBuilder.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TreeBuilder.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/TreeBuilder~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/UTF8Arrays.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UTF8Arrays.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UTF8Arrays.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UTF8Arrays.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UTF8Arrays.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UTF8Arrays.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UTF8Arrays.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UTF8Arrays.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UTF8Arrays~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/UnfairLock.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnfairLock.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnfairLock.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnfairLock.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnfairLock.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnfairLock.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnfairLock.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnfairLock.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnfairLock~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/UnicodeScalar.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnicodeScalar.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnicodeScalar.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnicodeScalar.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnicodeScalar.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnicodeScalar.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnicodeScalar.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnicodeScalar.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/UnicodeScalar~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Validate.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Validate.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Validate.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Validate.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Validate.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Validate.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Validate.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Validate.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Validate~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/Whitelist.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Whitelist.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Whitelist.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Whitelist.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Whitelist.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Whitelist.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Whitelist.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Whitelist.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/Whitelist~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/XmlDeclaration.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlDeclaration.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlDeclaration.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlDeclaration.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlDeclaration.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlDeclaration.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlDeclaration.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlDeclaration.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlDeclaration~partial.swiftmodule"}, "/Users/<USER>/SwiftSoup/Sources/XmlTreeBuilder.swift": {"const-values": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlTreeBuilder.swiftconstvalues", "dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlTreeBuilder.d", "diagnostics": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlTreeBuilder.dia", "index-unit-output-path": "/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlTreeBuilder.o", "llvm-bc": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlTreeBuilder.bc", "object": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlTreeBuilder.o", "swift-dependencies": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlTreeBuilder.swiftdeps", "swiftmodule": "/Users/<USER>/SwiftSoup/build/SwiftSoup.build/Debug/SwiftSoup.build/Objects-normal/x86_64/XmlTreeBuilder~partial.swiftmodule"}}