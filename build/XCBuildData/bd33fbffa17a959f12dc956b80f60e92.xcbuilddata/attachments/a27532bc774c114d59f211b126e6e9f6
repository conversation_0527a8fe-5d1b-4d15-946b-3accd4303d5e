/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Attacks.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Bitboard.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PieceSet.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square+BB.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Board.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Clock.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Configuration.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Game.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Move.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/MoveTree.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser+Regex.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EngineLANParser.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENParser.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/FENValidator.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser+Regex.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/PGNParser.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser+Regex.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/SANParser.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Piece.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Position.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Castling.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/EnPassant.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Square.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/UndoState.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/arm64/Comparable+Bounded.o
