/Users/<USER>/SwiftSoup/Sources/ArrayExt.swift
/Users/<USER>/SwiftSoup/Sources/Attribute.swift
/Users/<USER>/SwiftSoup/Sources/Attributes.swift
/Users/<USER>/SwiftSoup/Sources/BinarySearch.swift
/Users/<USER>/SwiftSoup/Sources/BooleanAttribute.swift
/Users/<USER>/SwiftSoup/Sources/CharacterExt.swift
/Users/<USER>/SwiftSoup/Sources/CharacterReader.swift
/Users/<USER>/SwiftSoup/Sources/Cleaner.swift
/Users/<USER>/SwiftSoup/Sources/Collector.swift
/Users/<USER>/SwiftSoup/Sources/CombiningEvaluator.swift
/Users/<USER>/SwiftSoup/Sources/Comment.swift
/Users/<USER>/SwiftSoup/Sources/Connection.swift
/Users/<USER>/SwiftSoup/Sources/CssSelector.swift
/Users/<USER>/SwiftSoup/Sources/DataNode.swift
/Users/<USER>/SwiftSoup/Sources/DataUtil.swift
/Users/<USER>/SwiftSoup/Sources/Document.swift
/Users/<USER>/SwiftSoup/Sources/DocumentType.swift
/Users/<USER>/SwiftSoup/Sources/Element.swift
/Users/<USER>/SwiftSoup/Sources/Elements.swift
/Users/<USER>/SwiftSoup/Sources/Entities.swift
/Users/<USER>/SwiftSoup/Sources/Evaluator.swift
/Users/<USER>/SwiftSoup/Sources/Exception.swift
/Users/<USER>/SwiftSoup/Sources/FormElement.swift
/Users/<USER>/SwiftSoup/Sources/HtmlTreeBuilder.swift
/Users/<USER>/SwiftSoup/Sources/HtmlTreeBuilderState.swift
/Users/<USER>/SwiftSoup/Sources/HttpStatusException.swift
/Users/<USER>/SwiftSoup/Sources/Mutex.swift
/Users/<USER>/SwiftSoup/Sources/Node.swift
/Users/<USER>/SwiftSoup/Sources/NodeTraversor.swift
/Users/<USER>/SwiftSoup/Sources/NodeVisitor.swift
/Users/<USER>/SwiftSoup/Sources/OrderedSet.swift
/Users/<USER>/SwiftSoup/Sources/ParseError.swift
/Users/<USER>/SwiftSoup/Sources/ParseErrorList.swift
/Users/<USER>/SwiftSoup/Sources/ParseSettings.swift
/Users/<USER>/SwiftSoup/Sources/Parser.swift
/Users/<USER>/SwiftSoup/Sources/ParsingStrings.swift
/Users/<USER>/SwiftSoup/Sources/Pattern.swift
/Users/<USER>/SwiftSoup/Sources/QueryParser.swift
/Users/<USER>/SwiftSoup/Sources/SerializationException.swift
/Users/<USER>/SwiftSoup/Sources/SimpleDictionary.swift
/Users/<USER>/SwiftSoup/Sources/StreamReader.swift
/Users/<USER>/SwiftSoup/Sources/String.swift
/Users/<USER>/SwiftSoup/Sources/StringBuilder.swift
/Users/<USER>/SwiftSoup/Sources/StringUtil.swift
/Users/<USER>/SwiftSoup/Sources/StructuralEvaluator.swift
/Users/<USER>/SwiftSoup/Sources/SwiftSoup.swift
/Users/<USER>/SwiftSoup/Sources/Tag.swift
/Users/<USER>/SwiftSoup/Sources/TextNode.swift
/Users/<USER>/SwiftSoup/Sources/Token.swift
/Users/<USER>/SwiftSoup/Sources/TokenQueue.swift
/Users/<USER>/SwiftSoup/Sources/Tokeniser.swift
/Users/<USER>/SwiftSoup/Sources/TokeniserState.swift
/Users/<USER>/SwiftSoup/Sources/TreeBuilder.swift
/Users/<USER>/SwiftSoup/Sources/UTF8Arrays.swift
/Users/<USER>/SwiftSoup/Sources/UnfairLock.swift
/Users/<USER>/SwiftSoup/Sources/UnicodeScalar.swift
/Users/<USER>/SwiftSoup/Sources/Validate.swift
/Users/<USER>/SwiftSoup/Sources/Whitelist.swift
/Users/<USER>/SwiftSoup/Sources/XmlDeclaration.swift
/Users/<USER>/SwiftSoup/Sources/XmlTreeBuilder.swift
