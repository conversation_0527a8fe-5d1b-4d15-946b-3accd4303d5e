{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildAndRun"}, "configuredTargets": [{"guid": "5382e9969f2585248a7890b7c0f5f916af91f57346d0d3db156476c3941d5add"}], "containerPath": "/Users/<USER>/Projects/MacChessBase/MacChessBase.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "NO"}}}}, "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": false, "useLegacyBuildLocations": false, "useParallelTargets": true}