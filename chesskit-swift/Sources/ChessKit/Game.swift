//
//  Game.swift
//  ChessKit
//

import Foundation

/// Represents a chess game.
///
/// This object is the entry point for interacting with a full
/// chess game within `ChessKit`. It provides methods for
/// making moves and publishes the played moves in an observable way.
public struct Game: Hashable, Sendable {

  // MARK: Properties

  /// The move tree representing all moves made in the game.
  public private(set) var moves: MoveTree
  /// The move tree index of the starting position in the game.
  public private(set) var startingIndex: MoveTree.MoveIndex
  /// A dictionary of every position in the game, keyed by move hash.
  public private(set) var positions: [MoveTree.MoveIndex: Position]
  /// Contains the tag pairs for this game.
  public var tags: Tags

  /// The starting position of the game.
  public var startingPosition: Position? {
    positions[startingIndex]
  }

  // MARK: Initializer

  /// Initialize a game with a starting position.
  ///
  /// - parameter position: The starting position of the game.
  /// - parameter tags: The PGN tags associated with this game.
  ///
  /// Defaults to the starting position.
  public init(startingWith position: Position = .standard, tags: Tags? = nil) {
    // Random starting position
    moves = MoveTree(position.sideToMove.opposite, position.clock.fullmoves - 1)
    let startingIndex = MoveTree.minimumIndex
    self.startingIndex = startingIndex
    positions = [startingIndex: position]
    self.tags = tags ?? .init()

    // moves.minimumIndex = startingIndex
  }

  /// Initialize a game with a PGN string.
  ///
  /// - parameter pgn: A string containing a PGN representation of
  /// a game.
  ///
  /// This initalizer fails if the PGN is invalid.
  public init?(pgn: String) {
    guard let parsed = PGNParser.parse(game: pgn) else {
      return nil
    }
    moves = parsed.moves
    startingIndex = MoveTree.minimumIndex
    positions = parsed.positions
    tags = parsed.tags
  }

  // MARK: Moves

  /// Perform the provided move in the game with optional promotion.
  ///
  /// - parameter move: The move to perform.
  /// - parameter index: The current move index to make the move from.
  /// If this parameter is `nil` or omitted, the move is made from the
  /// last move in the main variation branch.
  /// - parameter promotionPiece: The piece kind to promote to if this is a pawn promotion move. If `nil`, no promotion is performed.
  /// - returns: The move index of the resulting position. If the
  /// move couldn't be made, the provided `index` is returned directly.
  ///
  /// This method does not make any move legality assumptions,
  /// it will attempt to make the move defined by `move` by moving
  /// pieces at the provided starting/ending squares and making any
  /// necessary captures, promotions, etc. It is the responsibility
  /// of the caller to ensure the move is legal, see the ``Board`` struct.
  ///
  /// If `move` is the same as the upcoming move in the
  /// current variation of `index`, the move is not made, otherwise
  /// another variation with the same first move as the existing one
  /// would be created.
  @discardableResult
  public mutating func makeWithPromotion(
    move: Move,
    from index: MoveTree.MoveIndex,
    promotionPiece: Piece.Kind? = nil
  ) -> MoveTree.MoveIndex {
    if let existingMoveIndex = moves.hasNextMove(containing: move, for: index) {
      // if attempted move already exists next in the variation,
      // skip making it and return the corresponding index
      return existingMoveIndex
    }

    let newIndex = moves.add(move: move, toParentIndex: index)

    guard let currentPosition = positions[index] else {
      return index
    }

    var newPosition = currentPosition

    let metaMove = move.metaMove!

    switch metaMove.result {
    case .move:
      newPosition.moveWithPromotion(pieceAt: metaMove.start, to: metaMove.end, promotionPiece: promotionPiece)
      if metaMove.piece.kind == .pawn { newPosition.resetHalfmoveClock() }
    case let .capture(capturedPiece):
      newPosition.remove(capturedPiece)
      newPosition.moveWithPromotion(pieceAt: metaMove.start, to: metaMove.end, promotionPiece: promotionPiece)
      newPosition.resetHalfmoveClock()
    case let .castle(castling):
      newPosition.castle(castling)
    }

    if let promotedPiece = metaMove.promotedPiece {
      newPosition.promote(pieceAt: metaMove.end, to: promotedPiece.kind)
    }

    positions[newIndex] = newPosition
    return newIndex
  }

  /// Perform the provided move in the game.
  ///
  /// - parameter move: The move to perform.
  /// - parameter index: The current move index to make the move from.
  /// If this parameter is `nil` or omitted, the move is made from the
  /// last move in the main variation branch.
  /// - returns: The move index of the resulting position. If the
  /// move couldn't be made, the provided `index` is returned directly.
  ///
  /// This method does not make any move legality assumptions,
  /// it will attempt to make the move defined by `move` by moving
  /// pieces at the provided starting/ending squares and making any
  /// necessary captures, promotions, etc. It is the responsibility
  /// of the caller to ensure the move is legal, see the ``Board`` struct.
  ///
  /// If `move` is the same as the upcoming move in the
  /// current variation of `index`, the move is not made, otherwise
  /// another variation with the same first move as the existing one
  /// would be created.
  @discardableResult
  public mutating func make(
    move: Move,
    from index: MoveTree.MoveIndex
  ) -> MoveTree.MoveIndex {
    return makeWithPromotion(move: move, from: index, promotionPiece: nil)
  }

  /// Perform the provided move in the game with optional promotion.
  ///
  /// - parameter moveString: The SAN string of the move to perform.
  /// - parameter index: The current move index to make the move from.
  /// If this parameter is `nil` or omitted, the move is made from the
  /// last move in the main variation branch.
  /// - parameter promotionPiece: The piece kind to promote to if this is a pawn promotion move. If `nil`, no promotion is performed.
  /// - returns: The move index of the resulting position. If the
  /// move couldn't be made, the provided `index` is returned directly.
  ///
  /// This method does not make any move legality assumptions,
  /// it will attempt to make the move defined by `moveString` by moving
  /// pieces at the provided starting/ending squares and making any
  /// necessary captures, promotions, etc. It is the responsibility
  /// of the caller to ensure the move is legal, see the ``Board`` struct.
  @discardableResult
  public mutating func makeWithPromotion(
    move moveString: String,
    from index: MoveTree.MoveIndex,
    promotionPiece: Piece.Kind? = nil
  ) -> MoveTree.MoveIndex {
    guard let position = positions[index], let metaMove = SANParser.parse(metaMove: moveString, in: position)
    else {
      return index
    }
    return makeWithPromotion(move: Move(metaMove: metaMove), from: index, promotionPiece: promotionPiece)
  }

  /// Perform the provided move in the game.
  ///
  /// - parameter moveString: The SAN string of the move to perform.
  /// - parameter index: The current move index to make the move from.
  /// If this parameter is `nil` or omitted, the move is made from the
  /// last move in the main variation branch.
  /// - returns: The move index of the resulting position. If the
  /// move couldn't be made, the provided `index` is returned directly.
  ///
  /// This method does not make any move legality assumptions,
  /// it will attempt to make the move defined by `moveString` by moving
  /// pieces at the provided starting/ending squares and making any
  /// necessary captures, promotions, etc. It is the responsibility
  /// of the caller to ensure the move is legal, see the ``Board`` struct.
  @discardableResult
  public mutating func make(
    move moveString: String,
    from index: MoveTree.MoveIndex
  ) -> MoveTree.MoveIndex {
    return makeWithPromotion(move: moveString, from: index, promotionPiece: nil)
  }

  /// Perform the provided moves in the game.
  ///
  /// - parameter moveStrings: An array of SAN strings of the moves to perform.
  /// - parameter index: The current move index to make the moves from.
  /// If this parameter is `nil` or omitted, the move is made from the
  /// last move in the main variation branch.
  /// - returns: The move index of the resulting position. If the
  /// moves couldn't be made, the provided `index` is returned directly.
  ///
  /// This method does not make any move legality assumptions,
  /// it will attempt to make the moves defined by `moveStrings` by moving
  /// pieces at the provided starting/ending squares and making any
  /// necessary captures, promotions, etc. It is the responsibility
  /// of the caller to ensure the moves are legal, see the ``Board`` struct.
  @discardableResult
  public mutating func make(
    moves moveStrings: [String],
    from index: MoveTree.MoveIndex
  ) -> MoveTree.MoveIndex {
    var index = index

    for moveString in moveStrings {
      index = make(move: moveString, from: index)
    }

    return index
  }

  /// The PGN represenation of the game.
  public var pgn: String {
    PGNParser.convert(game: self)
  }

  // MARK: - Move Tree Editing

  /// Promotes a variation to become a higher priority variation.
  ///
  /// - parameter index: The index of any move within the variation to promote.
  /// - returns: `true` if the promotion was successful, `false` if no promotion was needed or possible.
  @discardableResult
  public mutating func promote(index: MoveTree.MoveIndex) -> Bool {
    return moves.promote(index: index)
  }
    
  /// Deletes a move and all subsequent moves from the game.
  ///
  /// This method removes the specified move and all its descendants from the move tree,
  /// and also removes the corresponding positions from the positions dictionary.
  ///
  /// - parameter index: The index of the move to delete.
  /// - returns: `true` if the move was successfully deleted, `false` if the move was not found.
  @discardableResult
  public mutating func delete(at index: MoveTree.MoveIndex) -> Bool {
    // Collect all indices that will be deleted
    var indicesToDelete: [MoveTree.MoveIndex] = []
    collectIndicesToDelete(from: index, into: &indicesToDelete)

    // Remove positions for all deleted indices
    for indexToDelete in indicesToDelete {
      positions.removeValue(forKey: indexToDelete)
    }

    // Delete from move tree
    return moves.delete(at: index)
  }

  /// Deletes all moves after (but not including) the specified move index.
  /// This includes both the next move and all children (variations).
  ///
  /// - parameter index: The index of the move from which to delete all subsequent moves
  /// - returns: `true` if any moves were deleted, `false` if no moves were found to delete
  @discardableResult
  public mutating func deleteAllAfter(index: MoveTree.MoveIndex) -> Bool {
    guard let node = moves.dictionary[index] else { return false }
    
    var indicesToDelete: [MoveTree.MoveIndex] = []
    
    // Collect next move and all its descendants
    if let next = node.next {
      collectIndicesToDelete(from: next.index, into: &indicesToDelete)
    }
    
    // Collect all children (variations) and their descendants
    for child in node.children {
      collectIndicesToDelete(from: child.index, into: &indicesToDelete)
    }
    
    // If no moves to delete, return false
    if indicesToDelete.isEmpty {
      return false
    }
    
    // Remove positions for all deleted indices
    for indexToDelete in indicesToDelete {
      positions.removeValue(forKey: indexToDelete)
    }
    
    // Delete from move tree
    return moves.deleteAllAfter(index: index)
  }

  public mutating func overwrite(move: Move, from index: MoveTree.MoveIndex) -> MoveTree.MoveIndex {
    let success = deleteAllAfter(index: index)
    if !success {
      return index
    }
    return make(move: move, from: index)
  }
  
  public mutating func overwrite(move moveString: String, from index: MoveTree.MoveIndex) -> MoveTree.MoveIndex {
    guard let position = positions[index], let metaMove = SANParser.parse(metaMove: moveString, in: position)
    else {
      return index
    }
    return overwrite(move: Move(metaMove: metaMove), from: index)
  }

  /// Promotes a variation to become the main variation.
  ///
  /// - parameter index: The index of any move within the variation to promote to main.
  /// - returns: `true` if the promotion was successful, `false` if the move was not found.
  @discardableResult
  public mutating func promoteToMainVariation(index: MoveTree.MoveIndex) -> Bool {
    return moves.promoteToMainVariation(index: index)
  }

  /// Deletes all moves before the specified move, making that move the new starting position.
  ///
  /// This method removes all moves that occurred before the specified move index,
  /// effectively making the position at the specified move the new starting position.
  /// The game is restructured to start from this position with updated FEN.
  ///
  /// - parameter index: The index of the move that should become the new starting point.
  /// - returns: `true` if the operation was successful, `false` if the move was not found.
  ///
  /// **Use Cases**:
  /// - Creating a new game from a middle position
  /// - Removing opening moves to focus on specific positions
  /// - Converting analysis positions to starting positions
  ///
  /// **Example**: In the game `1. e4 e5 2. Nf3 Nc6 3. Bb5`, calling `deleteBeforeMove`
  /// with the index of `3. Bb5` will result in a new game starting from that position.
  @discardableResult
  public mutating func deleteBeforeMove(at index: MoveTree.MoveIndex) -> Bool {
    // Get the position before the target move (this will be our new starting position)
    guard positions[index] != nil else {
      return false // Move not found
    }
    
    // Get the position that was BEFORE the target move was made
    // We need to find the position at the previous move index
    guard let targetNode = moves.dictionary[index],
          let previousNode = targetNode.previous,
          let newStartingPosition = positions[previousNode.index] else {
      return false // Could not determine previous position
    }
    
    // Perform the move tree restructuring
    let deletedIndices = moves.deleteBeforeMove(at: index)
    if deletedIndices.isEmpty {
      return false // Move tree operation failed
    }
    
    // Remove positions for all deleted moves
    for deletedIndex in deletedIndices {
      positions.removeValue(forKey: deletedIndex)
    }
    
    // Update the starting position to be the position before our target move
    positions[MoveTree.minimumIndex] = newStartingPosition
    startingIndex = MoveTree.minimumIndex
    
    // Update tags to reflect the new starting position
    // Set the FEN tag to represent the new starting position
    tags.fen = newStartingPosition.fen
    tags.setUp = "1" // Indicate this is a custom starting position
    
    return true
  }

  /// Recursively collects all indices that would be deleted when deleting from the given index.
  private func collectIndicesToDelete(from index: MoveTree.MoveIndex, into result: inout [MoveTree.MoveIndex]) {
    guard let node = moves.dictionary[index] else { return }

    result.append(index)

    // Collect next move
    if let next = node.next {
      collectIndicesToDelete(from: next.index, into: &result)
    }

    // Collect all sibling variations
    for child in node.children {
      collectIndicesToDelete(from: child.index, into: &result)
    }
  }

}

// MARK: - CustomStringConvertible
extension Game: CustomStringConvertible {

  public var description: String {
    pgn
  }

}

// MARK: - Tags
extension Game {

  /// Represents a PGN tag pair.
  @propertyWrapper
  public struct Tag: Hashable, Sendable {

    /// The name of the tag pair.
    ///
    /// Used as the key in a PGN tag pair.
    public var name: String

    /// The value of the tag pair.
    ///
    /// Appears at the top of the PGN after the
    /// corresponding ``name``, within brackets.
    public var wrappedValue: String = ""

    /// The projected value of this ``Tag`` object.
    public var projectedValue: Tag { self }

    /// The PGN representation of this tag.
    ///
    /// Formatted as `[Name "Value"]`.
    public var pgn: String {
      wrappedValue.isEmpty ? "" : "[\(name) \"\(wrappedValue)\"]"
    }

  }

  /// Contains the PGN tag pairs for a game.
  public struct Tags: Hashable, Sendable {

    /// Returns all named tags.
    ///
    /// Does not include custom tags included in ``other``.
    public var all: [Game.Tag] {
      Mirror(reflecting: self).children.compactMap {
        $0.value as? Game.Tag
      }
    }

    /// Whether or not all the standard mandatory tags for
    /// PGN archival are set.
    ///
    /// These include `event`, `site`, `date`, `round`,
    /// `white`, `black`, and `result` (known as the "Seven Tag Roster").
    public var isValid: Bool {
      [event, site, date, round, white, black, result]
        .allSatisfy { !$0.isEmpty }
    }

    /// Name of the tournament or match event.
    ///
    /// Example: `"F/S Return Match"`
    @Tag(name: "Event")
    public var event: String

    /// Location of the event.
    ///
    /// Example: `"Belgrade, Serbia JUG"`
    ///
    /// The format for this value is "City, Region COUNTRY",
    /// where "COUNTRY" is the three-letter International Olympic Committee
    /// code for the country.
    ///
    /// Although not part of the specification, some online chess platforms
    /// will include a URL or website as the site value.
    @Tag(name: "Site")
    public var site: String

    /// Starting date of the game, in YYYY.MM.DD format.
    ///
    /// Example: `"1992.11.04"`
    ///
    /// `"??"` is used for unknown values.
    @Tag(name: "Date")
    public var date: String

    /// Playing round ordinal of the game within the event.
    ///
    /// Example `"29"`
    @Tag(name: "Round")
    public var round: String

    /// Player of the white pieces, in "Lastname, Firstname" format.
    ///
    /// Example: `"Fischer, Robert J."`
    @Tag(name: "White")
    public var white: String

    /// Player of the black pieces, in "Lastname, Firstname" format.
    ///
    /// Example: `"Spassky, Boris V."`
    @Tag(name: "Black")
    public var black: String

    /// White player's team. (optional)
    @Tag(name: "WhiteTeam")
    public var whiteTeam: String

    /// Black player's team. (optional)
    @Tag(name: "BlackTeam")
    public var blackTeam: String

    /// White player's title. (optional)
    @Tag(name: "WhiteTitle")
    public var whiteTitle: String

    /// Black player's title. (optional)
    @Tag(name: "BlackTitle")
    public var blackTitle: String

    /// White player's Elo rating. (optional)
    @Tag(name: "WhiteElo")
    public var whiteElo: String

    /// Black player's Elo rating. (optional)
    @Tag(name: "BlackElo")
    public var blackElo: String

    /// White player's FIDE ID. (optional)
    @Tag(name: "WhiteFideId")
    public var whiteFideId: String

    /// Black player's FIDE ID. (optional)
    @Tag(name: "BlackFideId")
    public var blackFideId: String

    /// Result of the game.
    ///
    /// Example: `"1/2-1/2"`
    ///
    /// It is recorded as White score, dash, then Black score, or `*` (other, e.g., the game is ongoing).
    @Tag(name: "Result")
    public var result: String
    
    /// ECO code for the opening. (optional)
    @Tag(name: "ECO")
    public var eco: String

    /// Name of the opening. (optional)
    @Tag(name: "Opening")
    public var opening: String

    /// The person providing notes to the game. (optional)
    @Tag(name: "Annotator")
    public var annotator: String

    /// Comments or notes about the game. (optional)
    @Tag(name: "Comment")
    public var comment: String

    /// String value denoting the total number of half-moves played. (optional)
    @Tag(name: "PlyCount")
    public var plyCount: String

    /// e.g. 40/7200:3600 (moves per seconds: sudden death seconds) (optional)
    @Tag(name: "TimeControl")
    public var timeControl: String

    /// Time the game started, in HH:MM:SS format, in local clock time. (optional)
    @Tag(name: "Time")
    public var time: String

    /// Gives more details about the termination of the game. It may be abandoned, adjudication (result determined by third-party adjudication), death, emergency, normal, rules infraction, time forfeit, or unterminated. (optional)
    @Tag(name: "Termination")
    public var termination: String

    /// The mode of play used for the game. (optional)
    ///
    /// `"OTB"` (over-the-board) or `"ICS"` (Internet Chess Server)
    @Tag(name: "Mode")
    public var mode: String

    /// The initial position of the chessboard, in Forsyth–Edwards Notation. (optional)
    ///
    /// This is used to record partial games (starting at some initial position). It is also necessary for chess variants such as Chess960, where the initial position is not always the same as traditional chess.
    ///
    /// If a FEN tag is used, a separate tag pair SetUp must also appear and have its value set to 1.
    @Tag(name: "FEN")
    public var fen: String

    @Tag(name: "SetUp")
    public var setUp: String

    /// Extra custom tags.
    ///
    /// The key will be used as the tag name in the PGN.
    public var other: [String: String] = [:]

    /// Initializes a `Game.Tags` object with the provided
    /// values.
    ///
    /// For initialization purposes, all values are optional,
    /// and any omitted values will be set to empty strings.
    public init(
      event: String = "",
      site: String = "",
      date: String = "",
      round: String = "",
      white: String = "",
      black: String = "",
      result: String = "",
      whiteTeam: String = "",
      blackTeam: String = "",
      whiteTitle: String = "",
      blackTitle: String = "",
      whiteElo: String = "",
      blackElo: String = "",
      whiteFideId: String = "",
      blackFideId: String = "",
      eco: String = "",
      opening: String = "",
      annotator: String = "",
      comment: String = "",
      plyCount: String = "",
      timeControl: String = "",
      time: String = "",
      termination: String = "",
      mode: String = "",
      fen: String = "",
      setUp: String = "",
      other: [String: String] = [:]
    ) {
      self.event = event
      self.site = site
      self.date = date
      self.round = round
      self.white = white
      self.black = black
      self.result = result
      self.whiteTeam = whiteTeam
      self.blackTeam = blackTeam
      self.whiteTitle = whiteTitle
      self.blackTitle = blackTitle
      self.whiteElo = whiteElo
      self.blackElo = blackElo
      self.whiteFideId = whiteFideId
      self.blackFideId = blackFideId
      self.eco = eco
      self.opening = opening
      self.annotator = annotator
      self.comment = comment
      self.plyCount = plyCount
      self.timeControl = timeControl
      self.time = time
      self.termination = termination
      self.mode = mode
      self.fen = fen
      self.setUp = setUp
      self.other = other
    }
  }

}

// MARK: - Metadata Convenience Methods
extension Game {

  /// A convenience structure that mirrors the PGNMetadata structure
  /// but works directly with the Game's tags
  public struct Metadata {
    public var white: String
    public var black: String
    public var whiteTeam: String
    public var blackTeam: String
    public var whiteTitle: String
    public var blackTitle: String
    public var whiteElo: String
    public var blackElo: String
    public var whiteFideId: String
    public var blackFideId: String
    public var event: String
    public var site: String
    public var date: String
    public var round: String
    public var result: String
    public var eco: String
    public var opening: String
    public var timeControl: String
    public var notes: String
    public var setUp: String
    public var fen: String

    public init(
      white: String = "",
      black: String = "",
      whiteTeam: String = "",
      blackTeam: String = "",
      whiteTitle: String = "",
      blackTitle: String = "",
      whiteElo: String = "",
      blackElo: String = "",
      whiteFideId: String = "",
      blackFideId: String = "",
      event: String = "",
      site: String = "",
      date: String = "",
      round: String = "",
      result: String = "*",
      eco: String = "",
      opening: String = "",
      timeControl: String = "",
      notes: String = "",
      setUp: String = "",
      fen: String = ""
    ) {
      self.white = white
      self.black = black
      self.whiteTeam = whiteTeam
      self.blackTeam = blackTeam
      self.whiteTitle = whiteTitle
      self.blackTitle = blackTitle
      self.whiteElo = whiteElo
      self.blackElo = blackElo
      self.whiteFideId = whiteFideId
      self.blackFideId = blackFideId
      self.event = event
      self.site = site
      self.date = date
      self.round = round
      self.result = result
      self.eco = eco
      self.opening = opening
      self.timeControl = timeControl
      self.notes = notes
      self.setUp = setUp
      self.fen = fen
    }
  }

  /// Gets the current metadata from the game's tags
  public var metadata: Metadata {
    get {
      return Metadata(
        white: tags.white,
        black: tags.black,
        whiteTeam: tags.whiteTeam,
        blackTeam: tags.blackTeam,
        whiteTitle: tags.whiteTitle,
        blackTitle: tags.blackTitle,
        whiteElo: tags.whiteElo,
        blackElo: tags.blackElo,
        whiteFideId: tags.whiteFideId,
        blackFideId: tags.blackFideId,
        event: tags.event,
        site: tags.site,
        date: tags.date,
        round: tags.round,
        result: tags.result,
        eco: tags.eco,
        opening: tags.opening,
        timeControl: tags.timeControl,
        notes: tags.comment,
        setUp: tags.setUp,
        fen: tags.fen
      )
    }
    set {
      tags.white = newValue.white
      tags.black = newValue.black
      tags.whiteTeam = newValue.whiteTeam
      tags.blackTeam = newValue.blackTeam
      tags.whiteTitle = newValue.whiteTitle
      tags.blackTitle = newValue.blackTitle
      tags.whiteElo = newValue.whiteElo
      tags.blackElo = newValue.blackElo
      tags.whiteFideId = newValue.whiteFideId
      tags.blackFideId = newValue.blackFideId
      tags.event = newValue.event
      tags.site = newValue.site
      tags.date = newValue.date
      tags.round = newValue.round
      tags.result = newValue.result
      tags.eco = newValue.eco
      tags.opening = newValue.opening
      tags.timeControl = newValue.timeControl
      tags.comment = newValue.notes
      tags.setUp = newValue.setUp
      tags.fen = newValue.fen
    }
  }

  /// Updates metadata with default values for empty required fields
  public mutating func updateMetadataWithDefaults() {
    if tags.event.isEmpty { tags.event = "?" }
    if tags.site.isEmpty { tags.site = "?" }
    if tags.round.isEmpty { tags.round = "?" }
    if tags.white.isEmpty { tags.white = "?" }
    if tags.black.isEmpty { tags.black = "?" }
    if tags.result.isEmpty { tags.result = "*" }

    if tags.date.isEmpty {
      let formatter = DateFormatter()
      formatter.dateFormat = "yyyy.MM.dd"
      tags.date = formatter.string(from: Date())
    }
  }

  /// Generates a complete PGN string with metadata headers
  public var pgnWithMetadata: String {
    var pgnContent = ""

    // Add required headers (Seven Tag Roster)
    let event = tags.event.isEmpty ? "?" : tags.event
    let site = tags.site.isEmpty ? "?" : tags.site
    let date = tags.date.isEmpty ? {
      let formatter = DateFormatter()
      formatter.dateFormat = "yyyy.MM.dd"
      return formatter.string(from: Date())
    }() : tags.date
    let round = tags.round.isEmpty ? "?" : tags.round
    let white = tags.white.isEmpty ? "?" : tags.white
    let black = tags.black.isEmpty ? "?" : tags.black
    let result = tags.result.isEmpty ? "*" : tags.result

    pgnContent += "[Event \"\(event)\"]\n"
    pgnContent += "[Site \"\(site)\"]\n"
    pgnContent += "[Date \"\(date)\"]\n"
    pgnContent += "[Round \"\(round)\"]\n"
    pgnContent += "[White \"\(white)\"]\n"
    pgnContent += "[Black \"\(black)\"]\n"
    pgnContent += "[Result \"\(result)\"]\n"

    // Add optional standard headers
    if !tags.eco.isEmpty {
      pgnContent += "[ECO \"\(tags.eco)\"]\n"
    }

    if !tags.opening.isEmpty {
      pgnContent += "[Opening \"\(tags.opening)\"]\n"
    }

    if !tags.timeControl.isEmpty {
      pgnContent += "[TimeControl \"\(tags.timeControl)\"]\n"
    }

    if !tags.annotator.isEmpty {
      pgnContent += "[Annotator \"\(tags.annotator)\"]\n"
    }

    if !tags.comment.isEmpty {
      pgnContent += "[Comment \"\(tags.comment)\"]\n"
    }

    if !tags.plyCount.isEmpty {
      pgnContent += "[PlyCount \"\(tags.plyCount)\"]\n"
    }

    if !tags.time.isEmpty {
      pgnContent += "[Time \"\(tags.time)\"]\n"
    }

    if !tags.termination.isEmpty {
      pgnContent += "[Termination \"\(tags.termination)\"]\n"
    }

    if !tags.mode.isEmpty {
      pgnContent += "[Mode \"\(tags.mode)\"]\n"
    }

    if !tags.fen.isEmpty {
      pgnContent += "[FEN \"\(tags.fen)\"]\n"
    }

    if !tags.setUp.isEmpty {
      pgnContent += "[SetUp \"\(tags.setUp)\"]\n"
    }

    // Add custom tags from other dictionary
    for (key, value) in tags.other.sorted(by: { $0.key < $1.key }) {
      if !value.isEmpty {
        pgnContent += "[\(key) \"\(value)\"]\n"
      }
    }

    // Add the game moves
    pgnContent += "\n"
    pgnContent += pgn

    // Ensure the result appears at the end if not already there
    if !pgnContent.hasSuffix(result) {
      pgnContent += " \(result)"
    }

    return pgnContent
  }
}

// MARK: - Undo Support

extension Game {
    
    /// Makes a move with undo support and optional promotion.
    ///
    /// - parameter move: The move to make.
    /// - parameter index: The current move index to make the move from.
    /// - parameter promotionPiece: The piece kind to promote to if this is a pawn promotion move. If `nil`, no promotion is performed.
    ///
    /// - returns: A tuple containing the new move index and the undo state.
    ///
    public mutating func makeWithUndoAndPromotion(
        move: Move,
        from index: MoveTree.MoveIndex,
        promotionPiece: Piece.Kind? = nil
    ) -> (MoveTree.MoveIndex, AddMoveUndoState?) {
        if let existingMoveIndex = moves.hasNextMove(containing: move, for: index) {
            // if attempted move already exists next in the variation,
            // skip making it and return the corresponding index
            return (existingMoveIndex, nil)
        }

        guard let currentPosition = positions[index] else {
            return (index, nil)
        }

        // Use moves.addWithUndo to get proper path backup in the undo state
        let (newIndex, undoState) = moves.addWithUndo(move: move, toParentIndex: index)

        // Update the undo state with the correct position
        let updatedUndoState = AddMoveUndoState(
            parentIndex: undoState.parentIndex,
            move: undoState.move,
            position: currentPosition,  // Use the actual current position instead of standard
            parentPath: undoState.parentPath
        )

        // Create the actual position for the new move
        var newPosition = currentPosition
        let metaMove = move.metaMove!

        switch metaMove.result {
        case .move:
            newPosition.moveWithPromotion(pieceAt: metaMove.start, to: metaMove.end, promotionPiece: promotionPiece)
            if metaMove.piece.kind == .pawn { newPosition.resetHalfmoveClock() }
        case let .capture(capturedPiece):
            newPosition.remove(capturedPiece)
            newPosition.moveWithPromotion(pieceAt: metaMove.start, to: metaMove.end, promotionPiece: promotionPiece)
            newPosition.resetHalfmoveClock()
        case let .castle(castling):
            newPosition.castle(castling)
        }

//        if let promotedPiece = metaMove.promotedPiece {
//            newPosition.promote(pieceAt: metaMove.end, to: promotedPiece.kind)
//        }

        positions[newIndex] = newPosition

        return (newIndex, updatedUndoState)
    }

    /// Makes a move with undo support.
    ///
    /// - parameter move: The move to make.
    /// - parameter index: The current move index to make the move from.
    ///
    /// - returns: A tuple containing the new move index and the undo state.
    ///
    public mutating func makeWithUndo(
        move: Move,
        from index: MoveTree.MoveIndex
    ) -> (MoveTree.MoveIndex, AddMoveUndoState?) {
        return makeWithUndoAndPromotion(move: move, from: index, promotionPiece: nil)
    }
    
    /// Undoes a make move operation.
    ///
    /// - parameter undoState: The undo state from the original make operation.
    ///
    /// - returns: `true` if the undo was successful, `false` otherwise.
    ///
    @discardableResult
    public mutating func undoMake(_ undoState: AddMoveUndoState) -> Bool {
        // Use the undo state's method to find the move to remove
        if let moveIndexToRemove = undoState.newMoveIndexToRemove(in: moves) {
            positions.removeValue(forKey: moveIndexToRemove)
        }
        
        // Undo the move tree operation
        return moves.undoAdd(undoState)
    }
    
    /// Deletes a move with undo support.
    ///
    /// - parameter index: The index of the move to delete.
    ///
    /// - returns: A tuple containing success status and the undo state.
    ///
    public mutating func deleteWithUndo(at index: MoveTree.MoveIndex) -> (Bool, DeleteMoveUndoState?) {
        // Collect all indices that will be deleted
        var indicesToDelete: [MoveTree.MoveIndex] = []
        collectIndicesToDelete(from: index, into: &indicesToDelete)
        
        // Save positions for all deleted indices
        var deletedPositions: [MoveTree.MoveIndex: Position] = [:]
        for indexToDelete in indicesToDelete {
            if let position = positions[indexToDelete] {
                deletedPositions[indexToDelete] = position
            }
        }
        
        // Perform the move tree deletion with undo
        let (success, undoState) = moves.deleteWithUndo(at: index)
        
        if success, var undoState = undoState {
            // Update undo state with saved positions
            undoState = DeleteMoveUndoState(
                deletedNodes: undoState.deletedNodes,
                deletedPositions: deletedPositions,
                parentIndex: undoState.parentIndex,
                indexInParent: undoState.indexInParent
            )
            
            // Remove positions for all deleted indices
            for indexToDelete in indicesToDelete {
                positions.removeValue(forKey: indexToDelete)
            }
            
            return (true, undoState)
        }
        
        return (false, nil)
    }
    
    /// Undoes a delete move operation.
    ///
    /// - parameter undoState: The undo state from the original delete operation.
    ///
    /// - returns: `true` if the undo was successful, `false` otherwise.
    ///
    @discardableResult
    public mutating func undoDelete(_ undoState: DeleteMoveUndoState) -> Bool {
        // Undo the move tree operation
        let success = moves.undoDelete(undoState)
        if !success {
            return false
        }
        
        // Restore positions
        for (index, position) in undoState.deletedPositions {
            positions[index] = position
        }
        
        return true
    }
    
    /// Deletes all moves before the specified move with undo support.
    ///
    /// - parameter index: The index of the move that should become the new starting point.
    ///
    /// - returns: A tuple containing success status and the undo state.
    ///
    public mutating func deleteBeforeMoveWithUndo(at index: MoveTree.MoveIndex) -> (Bool, DeleteBeforeMoveUndoState?) {
        // Get the position before the target move (this will be our new starting position)
        guard positions[index] != nil else {
            return (false, nil) // Move not found
        }
        
        // Get the position that was BEFORE the target move was made
        guard let targetNode = moves.dictionary[index],
              let previousNode = targetNode.previous,
              let newStartingPosition = positions[previousNode.index] else {
            return (false, nil) // Could not determine previous position
        }
        
        // Save all positions that will be deleted
        let (deletedIndices, undoState) = moves.deleteBeforeMoveWithUndo(at: index)
        
        if !deletedIndices.isEmpty, var undoState = undoState {
            // Save positions for all deleted moves
            var deletedPositions: [MoveTree.MoveIndex: Position] = [:]
            for deletedIndex in deletedIndices {
                if let position = positions[deletedIndex] {
                    deletedPositions[deletedIndex] = position
                }
            }
            
            // Update undo state with positions and starting position
            undoState = DeleteBeforeMoveUndoState(
                deletedNodes: undoState.deletedNodes,
                deletedPositions: deletedPositions,
                originalHeadNode: undoState.originalHeadNode,
                originalStartingPosition: positions[MoveTree.minimumIndex] ?? Position.standard,
                targetIndex: index
            )
            
            // Remove positions for all deleted moves
            for deletedIndex in deletedIndices {
                positions.removeValue(forKey: deletedIndex)
            }
            
            // Update the starting position to be the position before our target move
            positions[MoveTree.minimumIndex] = newStartingPosition
            
            // Update tags to reflect the new starting position
            tags.fen = newStartingPosition.fen
            tags.setUp = "1" // Indicate this is a custom starting position
            
            return (true, undoState)
        }
        
        return (false, nil)
    }
    
    /// Undoes a delete before move operation.
    ///
    /// - parameter undoState: The undo state from the original delete before move operation.
    ///
    /// - returns: `true` if the undo was successful, `false` otherwise.
    ///
    @discardableResult
    public mutating func undoDeleteBeforeMove(_ undoState: DeleteBeforeMoveUndoState) -> Bool {
        // Undo the move tree operation
        let success = moves.undoDeleteBeforeMove(undoState)
        if !success {
            return false
        }
        
        // Restore original starting position
        positions[MoveTree.minimumIndex] = undoState.originalStartingPosition
        
        // Restore all deleted positions
        for (index, position) in undoState.deletedPositions {
            positions[index] = position
        }
        
        // Restore original tags
        if undoState.originalStartingPosition == Position.standard {
            tags.fen = Position.standard.fen
            tags.setUp = "0"
        } else {
            tags.fen = undoState.originalStartingPosition.fen
            tags.setUp = "1"
        }
        
        return success
    }
    
    /// Promotes a variation with undo support.
    ///
    /// - parameter index: The index of any move within the variation to promote.
    ///
    /// - returns: A tuple containing success status and the undo state.
    ///
    public mutating func promoteWithUndo(index: MoveTree.MoveIndex) -> (Bool, PromoteVariationUndoState?) {
        return moves.promoteWithUndo(index: index)
    }
    
    /// Undoes a promote variation operation.
    ///
    /// - parameter undoState: The undo state from the original promote operation.
    ///
    /// - returns: `true` if the undo was successful, `false` otherwise.
    ///
    @discardableResult
    public mutating func undoPromote(_ undoState: PromoteVariationUndoState) -> Bool {
        return moves.undoPromote(undoState)
    }
    
    /// Promotes a variation to become the main variation with undo support.
    ///
    /// - parameter index: The index of any move within the variation to promote to main.
    ///
    /// - returns: A tuple containing success status and the undo state.
    ///
    public mutating func promoteToMainVariationWithUndo(index: MoveTree.MoveIndex) -> (Bool, PromoteToMainUndoState?) {
        return moves.promoteToMainVariationWithUndo(index: index)
    }
    
    /// Undoes a promote to main variation operation.
    ///
    /// - parameter undoState: The undo state from the original promote to main operation.
    ///
    /// - returns: `true` if the undo was successful, `false` otherwise.
    ///
    @discardableResult
    public mutating func undoPromoteToMain(_ undoState: PromoteToMainUndoState) -> Bool {
        return moves.undoPromoteToMain(undoState)
    }
    
    /// Overwrites moves after the specified index with undo support and optional promotion.
    ///
    /// - parameter move: The new move to add.
    /// - parameter index: The index to overwrite from.
    /// - parameter promotionPiece: The piece kind to promote to if this is a pawn promotion move. If `nil`, no promotion is performed.
    ///
    /// - returns: A tuple containing the new move index and the undo state.
    ///
    public mutating func overwriteWithUndoAndPromotion(move: Move, from index: MoveTree.MoveIndex, promotionPiece: Piece.Kind? = nil) -> (MoveTree.MoveIndex, OverwriteUndoState?) {
        guard let node = moves.dictionary[index] else {
            return (index, nil)
        }
        
        // Collect all indices that will be deleted
        var indicesToDelete: [MoveTree.MoveIndex] = []
        
        // Collect next move and all its descendants
        if let next = node.next {
            collectIndicesToDelete(from: next.index, into: &indicesToDelete)
        }
        
        // Collect all children and their descendants
        for child in node.children {
            collectIndicesToDelete(from: child.index, into: &indicesToDelete)
        }
        
        // If there are no nodes to delete, this is just a make operation
        guard !indicesToDelete.isEmpty else {
            let (newIndex, _) = makeWithUndo(move: move, from: index)
            return (newIndex, nil) // No overwrite undo state needed
        }
        
        // Save positions for all deleted indices
        var deletedPositions: [MoveTree.MoveIndex: Position] = [:]
        for indexToDelete in indicesToDelete {
            if let position = positions[indexToDelete] {
                deletedPositions[indexToDelete] = position
            }
        }
        
        // Perform the move tree overwrite with undo
        let (newIndex, undoState) = moves.overwriteWithUndo(move: move, toParentIndex: index)
        
        if var undoState = undoState {
            // Update undo state with saved positions
            undoState = OverwriteUndoState(
                deletedNodes: undoState.deletedNodes,
                deletedPositions: deletedPositions,
                parentIndex: undoState.parentIndex,
                parentPath: undoState.parentPath
            )
            
            // Remove positions for all deleted indices
            for indexToDelete in indicesToDelete {
                positions.removeValue(forKey: indexToDelete)
            }
            
            // Create position for the new move
            if let currentPosition = positions[index] {
                var newPosition = currentPosition
                let metaMove = move.metaMove!
                
                switch metaMove.result {
                case .move:
                    newPosition.moveWithPromotion(pieceAt: metaMove.start, to: metaMove.end, promotionPiece: promotionPiece)
                    if metaMove.piece.kind == .pawn { newPosition.resetHalfmoveClock() }
                case let .capture(capturedPiece):
                    newPosition.remove(capturedPiece)
                    newPosition.moveWithPromotion(pieceAt: metaMove.start, to: metaMove.end, promotionPiece: promotionPiece)
                    newPosition.resetHalfmoveClock()
                case let .castle(castling):
                    newPosition.castle(castling)
                }
                
                if let promotedPiece = metaMove.promotedPiece {
                    newPosition.promote(pieceAt: metaMove.end, to: promotedPiece.kind)
                }
                
                positions[newIndex] = newPosition
            }
            
            return (newIndex, undoState)
        }
        
        return (newIndex, nil)
    }

    /// Overwrites moves after the specified index with undo support.
    ///
    /// - parameter move: The new move to add.
    /// - parameter index: The index to overwrite from.
    ///
    /// - returns: A tuple containing the new move index and the undo state.
    ///
    public mutating func overwriteWithUndo(move: Move, from index: MoveTree.MoveIndex) -> (MoveTree.MoveIndex, OverwriteUndoState?) {
        return overwriteWithUndoAndPromotion(move: move, from: index, promotionPiece: nil)
    }

    /// Undoes an overwrite operation.
    ///
    /// - parameter undoState: The undo state from the original overwrite operation.
    ///
    /// - returns: `true` if the undo was successful, `false` otherwise.
    ///
    @discardableResult
    public mutating func undoOverwrite(_ undoState: OverwriteUndoState) -> Bool {
        // Use the undo state's method to find and remove the new move's position
        if let moveIndexToRemove = undoState.newMoveIndexToRemove(in: moves) {
            positions.removeValue(forKey: moveIndexToRemove)
        }
        
        // Restore all deleted positions
        for (index, position) in undoState.deletedPositions {
            positions[index] = position
        }
        
        // Undo the move tree operation
        return moves.undoOverwrite(undoState)
    }
    
    /// Edits a move with undo support.
    ///
    /// - parameter index: The index of the move to edit.
    /// - parameter newMove: The new move to set.
    ///
    /// - returns: The undo state if successful, `nil` otherwise.
    ///
    public mutating func editMoveWithUndo(index: MoveTree.MoveIndex, newMove: Move) -> EditMoveUndoState? {
        return moves.setNodeMoveWithUndo(index: index, newMove: newMove)
    }
    
    /// Undoes an edit move operation.
    ///
    /// - parameter undoState: The undo state from the original edit operation.
    ///
    /// - returns: `true` if the undo was successful, `false` otherwise.
    ///
    @discardableResult
    public mutating func undoEditMove(_ undoState: EditMoveUndoState) -> Bool {
        return moves.undoEditMove(undoState)
    }
}
