//
//  MoveTree.swift
//  ChessKit
//

import Foundation

/// A tree-like data structure that represents the moves of a chess game.
///
/// The tree maintains the move order including variations and
/// provides index-based access for any element in the tree.
public struct MoveTree: Hashable, Sendable {
  public typealias MoveIndex = Int
  
  /// The index of the root of the move tree.
  ///
  /// Defaults to -1. Head node's index.
  public static let minimumIndex: MoveIndex = -1
  var headNode: Node
  
  /// Dictionary representation of the tree for faster access.
  private(set) var dictionary: [MoveIndex: Node] = [:]
  
  /// Hash value counter for generating unique move hashes.
  /// Starts from 0 and increments for each new move created.
  private var hashVal: MoveIndex = 0
  fileprivate mutating func updateHashValue() {
    hashVal += 1
  }
  
  init(_ headNodeColor: Piece.Color = .black, _ headNodeNumber: Int = 0) {
    let node = Node(index: MoveTree.minimumIndex, move: Move(), color: headNodeColor, number: headNodeNumber)
    self.headNode = node
    self.dictionary = [MoveTree.minimumIndex: node]
  }
  
  public var firstIndex: MoveIndex? {
    headNode.next?.index
  }
  
  public var initialSideToMove: Piece.Color {
    return headNode.color.opposite
  }
  
  public var initialNumber: Int {
    return headNode.number + 1
  }
  
  /// The last index of the main variation of the move tree.
  /// This is a computed property that traverses the main line from the root.
  var lastMainVariationIndex: MoveIndex {
    var currentNode: Node = headNode
    
    while currentNode.next != nil {
      currentNode = currentNode.next!
    }
    return currentNode.index
  }
  
  /// A set containing the indices of all the moves stored in the tree.
  public var indices: [MoveIndex] {
    Array(dictionary.keys)
  }
  
  /// Whether the tree is empty or not.
  public var isEmpty: Bool {
    headNode.next == nil
  }
  
  public var hasVariation: Bool {
    var root = headNode
    var hasVariation = false
    while true {
      if !root.children.isEmpty {
        hasVariation = true
        break
      }
      if root.next == nil {
        break
      }
      root = root.next!
    }
    return hasVariation
  }
  
  public var allMainVariationIndices: Set<MoveIndex> {
    var mainVariationIndices: Set<MoveIndex> = []
    var root = headNode
    while let pointer = root.next {
      mainVariationIndices.insert(pointer.index)
      root = pointer
    }
    return mainVariationIndices
  }
  
  public func getVariationRootNodeIndex(index: MoveIndex) -> MoveIndex? {
    guard let rootNode = getVariationRootNode(for: index) else {
      return nil
    }
    return rootNode == headNode ? headNode.next?.index : rootNode.index
  }
  
  public func getNodeMove(index: MoveIndex) -> Move? {
    return dictionary[index]?.move
  }
  
  public func setNodeMove(index: MoveIndex, newMove: Move) {
    dictionary[index]?.move = newMove
  }

  /// Sets a node's move with undo support.
  ///
  /// - parameter index: The index of the move to edit.
  /// - parameter newMove: The new move to set.
  ///
  /// - returns: The undo state if successful, `nil` otherwise.
  ///
  public mutating func setNodeMoveWithUndo(index: MoveIndex, newMove: Move) -> EditMoveUndoState? {
    guard let node = dictionary[index] else {
      return nil
    }

    let originalMove = node.move

    // Generate backup path for reliable undo
    let movePath = NodePath.pathTo(moveIndex: index, in: self)

    // Perform the edit
    setNodeMove(index: index, newMove: newMove)

    return EditMoveUndoState(
      moveIndex: index,
      originalMove: originalMove,
      movePath: movePath
    )
  }

  /// Undoes an edit move operation.
  ///
  /// - parameter undoState: The undo state from the original edit operation.
  ///
  /// - returns: `true` if the undo was successful, `false` otherwise.
  ///
  @discardableResult
  public mutating func undoEditMove(_ undoState: EditMoveUndoState) -> Bool {
    // Try to resolve index using robust resolution
    let moveIndex: MoveIndex
    if dictionary[undoState.moveIndex] != nil {
      moveIndex = undoState.moveIndex
    } else if let path = undoState.movePath,
              let resolvedIndex = path.findIndex(in: self) {
      moveIndex = resolvedIndex
    } else {
      return false // Cannot find move to undo
    }

    setNodeMove(index: moveIndex, newMove: undoState.originalMove)
    return true
  }
  
  public func getNodeColor(index: MoveIndex) -> Piece.Color? {
    return dictionary[index]?.color
  }
  
  public func getNodeNumber(index: MoveIndex) -> Int? {
    return dictionary[index]?.number
  }
  
  public func nextIndex(currentIndex: MoveIndex) -> MoveIndex? {
    if currentIndex == MoveTree.minimumIndex {
      return headNode.next?.index
    }
    return dictionary[currentIndex]?.next?.index
  }
  
  public func previousIndex(currentIndex: MoveIndex) -> MoveIndex? {
    return dictionary[currentIndex]?.previous?.index
  }
  
  // moves on main variation
  public var count: Int {
    var pointer = headNode.next
    var counter = 0
    while pointer != nil {
      counter += 1
      pointer = pointer!.next
    }
    return counter
  }
  
  // all moves (expect for non-exisiting headNode)
  public var allCount: Int {
    dictionary.count - 1
  }

  /// Lock to restrict modification of tree nodes
  /// to ensure `Sendable` conformance for ``Node``.
  private static let nodeLock = NSLock()

  /// Adds a move to the move tree.
  ///
  /// - parameter move: The move to add to the tree.
  /// - parameter moveIndex: The `MoveIndex` of the parent move, if applicable.
  /// If `moveIndex` is `nil`, the move tree is cleared and the provided
  /// move is set to the `head` of the move tree.
  ///
  /// - returns: The move index resulting from the addition of the move.
  ///
  @discardableResult
  public mutating func add(
    move: Move,
    toParentIndex index: MoveIndex = MoveTree.minimumIndex
  ) -> MoveIndex {
    let parent = dictionary[index]!
    let (newColor, newNumber) = parent.nextColorAndNumber(parent == headNode)
    let newNode = Node(index: hashVal, move: move, color: newColor, number: newNumber)
    updateHashValue()

    newNode.previous = parent
    if parent.next == nil {
      parent.next = newNode
    } else {
      parent.children.append(newNode)
    }

    Self.nodeLock.withLock {
      dictionary[newNode.index] = newNode
    }

    return newNode.index
  }

  /// Adds a move to the move tree with undo support.
  ///
  /// - parameter move: The move to add to the tree.
  /// - parameter moveIndex: The `MoveIndex` of the parent move, if applicable.
  ///
  /// - returns: A tuple containing the new move index and the undo state.
  ///
  public mutating func addWithUndo(
    move: Move,
    toParentIndex index: MoveIndex = MoveTree.minimumIndex
  ) -> (MoveIndex, AddMoveUndoState) {
    let parentPosition = Position.standard // This will be provided by Game layer

    let newIndex = add(move: move, toParentIndex: index)

    // Generate backup path for reliable undo
    guard let parentPath = NodePath.pathTo(moveIndex: index, in: self) else {
      fatalError("Failed to generate parent path for index \(index)")
    }

    let undoState = AddMoveUndoState(
      parentIndex: index,
      move: move,
      position: parentPosition,
      parentPath: parentPath
    )

    return (newIndex, undoState)
  }

  /// Undoes an add move operation.
  ///
  /// - parameter undoState: The undo state from the original add operation.
  ///
  /// - returns: `true` if the undo was successful, `false` otherwise.
  ///
  @discardableResult
  public mutating func undoAdd(_ undoState: AddMoveUndoState) -> Bool {
    // Use the undo state's method to resolve parent index
    guard let parentIndex = undoState.resolvedParentIndex(in: self),
          let parent = dictionary[parentIndex] else {
      return false
    }

    // Use the undo state's method to find the move to remove
    guard let moveIndexToRemove = undoState.newMoveIndexToRemove(in: self) else {
      return false
    }

    // Remove the move from parent's connections
    if !parent.children.isEmpty && parent.children.last?.index == moveIndexToRemove {
      parent.children.removeLast()
    } else if let next = parent.next, next.index == moveIndexToRemove {
      parent.next = nil
    }

    // Remove from dictionary
    dictionary.removeValue(forKey: moveIndexToRemove)

    return true
  }

  /// Deletes a move and all subsequent moves from the tree.
  ///
  /// This method recursively removes the specified node and all its descendants,
  /// including both the main continuation (`next`) and all variations (`siblings`).
  ///
  /// - parameter index: The index of the move to delete.
  /// - returns: `true` if the move was successfully deleted, `false` if the move was not found.
  ///
  /// **Important**: This operation cannot be undone. All moves following the deleted
  /// move will be permanently removed from the tree.
  ///
  /// **Examples**:
  /// - Deleting a move in the middle of the main line removes that move and all subsequent moves
  /// - Deleting a variation move removes the entire variation branch
  /// - Deleting the root move clears the entire tree
  @discardableResult
  public mutating func delete(at index: MoveIndex) -> Bool {
    assert(index != MoveTree.minimumIndex)
    guard let nodeToDelete = dictionary[index] else {
      return false // Node not found
    }

    // Recursively collect all nodes to delete (current node + all descendants)
    var nodesToDelete: [Node] = []
    collectNodesToDelete(node: nodeToDelete, into: &nodesToDelete)

    // Remove all collected nodes from the dictionary
    Self.nodeLock.withLock {
      for node in nodesToDelete {
        dictionary.removeValue(forKey: node.index)
      }
    }

    // Update parent references
    let parent = nodeToDelete.previous!
      // If this is the next node of its parent
    if parent.next == nodeToDelete {
      // Promote the first sibling to be the next node, if any
      if let firstSibling = parent.children.first {
        parent.next = firstSibling
        parent.children.removeFirst()
      } else {
        parent.next = nil
      }
    } else {
      // This is a sibling node, remove it from siblings array
      parent.children.removeAll { $0 === nodeToDelete }
    }
    
    return true
  }

  /// Deletes a move and all subsequent moves from the tree with undo support.
  ///
  /// - parameter index: The index of the move to delete.
  ///
  /// - returns: A tuple containing success status and the undo state.
  ///
  public mutating func deleteWithUndo(at index: MoveIndex) -> (Bool, DeleteMoveUndoState?) {
    assert(index != MoveTree.minimumIndex)
    guard let nodeToDelete = dictionary[index] else {
      return (false, nil) // Node not found
    }

    // Save the parent information for undo
    let parent = nodeToDelete.previous!
    let indexInParent: Int
    if parent.next == nodeToDelete {
      indexInParent = -1 // This is the next node
    } else {
      // Find position in children array
      indexInParent = parent.children.firstIndex { $0 === nodeToDelete } ?? 0
    }

    // Collect all nodes that will be deleted
    var nodesToDelete: [Node] = []
    collectNodesToDelete(node: nodeToDelete, into: &nodesToDelete)

    // Create saved nodes for undo with path backup for cross-boundary references
    let savedNodes = nodesToDelete.map { node in
      SavedNode(from: node, moveTree: self)
    }

    // Perform the actual deletion
    let success = delete(at: index)

    if success {
      let undoState = DeleteMoveUndoState(
        deletedNodes: savedNodes,
        deletedPositions: [:], // Will be filled by Game layer
        parentIndex: parent.index,
        indexInParent: indexInParent
      )
      return (true, undoState)
    }

    return (false, nil)
  }

  /// Undoes a delete move operation.
  ///
  /// - parameter undoState: The undo state from the original delete operation.
  ///
  /// - returns: `true` if the undo was successful, `false` otherwise.
  ///
  @discardableResult
  public mutating func undoDelete(_ undoState: DeleteMoveUndoState) -> Bool {
    guard let parent = dictionary[undoState.parentIndex] else {
      return false
    }

    // Recreate all deleted nodes
    var recreatedNodes: [MoveIndex: Node] = [:]

    // First pass: create all nodes
    for savedNode in undoState.deletedNodes {
      let node = Node(
        index: savedNode.index,
        move: savedNode.move,
        color: savedNode.color,
        number: savedNode.number
      )
      recreatedNodes[savedNode.index] = node
      dictionary[savedNode.index] = node
    }

    // Second pass: restore relationships using robust resolution
    for savedNode in undoState.deletedNodes {
      guard let node = recreatedNodes[savedNode.index] else { continue }

      // Restore previous reference using robust resolution
      if let prevIndex = savedNode.resolvePreviousIndex(in: self) {
        node.previous = dictionary[prevIndex]
      }

      // Restore next reference using robust resolution
      if let nextIndex = savedNode.resolveNextIndex(in: self) {
        node.next = dictionary[nextIndex]
      }

      // Restore children references using robust resolution
      let childrenIndices = savedNode.resolveChildrenIndices(in: self)
      node.children = childrenIndices.compactMap { dictionary[$0] }
    }

    // Restore parent connection
    if let rootNode = recreatedNodes[undoState.deletedNodes.first?.index ?? -1] {
      if undoState.indexInParent == -1 {
        parent.next = rootNode
      } else {
        parent.children.insert(rootNode, at: undoState.indexInParent)
      }
    }

    return true
  }

  /// Deletes all moves after (but not including) the specified move index.
  /// This includes both the next move and all children (variations).
  ///
  /// - parameter index: The index of the move from which to delete all subsequent moves
  /// - returns: `true` if any moves were deleted, `false` if no moves were found to delete
  @discardableResult
  public mutating func deleteAllAfter(index: MoveIndex) -> Bool {
    guard let node = dictionary[index] else { return false }
    
    var nodesToDelete: [Node] = []
    var hasNodesToDelete = false
    
    // Collect next move and all its descendants
    if let next = node.next {
      collectNodesToDelete(node: next, into: &nodesToDelete)
      hasNodesToDelete = true
    }
    
    // Collect all children and their descendants
    for child in node.children {
      collectNodesToDelete(node: child, into: &nodesToDelete)
      hasNodesToDelete = true
    }
    
    // If there are no nodes to delete, return false
    guard hasNodesToDelete else { return false }
    
    // Remove all collected nodes from the dictionary
    Self.nodeLock.withLock {
      for nodeToDelete in nodesToDelete {
        dictionary.removeValue(forKey: nodeToDelete.index)
      }
    }
    
    // Clear the next and children references from the specified node
    node.next = nil
    node.children.removeAll()
    
    return true
  }
  
  public mutating func overwrite(move: Move, toParentIndex: MoveIndex) -> MoveIndex {
    let success = deleteAllAfter(index: toParentIndex)
    if !success {
      return toParentIndex
    }
    return add(move: move, toParentIndex: toParentIndex)
  }

  /// Overwrites moves after the specified index with undo support.
  ///
  /// - parameter move: The new move to add.
  /// - parameter toParentIndex: The parent index to overwrite from.
  ///
  /// - returns: A tuple containing the new move index and the undo state.
  ///
  public mutating func overwriteWithUndo(move: Move, toParentIndex: MoveIndex) -> (MoveIndex, OverwriteUndoState?) {
    guard let parentNode = dictionary[toParentIndex] else {
      return (toParentIndex, nil)
    }

    // Collect all nodes that will be deleted
    var nodesToDelete: [Node] = []
    var hasNodesToDelete = false

    // Collect next move and all its descendants
    if let next = parentNode.next {
      collectNodesToDelete(node: next, into: &nodesToDelete)
      hasNodesToDelete = true
    }

    // Collect all children and their descendants
    for child in parentNode.children {
      collectNodesToDelete(node: child, into: &nodesToDelete)
      hasNodesToDelete = true
    }

    // If there are no nodes to delete, this is just an add operation
    guard hasNodesToDelete else {
      let newIndex = add(move: move, toParentIndex: toParentIndex)
      return (newIndex, nil)
    }

    // Create saved nodes for undo with path backup for cross-boundary references
    let savedNodes = nodesToDelete.map { node in
      SavedNode(from: node, moveTree: self)
    }

    // Perform the actual overwrite
    let newIndex = overwrite(move: move, toParentIndex: toParentIndex)
    
    // Generate backup path for reliable undo
    guard let parentPath = NodePath.pathTo(moveIndex: toParentIndex, in: self) else {
      fatalError("Failed to generate parent path for index \(toParentIndex)")
    }

    let undoState = OverwriteUndoState(
      deletedNodes: savedNodes,
      deletedPositions: [:], // Will be filled by Game layer
      parentIndex: toParentIndex,
      parentPath: parentPath
    )

    return (newIndex, undoState)
  }

  /// Undoes an overwrite operation.
  ///
  /// - parameter undoState: The undo state from the original overwrite operation.
  ///
  /// - returns: `true` if the undo was successful, `false` otherwise.
  ///
  @discardableResult
  public mutating func undoOverwrite(_ undoState: OverwriteUndoState) -> Bool {
    // Use the undo state's method to resolve parent index
    guard let parentIndex = undoState.resolvedParentIndex(in: self),
          let parent = dictionary[parentIndex] else {
      return false
    }

    // Remove the new move that was added using the undo state's method
    if let moveIndexToRemove = undoState.newMoveIndexToRemove(in: self) {
      dictionary.removeValue(forKey: moveIndexToRemove)
      parent.next = nil
    }

    // Recreate all deleted nodes
    var recreatedNodes: [MoveIndex: Node] = [:]

    // First pass: create all nodes
    for savedNode in undoState.deletedNodes {
      let node = Node(
        index: savedNode.index,
        move: savedNode.move,
        color: savedNode.color,
        number: savedNode.number
      )
      recreatedNodes[savedNode.index] = node
      dictionary[savedNode.index] = node
    }

    // Second pass: restore relationships using robust resolution
    for savedNode in undoState.deletedNodes {
      guard let node = recreatedNodes[savedNode.index] else { continue }

      // Restore previous reference using robust resolution
      if let prevIndex = savedNode.resolvePreviousIndex(in: self) {
        node.previous = dictionary[prevIndex]
      }

      // Restore next reference using robust resolution
      if let nextIndex = savedNode.resolveNextIndex(in: self) {
        node.next = dictionary[nextIndex]
      }

      // Restore children references using robust resolution
      let childrenIndices = savedNode.resolveChildrenIndices(in: self)
      node.children = childrenIndices.compactMap { dictionary[$0] }
    }

    // Restore parent connections
    // Find the original next node and children
    let originalNext = undoState.deletedNodes.first { $0.previousIndex == undoState.parentIndex }
    if let originalNextIndex = originalNext?.index {
      parent.next = dictionary[originalNextIndex]
    }

    // Restore children that were directly connected to parent
    parent.children = undoState.deletedNodes
      .filter { $0.previousIndex == undoState.parentIndex && $0.index != originalNext?.index }
      .compactMap { dictionary[$0.index] }

    return true
  }

  /// Deletes all moves before the specified move, making that move the new starting position.
  ///
  /// This method removes all moves that occurred before the specified move index,
  /// effectively making the position at the specified move the new starting position.
  /// The head node is updated with the position information and move tree is restructured.
  ///
  /// - parameter index: The index of the move that should become the new starting point.
  /// - returns: `true` if the operation was successful, `false` if the move was not found.
  ///
  /// **Use Cases**:
  /// - Creating a new game from a middle position
  /// - Removing opening moves to focus on specific positions
  /// - Converting analysis positions to starting positions
  ///
  /// **Example**: In the game `1. e4 e5 2. Nf3 Nc6 3. Bb5`, calling `deleteBeforeMove`
  /// with the index of `3. Bb5` will result in a new game starting from that position.
  @discardableResult
public mutating func deleteBeforeMove(at index: MoveIndex) -> [MoveIndex] {
    assert(index != MoveTree.minimumIndex)
    guard let targetNode = dictionary[index] else {
      return [] // Node not found
    }
    
    // Update the head node to reflect the position before the target move
    guard let previousNode = targetNode.previous else {
      return []
    }
    
    // Collect all nodes that will be deleted (everything before the target node)
    var nodesToDelete: [Node] = []
    var deletedIndices: [MoveIndex] = []
    
    // Start from the head node and collect all moves that come before the target
    collectMovesBeforeTarget(from: headNode, targetIndex: index, into: &nodesToDelete)
  
    headNode.move.positionComment = previousNode.move.positionComment
    headNode.color = previousNode.color
    headNode.number = previousNode.number
    
    // The target node becomes the first move in the new tree
    headNode.next = targetNode
    targetNode.previous = headNode
    
    // Extract indices of deleted nodes
    deletedIndices = nodesToDelete.map { $0.index }
    
    // Remove all deleted nodes from dictionary
    Self.nodeLock.withLock {
      for node in nodesToDelete {
        dictionary.removeValue(forKey: node.index)
      }
    }
    
    return deletedIndices
  }

  /// Deletes all moves before the specified move with undo support.
  ///
  /// - parameter index: The index of the move that should become the new starting point.
  ///
  /// - returns: A tuple containing the deleted indices and the undo state.
  ///
  public mutating func deleteBeforeMoveWithUndo(at index: MoveIndex) -> ([MoveIndex], DeleteBeforeMoveUndoState?) {
    assert(index != MoveTree.minimumIndex)
    guard let targetNode = dictionary[index] else {
      return ([], nil) // Node not found
    }

    guard targetNode.previous != nil else {
      return ([], nil)
    }

    // Save original head node state with path backup
    let originalHeadNode = SavedNode(from: headNode, moveTree: self)

    // Collect all nodes that will be deleted
    var nodesToDelete: [Node] = []
    collectMovesBeforeTarget(from: headNode, targetIndex: index, into: &nodesToDelete)

    // Create saved nodes for undo with path backup for cross-boundary references
    let savedNodes = nodesToDelete.map { node in
      SavedNode(from: node, moveTree: self)
    }

    // Perform the actual deletion
    let deletedIndices = deleteBeforeMove(at: index)

    if !deletedIndices.isEmpty {
      let undoState = DeleteBeforeMoveUndoState(
        deletedNodes: savedNodes,
        deletedPositions: [:], // Will be filled by Game layer
        originalHeadNode: originalHeadNode,
        originalStartingPosition: Position.standard, // Will be filled by Game layer
        targetIndex: index
      )
      return (deletedIndices, undoState)
    }

    return ([], nil)
  }

  /// Undoes a delete before move operation.
  ///
  /// - parameter undoState: The undo state from the original delete before move operation.
  ///
  /// - returns: `true` if the undo was successful, `false` otherwise.
  ///
  @discardableResult
  public mutating func undoDeleteBeforeMove(_ undoState: DeleteBeforeMoveUndoState) -> Bool {
    // Restore original head node state
    headNode.move = undoState.originalHeadNode.move
    headNode.color = undoState.originalHeadNode.color
    headNode.number = undoState.originalHeadNode.number

    // Recreate all deleted nodes
    var recreatedNodes: [MoveIndex: Node] = [:]

    // First pass: create all nodes
    for savedNode in undoState.deletedNodes {
      let node = Node(
        index: savedNode.index,
        move: savedNode.move,
        color: savedNode.color,
        number: savedNode.number
      )
      recreatedNodes[savedNode.index] = node
      dictionary[savedNode.index] = node
    }

    // Second pass: restore relationships using robust resolution
    for savedNode in undoState.deletedNodes {
      guard let node = recreatedNodes[savedNode.index] else { continue }

      // Restore previous reference using robust resolution
      if let prevIndex = savedNode.resolvePreviousIndex(in: self) {
        node.previous = dictionary[prevIndex]
      }

      // Restore next reference using robust resolution
      if let nextIndex = savedNode.resolveNextIndex(in: self) {
        node.next = dictionary[nextIndex]
      }

      // Restore children references using robust resolution
      let childrenIndices = savedNode.resolveChildrenIndices(in: self)
      node.children = childrenIndices.compactMap { dictionary[$0] }
    }

    // Restore head node connections using robust resolution
    if let originalNextIndex = undoState.originalHeadNode.resolveNextIndex(in: self) {
      headNode.next = dictionary[originalNextIndex]
    }
    let originalChildrenIndices = undoState.originalHeadNode.resolveChildrenIndices(in: self)
    headNode.children = originalChildrenIndices.compactMap { dictionary[$0] }

    // Restore target node's previous reference
    if let targetNode = dictionary[undoState.targetIndex] {
      if let targetPrevIndex = undoState.deletedNodes.first(where: { savedNode in
        savedNode.nextIndex == undoState.targetIndex
      })?.index {
        targetNode.previous = dictionary[targetPrevIndex]
      }
    }

    return true
  }
  
  /// Helper method to collect all moves that come before the target node
  private func collectMovesBeforeTarget(from node: Node, targetIndex: MoveIndex, into result: inout [Node]) {
    // Don't delete the head node or the target node
    if node.index == MoveTree.minimumIndex || node.index == targetIndex {
      // For the target node, we want to keep it and everything after it
      if node.index == targetIndex {
        return // Stop here, don't delete this node or its descendants
      }
      
      // For head node, check its children and next
      if let next = node.next {
        if next.index != targetIndex {
          collectMovesBeforeTarget(from: next, targetIndex: targetIndex, into: &result)
        }
      }
      
      for child in node.children {
        if child.index != targetIndex {
          collectMovesBeforeTarget(from: child, targetIndex: targetIndex, into: &result)
        }
      }
      return
    }
    
    // Check if this node is on the path to the target
    if isNodeOnPathToTarget(node: node, targetIndex: targetIndex) {
      // This node is on the path to target, so we delete it
      // but we need to check its siblings first
      for child in node.children {
        if child.index != targetIndex && !isNodeOnPathToTarget(node: child, targetIndex: targetIndex) {
          // This child is not on the path to target, delete it completely
          collectNodesToDelete(node: child, into: &result)
        } else if child.index != targetIndex {
          // This child is on the path, recurse
          collectMovesBeforeTarget(from: child, targetIndex: targetIndex, into: &result)
        }
      }
      
      // Add this node to deletion list (it's on the path but before target)
      result.append(node)
      
      // Continue with next node if it's not the target
      if let next = node.next, next.index != targetIndex {
        collectMovesBeforeTarget(from: next, targetIndex: targetIndex, into: &result)
      }
    } else {
      // This node is not on the path to target, delete it and all its descendants
      collectNodesToDelete(node: node, into: &result)
    }
  }
  
  /// Helper method to check if a node is on the path to the target
  private func isNodeOnPathToTarget(node: Node, targetIndex: MoveIndex) -> Bool {
    var current: Node? = dictionary[targetIndex]
    while let currentNode = current {
      if currentNode.index == node.index {
        return true
      }
      current = currentNode.previous
    }
    return false
  }

  /// Promotes a variation to become the main line or a higher priority variation.
  ///
  /// This method moves a variation branch to a higher position in the tree hierarchy.
  /// If the variation is already the main line, no operation is performed.
  ///
  /// - parameter index: The index of any move within the variation to promote.
  /// - returns: `true` if the promotion was successful, `false` if no promotion was needed or possible.
  ///
  /// **Algorithm**:
  /// 1. Find the root node of the variation containing the given index
  /// 2. If already main variation, return early
  /// 3. Swap the variation root with its "elder brother" (the current main/priority line)
  /// 4. Reorganize the siblings array to maintain proper hierarchy
  /// 5. Update parent references to point to the promoted variation
  ///
  /// **Example**: Promoting `2. Nc3` in `1. e4 e5 (2. Nc3 Nf6) 2. Nf3 Nc6`
  /// Results in: `1. e4 e5 2. Nc3 Nf6 (2. Nf3 Nc6)`
  @discardableResult
  public mutating func promote(index: MoveIndex) -> Bool {
    // Step 1: Find the root node of the variation
    guard let variationRootNode = getVariationRootNode(for: index) else {
      return false // Index not found
    }

    // Step 2: If already the main variation (root), no promotion needed
    if variationRootNode == headNode {
      return false // Already main variation
    }

    // Step 3: Handle root-level variations specially
    guard let parent = variationRootNode.previous else {
      return false // Should not happen if not root
    }

    // The elder brother is the current next node of the parent
    guard let elderBrother = parent.next else {
      return false // Should not happen
    }
    
    // Step 4: Reorganize children array according to the specified algorithm

    // Remove variationRootNode from parent's siblings (it's currently there)
    parent.children.removeAll { $0 === variationRootNode }

    // Insert elderBrother at the beginning of tmpChildren
    parent.children.insert(elderBrother, at: 0)

    // Step 5: Update parent references
    parent.next = variationRootNode

    return true
  }

  /// Promotes a variation with undo support.
  ///
  /// - parameter index: The index of any move within the variation to promote.
  ///
  /// - returns: A tuple containing success status and the undo state.
  ///
  public mutating func promoteWithUndo(index: MoveIndex) -> (Bool, PromoteVariationUndoState?) {
    // Step 1: Find the root node of the variation
    guard let variationRootNode = getVariationRootNode(for: index) else {
      return (false, nil) // Index not found
    }

    // Step 2: If already the main variation (root), no promotion needed
    if variationRootNode == headNode {
      return (false, nil) // Already main variation
    }

    // Step 3: Handle root-level variations specially
    guard let parent = variationRootNode.previous else {
      return (false, nil) // Should not happen if not root
    }

    // The elder brother is the current next node of the parent
    guard let elderBrother = parent.next else {
      return (false, nil) // Should not happen
    }

    // Save state for undo
    let promoteStep = PromotionStep(
      variationRootIndex: variationRootNode.index,
      parentIndex: parent.index,
      originalNextIndex: elderBrother.index,
      originalSiblingOrder: parent.children.map { $0.index }
    )

    // Perform the actual promotion
    let success = promote(index: index)

    if success {
      let undoState = PromoteVariationUndoState(
        promoteStep: promoteStep,
        targetIndex: index
      )
      return (true, undoState)
    }

    return (false, nil)
  }

  /// Undoes a promote variation operation.
  ///
  /// - parameter undoState: The undo state from the original promote operation.
  ///
  /// - returns: `true` if the undo was successful, `false` otherwise.
  ///
  @discardableResult
  public mutating func undoPromote(_ undoState: PromoteVariationUndoState) -> Bool {
    guard let parent = dictionary[undoState.promoteStep.parentIndex],
          dictionary[undoState.promoteStep.variationRootIndex] != nil,
          let originalNext = dictionary[undoState.promoteStep.originalNextIndex] else {
      return false
    }

    // Restore original next reference
    parent.next = originalNext

    // Restore original children order
    parent.children = undoState.promoteStep.originalSiblingOrder.compactMap { dictionary[$0] }

    return true
  }

  /// Promotes a variation to become the main variation by recursively promoting all parent variations.
  ///
  /// This method ensures that the entire path from the root to the specified move becomes
  /// the main variation. It works by repeatedly promoting variation roots until
  /// the target move is on the main line.
  ///
  /// - parameter index: The index of any move within the variation to promote to main.
  /// - returns: `true` if the promotion was successful, `false` if the move was not found.
  ///
  /// **Algorithm**:
  /// 1. While the move is not on the main variation:
  /// 2. Find the variation root and promote it
  /// 3. Repeat until the entire path is main
  ///
  /// **Example**: For a deeply nested variation like `1. e4 e5 (2. Nc3 (2... f5 3. exf5) Nf6) 2. Nf3`,
  /// promoting the move `3. exf5` will result in: `1. e4 e5 2. Nc3 f5 3. exf5 (2... Nf6) (2. Nf3)`
  ///
  /// **Use Cases**:
  /// - Converting analysis variations to main lines
  /// - Reorganizing game trees based on new analysis
  /// - Preparing variations for publication or study
  @discardableResult
  public mutating func promoteToMainVariation(index: MoveIndex) -> Bool {
    // Check if the move exists
    guard dictionary[index] != nil else {
      return false // Index not found
    }

    // Keep promoting until the target is on the main variation
    while !isOnMainVariation(index: index) {
      // Find the variation root for this index
      guard let variationRoot = getVariationRootNode(for: index) else {
        return false // Should not happen if index exists
      }

      // Promote the variation root
    guard promote(index: variationRoot.index) else {
        return false // Promotion failed
      }
    }

    return true
  }

  /// Promotes a variation to become the main variation with undo support.
  ///
  /// - parameter index: The index of any move within the variation to promote to main.
  ///
  /// - returns: A tuple containing success status and the undo state.
  ///
  public mutating func promoteToMainVariationWithUndo(index: MoveIndex) -> (Bool, PromoteToMainUndoState?) {
    // Check if the move exists
    guard dictionary[index] != nil else {
      return (false, nil) // Index not found
    }

    var promotionSteps: [PromotionStep] = []

    // Keep promoting until the target is on the main variation
    while !isOnMainVariation(index: index) {
      // Find the variation root for this index
      guard let variationRoot = getVariationRootNode(for: index) else {
        return (false, nil) // Should not happen if index exists
      }

      // Get promotion step info before promoting
      guard let parent = variationRoot.previous,
            let elderBrother = parent.next else {
        return (false, nil)
      }

      let promoteStep = PromotionStep(
        variationRootIndex: variationRoot.index,
        parentIndex: parent.index,
        originalNextIndex: elderBrother.index,
        originalSiblingOrder: parent.children.map { $0.index }
      )

      promotionSteps.append(promoteStep)

      // Promote the variation root
      guard promote(index: variationRoot.index) else {
        return (false, nil) // Promotion failed
      }
    }

    if !promotionSteps.isEmpty {
      let undoState = PromoteToMainUndoState(
        promotionSteps: promotionSteps,
        targetIndex: index
      )
      return (true, undoState)
    }

    return (true, nil) // Already on main variation
  }

  /// Undoes a promote to main variation operation.
  ///
  /// - parameter undoState: The undo state from the original promote to main operation.
  ///
  /// - returns: `true` if the undo was successful, `false` otherwise.
  ///
  @discardableResult
  public mutating func undoPromoteToMain(_ undoState: PromoteToMainUndoState) -> Bool {
    // Undo promotions in reverse order
    for promoteStep in undoState.promotionSteps.reversed() {
      guard let parent = dictionary[promoteStep.parentIndex],
            dictionary[promoteStep.variationRootIndex] != nil,
            let originalNext = dictionary[promoteStep.originalNextIndex] else {
        return false
      }

      // Restore original next reference
      parent.next = originalNext

      // Restore original children order
      parent.children = promoteStep.originalSiblingOrder.compactMap { dictionary[$0] }
    }

    return true
  }

  /// Recursively collects all nodes that should be deleted (node + all descendants).
  private func collectNodesToDelete(node: Node, into result: inout [Node]) {
    result.append(node)

    // Recursively delete the next node
    if let next = node.next {
      collectNodesToDelete(node: next, into: &result)
    }

    // Recursively delete all children nodes
    for child in node.children {
      collectNodesToDelete(node: child, into: &result)
    }
  }

  /// Finds the root node of the variation that contains the given move hash.
  ///
  /// - parameter moveHash: The move hash of the node to analyze.
  /// - returns: The root node of the variation, or `nil` if the move hash doesn't exist.
  ///
  /// The variation root node is defined as:
  /// - For the main variation: the actual root node of the tree
  /// - For other variations: the first node where `previous.next != current`
  ///
  /// This function traverses backwards from the given node until it finds
  /// the point where the variation branches off from its parent.
  internal func getVariationRootNode(for index: MoveIndex) -> Node? {
    guard let startNode = dictionary[index] else {
      return nil
    }

    var pointer = startNode

    while let previous = pointer.previous {
      // If the previous node's next is not the current pointer,
      // then the current pointer is the root of this variation
      if previous.next !== pointer {
        return pointer
      }

      pointer = previous
    }

    // If we reach here, we've traversed to the actual root of the tree
    return pointer
  }

  /// Determines if a node with the given move hash is on the main variation.
  ///
  /// - parameter moveHash: The move hash of the node to check.
  /// - returns: `true` if the node is on the main variation, `false` otherwise.
  ///
  /// This function uses `getVariationRootNode` to find the root of the variation
  /// containing the given node, then checks if that root is the tree's root node.
  public func isOnMainVariation(index: MoveIndex) -> Bool {
    guard let variationRoot = getVariationRootNode(for: index) else {
      return false
    }

    // A node is on the main variation if its variation root is the tree's root
    return variationRoot == headNode
  }

  /// Returns the index matching `move` in the next or child moves of the
  /// move contained at the given move hash.
  public func hasNextMove(containing move: Move, for index: MoveIndex) -> MoveIndex? {
    guard let node = index == MoveTree.minimumIndex ? headNode : dictionary[index] else {
      return nil
    }

    if let next = node.next, next.move == move {
      return next.index
    } else {
      return node.children.filter { $0.move == move }.first?.index
    }
  }

  /// Provides a single history for a given move hash.
  ///
  /// - parameter moveHash: The move hash from which to generate the history.
  /// - returns: An array of move indices sorted from beginning to end with
  /// the end being the move with the provided `moveHash`.
  ///
  /// For chess this would represent an array of all the move indices
  /// from the starting move until the move defined by `moveHash`, accounting
  /// for any branching variations in between.
  public func history(for index: MoveIndex) -> [MoveIndex] {
    var currentNode = dictionary[index]
    var history: [MoveIndex] = []

    while currentNode != nil {
      if let node = currentNode {
        history.append(node.index)
      }

      currentNode = currentNode?.previous
    }

    return history.reversed()
  }

  /// Provides a single future for a given move hash.
  ///
  /// - parameter moveHash: The move hash from which to generate the future.
  /// - returns: An array of move indices sorted from beginning to end.
  ///
  /// For chess this would represent an array of all the move indices
  /// from the move after the move defined by `moveHash` to the last move
  /// of the variation.
  public func future(for index: MoveIndex) -> [MoveIndex] {
    var currentNode = dictionary[index]
    var future: [MoveIndex] = []

    while currentNode != nil {
      currentNode = currentNode?.next

      if let node = currentNode {
        future.append(node.index)
      }
    }

    return future
  }

  /// Returns the full variation for a move at the provided `index`.
  ///
  /// This returns the sum of `history(for:)` and `future(for:)`.
  public func fullVariation(for index: MoveIndex) -> [MoveIndex] {
    history(for: index) + future(for: index)
  }

  /// Returns all variation indices that branch from the given move hash.
  ///
  /// - parameter moveHash: The move hash from which to find variations.
  /// - returns: An array of indices representing the first move of each variation,
  /// sorted by variation priority (main line first, then variations by order).
  ///
  /// This method finds all alternative moves that can be played from the position
  /// after the given move hash. It includes both the main continuation and all variations.
  public func variations(from index: MoveIndex) -> [MoveIndex] {
    guard let node = dictionary[index] else {
      return []
    }

    var variations: [MoveIndex] = []

    // Add all sibling variations
    for child in node.children {
     variations.append(child.index)
    }

    return variations
  }

  /// Returns the PGN elements for a variation starting from the given move hash.
  ///
  /// - parameter moveHash: The move hash of the starting node of the variation.
  /// - returns: An array of PGN elements representing the variation.
  ///
  /// This method generates the PGN representation for a specific variation branch,
  /// including move numbers, moves, and nested sub-variations.
  public func variationPGN(from index: MoveIndex) -> [PGNElement] {
    guard let startNode = dictionary[index] else {
      return []
    }

    return pgn(for: startNode)
  }

  private func indices(between start: MoveIndex, and end: MoveIndex) -> [MoveIndex] {
    var result = [MoveIndex]()

    let endNode = dictionary[end]
    var currentNode = dictionary[start]

    while currentNode != endNode {
      if let currentNode {
        result.append(currentNode.index)
      }

      currentNode = currentNode?.previous
    }

    return result
  }

  /// Provides the shortest path through the move tree
  /// from the given start and end indices.
  ///
  /// - parameter startIndex: The starting index of the path.
  /// - parameter endIndex: The ending index of the path.
  /// - returns: An array of indices starting with the index after `startIndex`
  /// and ending with `endIndex`. If `startIndex` and `endIndex`
  /// are the same, an empty array is returned.
  ///
  /// The purpose of this path is return the indices of the moves required to
  /// go from the current position at `startIndex` and end up with the
  /// final position at `endIndex`, so `startIndex` is included in the returned
  /// array, but `endIndex` is not. The path direction included with the index
  /// indicates the direction to move to get to the next index.
  public func path(
    from startIndex: MoveIndex,
    to endIndex: MoveIndex
  ) -> [(direction: PathDirection, index: MoveIndex)] {
    var results = [(PathDirection, MoveIndex)]()
    let startHistory = history(for: startIndex)
    let endHistory = history(for: endIndex)

    if startIndex == endIndex {
      // keep results array empty
    } else if startHistory.contains(endIndex) {
      results = indices(between: startIndex, and: endIndex)
        .map { (.reverse, $0) }
    } else if endHistory.contains(startIndex) {
      results = indices(between: endIndex, and: startIndex)
        .map { (.forward, $0) }
        .reversed()
    } else {
      // lowest common ancestor
      guard
        let lca = zip(startHistory, endHistory).filter({ $0 == $1 }).last?.0,
        let startLCAIndex = startHistory.firstIndex(where: { $0 == lca }),
        let endLCAIndex = endHistory.firstIndex(where: { $0 == lca })
      else {
        return []
      }

      let startToLCAPath = startHistory[startLCAIndex...]
        .reversed()  // reverse since history is in ascending order
        .dropLast()  // drop LCA; to be included in the next array
        .map { (PathDirection.reverse, $0) }

      let LCAtoEndPath = endHistory[(endLCAIndex+1)...]
        .map { (PathDirection.forward, $0) }

      results = startToLCAPath + LCAtoEndPath
    }

    return results
  }

  /// The direction of the ``MoveTree`` path.
  public enum PathDirection: Sendable {
    /// Move forward (i.e. perform a move).
    case forward
    /// Move backward (i.e. undo a move).
    case reverse
  }

  // MARK: - PGN

  /// An element for representing the ``MoveTree`` in
  /// PGN (Portable Game Notation) format.
  public enum PGNElement: Hashable, Sendable {
    /// e.g. `1.`
    case whiteNumber(Int)
    /// e.g. `1...`
    case blackNumber(Int)
    /// e.g. `e4! {Unexpected to everyone}`
    case move(Move, MoveIndex)
    /// e.g. `(`
    case variationStart
    /// e.g. `)`
    case variationEnd
  }

  private func pgn(for node: Node?) -> [PGNElement] {
    guard let node else {
      return []
    }
    
    var result: [PGNElement] = []
    
    if node == headNode {
      result.append(.move(node.move, node.index))
    } else {
      switch node.color {
      case .white:
        result.append(.whiteNumber(node.number))
      case .black:
        result.append(.blackNumber(node.number))
      }
      result.append(.move(node.move, node.index))
    }
    
    var curr = node

    while curr.next != nil {
      if curr.next!.color == .white {
        result.append(.whiteNumber(curr.next!.number))
      } else {
        result.append(.blackNumber(curr.next!.number))
      }
      result.append(.move(curr.next!.move, curr.next!.index))

      // recursively generate PGN for all children nodes
      curr.children.forEach { child in
        result.append(.variationStart)
        result.append(contentsOf: pgn(for: child))
        result.append(.variationEnd)
      }
      curr = curr.next!
    }
    return result
  }

  /// Returns the ``MoveTree`` as an array of PGN
  /// (Portable Game Format) elements.
  public var pgnRepresentation: [PGNElement] {
    pgn(for: headNode)
  }
}

// MARK: - Testing Helpers
extension MoveTree {
  
  /// Returns the index of a move at the given sequential position in the main variation.
  /// - Parameter position: The zero-based position in the main variation (0 = first move, 1 = second move, etc.)
  /// - Returns: The index of the move at that position, or nil if the position doesn't exist
  internal func mainVariationIndex(at position: Int) -> MoveIndex? {
    guard position >= 0 else { return nil }
    
    var currentNode: Node? = headNode.next
    var currentPosition = 0
    
    while let node = currentNode {
      if currentPosition == position {
        return node.index
      }
      currentPosition += 1
      currentNode = node.next
    }
    
    return nil
  }
  
  /// Returns all indices in the main variation in sequential order.
  /// - Returns: Array of indices ordered from first move to last move in main variation
  internal var mainVariationIndicesInOrder: [MoveIndex] {
    var indices: [MoveIndex] = []
    var currentNode: Node? = headNode.next
    
    while let node = currentNode {
      indices.append(node.index)
      currentNode = node.next
    }
    
    return indices
  }
}

// MARK: - Equatable
extension MoveTree: Equatable {

  public static func == (lhs: MoveTree, rhs: MoveTree) -> Bool {
    lhs.dictionary == rhs.dictionary
  }

}

// MARK: - Node
extension MoveTree {

  /// Object that represents a node in the move tree.
  class Node: Hashable, @unchecked Sendable {

    /// The move for this node.
    public var move: Move
    public var color: Piece.Color
    public var number: Int
    
    /// The position assessment for this node.
    var positionComment = ""
    /// The index for this node.
    public let index: MoveIndex
    /// The previous node.
    fileprivate(set) weak var previous: Node?
    /// The next node.
    fileprivate(set) weak var next: Node?
    /// children nodes (i.e. variation moves).
    internal var children: [Node] = []

    fileprivate init(index: MoveIndex, move: Move, color: Piece.Color, number: Int) {
      self.index = index
      self.move = move
      self.color = color
      self.number = number
    }
    
    fileprivate func nextColorAndNumber(_ isHeadNode: Bool) -> (Piece.Color, Int) {
      switch self.color {
      case .white:
        return (Piece.Color.black, self.number + (isHeadNode ? 1 : 0))
      case .black:
        return (Piece.Color.white, self.number + 1)
      }
    }

    // MARK: Equatable
    static func == (lhs: Node, rhs: Node) -> Bool {
      lhs.index == rhs.index
    }

    // MARK: Hashable
    func hash(into hasher: inout Hasher) {
      hasher.combine(index)
    }
  }

}
