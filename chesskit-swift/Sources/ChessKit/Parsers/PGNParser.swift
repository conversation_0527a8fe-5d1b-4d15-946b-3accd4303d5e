//
//  PGNParser.swift
//  ChessKit
//

import Foundation

/// Parses and converts the Portable Game Notation (PGN)
/// of a chess game.
public enum PGNParser {

  /// Represents a token in PGN movetext.
  private enum Token: Equatable {
    case moveNumber(Int)
    case move(String)
    case variationStart
    case variationEnd
    case comment(String)
    case annotation(String)
    case result(String)
    case whitespace
  }

  /// Contains the contents of a single parsed move pair.
  private struct ParsedMove {
    /// The number of the move within the game.
    let number: Int
    /// The white move SAN string, annotation, and comment.
    let whiteMove: (san: String, annotation: MetaMove.Assessment, comment: String)
    /// The black move SAN string, annotation, and comment (can be `nil`).
    let blackMove: (san: String, annotation: MetaMove.Assessment, comment: String)?
    /// The result of the game, if applicable.
    let result: Result?

    enum Result: String {
      case whiteWin = "1-0"
      case blackWin = "0-1"
      case draw = "1/2-1/2"
    }
  }

  /// Represents a parsed move element with variations.
  private struct MoveElement {
    let moveNumber: Int?
    let color: Piece.Color?
    let san: String
    let moveAssessment: MetaMove.Assessment
    let positionAssessment: MetaMove.Assessment
    let positionComment: Move.PositionComment
    var variations: [MoveSequence] = []
    
    init(
      moveNumber: Int?,
      color: Piece.Color?,
      san: String,
      moveAssessment: MetaMove.Assessment = .null,
      positionAssessment: MetaMove.Assessment = .null,
      positionComment: Move.PositionComment = Move.PositionComment()
    ) {
      self.moveNumber = moveNumber
      self.color = color
      self.san = san
      self.moveAssessment = moveAssessment
      self.positionAssessment = positionAssessment
      self.positionComment = positionComment
    }
  }

  /// Represents a sequence of moves.
  private struct MoveSequence {
    var elements: [MoveElement] = []
    var result: String? = nil
    var initialPositionComments: [String] = []
  }

  // MARK: - Private Methods

  /// Tokenizes the movetext portion of a PGN string.
  private static func tokenize(movetext: String) -> [Token] {
    var tokens: [Token] = []
    var currentIndex = movetext.startIndex

    while currentIndex < movetext.endIndex {
      let remainingText = String(movetext[currentIndex...])

      // Skip whitespace
      if remainingText.first?.isWhitespace == true {
        currentIndex = movetext.index(after: currentIndex)
        continue
      }

      // Match move number (e.g., "1.", "2...", "10.")
      if let match = remainingText.range(of: #"^\d+\.{1,3}"#, options: .regularExpression) {
        let numberStr = String(remainingText[..<match.upperBound]).replacingOccurrences(of: ".", with: "")
        if let number = Int(numberStr) {
          tokens.append(.moveNumber(number))
        }
        currentIndex = movetext.index(currentIndex, offsetBy: match.upperBound.utf16Offset(in: remainingText))
        continue
      }

      // Match variation start
      if remainingText.hasPrefix("(") {
        tokens.append(.variationStart)
        currentIndex = movetext.index(after: currentIndex)
        continue
      }

      // Match variation end
      if remainingText.hasPrefix(")") {
        tokens.append(.variationEnd)
        currentIndex = movetext.index(after: currentIndex)
        continue
      }

      // Match comment with improved bracket handling for annotations like {[%clk 01:20:52]}
      if remainingText.hasPrefix("{") {
        if let commentContent = parseComplexComment(from: remainingText, startIndex: currentIndex) {
          tokens.append(.comment(commentContent.content))
          currentIndex = movetext.index(currentIndex, offsetBy: commentContent.length)
          continue
        }
      }

      // Match annotation (e.g., "$1", "!", "?", "!!", etc.)
      if let match = remainingText.range(of: #"^(\$\d+|[!?]{1,2})"#, options: .regularExpression) {
        let annotation = String(remainingText[match])
        tokens.append(.annotation(annotation))
        currentIndex = movetext.index(currentIndex, offsetBy: match.upperBound.utf16Offset(in: remainingText))
        continue
      }

      // Match game result
      if let match = remainingText.range(of: #"^(1-0|0-1|1/2-1/2|\*)"#, options: .regularExpression) {
        let result = String(remainingText[match])
        tokens.append(.result(result))
        currentIndex = movetext.index(currentIndex, offsetBy: match.upperBound.utf16Offset(in: remainingText))
        continue
      }

      // Match move (including castling)
      if let match = remainingText.range(of: #"^([KQRBN]?[a-h]?[1-8]?x?[a-h][1-8](\=[QRBN])?[+#]?|[Oo0]-[Oo0](-[Oo0])?)"#, options: .regularExpression) {
        let move = String(remainingText[match])
        tokens.append(.move(move))
        currentIndex = movetext.index(currentIndex, offsetBy: match.upperBound.utf16Offset(in: remainingText))
        continue
      }

      // Skip unrecognized character
      currentIndex = movetext.index(after: currentIndex)
    }

    return tokens
  }

  /// Parses tokens into a move sequence with variations.
  private static func parseTokens(_ tokens: [Token]) -> MoveSequence {
    var index = 0
    return parseMoveSequence(tokens, index: &index)
  }

  /// Recursively parses a move sequence.
  private static func parseMoveSequence(_ tokens: [Token], index: inout Int) -> MoveSequence {
    var sequence = MoveSequence()
    var currentMoveNumber: Int? = nil
    var currentColor: Piece.Color? = nil
    var pendingMove: String? = nil
    var pendingMoveAssessments: [MetaMove.Assessment] = []
    var pendingPositionAssessments: [MetaMove.Assessment] = []
    var pendingComments: [String] = []
    var initialPositionComments: [String] = []

    while index < tokens.count {
      let token = tokens[index]

      switch token {
      case .moveNumber(let number):
        // Flush any pending move before processing new move number
        if let move = pendingMove {
          let element = MoveElement(
            moveNumber: currentMoveNumber,
            color: currentColor,
            san: move,
            moveAssessment: pendingMoveAssessments.first ?? .null,
            positionAssessment: pendingPositionAssessments.first ?? .null,
            positionComment: mergeComments(pendingComments)
          )
          sequence.elements.append(element)
          pendingMove = nil
          pendingMoveAssessments = []
          pendingPositionAssessments = []
          pendingComments = []
        }

        currentMoveNumber = number
        currentColor = .white // Move numbers always start with white
        index += 1

      case .move(let san):
        // If we have a pending move, flush it first
        if let move = pendingMove {
          let element = MoveElement(
            moveNumber: currentMoveNumber,
            color: currentColor,
            san: move,
            moveAssessment: pendingMoveAssessments.first ?? .null,
            positionAssessment: pendingPositionAssessments.first ?? .null,
            positionComment: mergeComments(pendingComments)
          )
          sequence.elements.append(element)

          // Update color for next move
          currentColor = (currentColor == .white) ? .black : .white
          pendingMoveAssessments = []
          pendingPositionAssessments = []
          pendingComments = []
        }

        pendingMove = san
        index += 1

      case .annotation(let ann):
        let assessment: MetaMove.Assessment
        if ann.hasPrefix("$") {
          assessment = MetaMove.Assessment(rawValue: ann) ?? .null
        } else {
          assessment = MetaMove.Assessment(notation: ann) ?? .null
        }
        
        // Categorize the assessment
        switch assessment.assessmentType {
        case .moveAssessment:
          pendingMoveAssessments.append(assessment)
        case .positionAssessment:
          pendingPositionAssessments.append(assessment)
        case .none:
          break // Ignore null assessments
        }
        index += 1

      case .comment(let comment):
        if currentMoveNumber == nil && pendingMove == nil {
          // This is an initial position comment (before any moves)
          initialPositionComments.append(comment)
        } else {
          pendingComments.append(comment)
        }
        index += 1

      case .variationStart:
        // Flush any pending move before processing variation
        if let move = pendingMove {
          let element = MoveElement(
            moveNumber: currentMoveNumber,
            color: currentColor,
            san: move,
            moveAssessment: pendingMoveAssessments.first ?? .null,
            positionAssessment: pendingPositionAssessments.first ?? .null,
            positionComment: mergeComments(pendingComments)
          )
          sequence.elements.append(element)

          // Update color for next move
          currentColor = (currentColor == .white) ? .black : .white
          pendingMove = nil
          pendingMoveAssessments = []
          pendingPositionAssessments = []
          pendingComments = []
        }

        // Parse variation recursively
        index += 1 // Skip opening parenthesis
        let variation = parseMoveSequence(tokens, index: &index)

        // Add variation to the last move element
        if !sequence.elements.isEmpty {
          sequence.elements[sequence.elements.count - 1].variations.append(variation)
        }

      case .variationEnd:
        // Flush any pending move before ending variation
        if let move = pendingMove {
          let element = MoveElement(
            moveNumber: currentMoveNumber,
            color: currentColor,
            san: move,
            moveAssessment: pendingMoveAssessments.first ?? .null,
            positionAssessment: pendingPositionAssessments.first ?? .null,
            positionComment: mergeComments(pendingComments)
          )
          sequence.elements.append(element)
        }
        index += 1 // Skip closing parenthesis
        return sequence

      case .result(let result):
        sequence.result = result
        index += 1

      case .whitespace:
        index += 1
      }
    }

    // Flush any remaining pending move
    if let move = pendingMove {
      let element = MoveElement(
        moveNumber: currentMoveNumber,
        color: currentColor,
        san: move,
        moveAssessment: pendingMoveAssessments.first ?? .null,
        positionAssessment: pendingPositionAssessments.first ?? .null,
        positionComment: mergeComments(pendingComments)
      )
      sequence.elements.append(element)
    }

    // Set initial position comments
    sequence.initialPositionComments = initialPositionComments

    return sequence
  }

  // MARK: - Public

  /// Parses a PGN string containing multiple games and returns an array of games.
  ///
  /// - parameter pgn: The PGN string containing one or more chess games.
  /// - returns: An array of Swift representations of the chess games.
  ///     Returns an empty array if no valid games are found.
  ///
  public static func parseMultiple(games pgn: String) -> [Game] {
    // Split PGN by game boundaries - look for result markers followed by tags or end of string
    let gameStrings = splitPGNIntoGames(pgn)
    
    var games: [Game] = []
    
    for gameString in gameStrings {
      if let game = parse(game: gameString) {
        games.append(game)
      }
    }
    
    return games
  }
  
  /// Splits a multi-game PGN string into individual game strings.
  private static func splitPGNIntoGames(_ pgn: String) -> [String] {
    let normalizedPGN = pgn
      .replacingOccurrences(of: "\r\n", with: "\n")
      .replacingOccurrences(of: "\r", with: "\n")
    
    // Pattern to match game result followed by optional whitespace and either:
    // 1. A tag (starts with [)
    // 2. End of string
    // 3. A move number (starts with digit followed by .)
    let gameEndPattern = #"(1-0|0-1|1/2-1/2|\*)(\s*(?=\[)|$|\s*(?=\d+\.))"#
    
    let regex = try! NSRegularExpression(pattern: gameEndPattern, options: [])
    let range = NSRange(0..<normalizedPGN.utf16.count)
    
    let matches = regex.matches(in: normalizedPGN, options: [], range: range)
    
    var games: [String] = []
    var lastEndIndex = 0
    
    for match in matches {
      let matchEnd = match.range.upperBound
      let gameString = String(normalizedPGN[normalizedPGN.index(normalizedPGN.startIndex, offsetBy: lastEndIndex)..<normalizedPGN.index(normalizedPGN.startIndex, offsetBy: match.range.upperBound)])
      
      // Only add non-empty games
      let trimmedGame = gameString.trimmingCharacters(in: .whitespacesAndNewlines)
      if !trimmedGame.isEmpty {
        games.append(trimmedGame)
      }
      
      lastEndIndex = matchEnd
    }
    
    // Handle case where there's remaining content after the last match
    if lastEndIndex < normalizedPGN.count {
      let remainingString = String(normalizedPGN[normalizedPGN.index(normalizedPGN.startIndex, offsetBy: lastEndIndex)...])
      let trimmedRemaining = remainingString.trimmingCharacters(in: .whitespacesAndNewlines)
      if !trimmedRemaining.isEmpty {
        games.append(trimmedRemaining)
      }
    }
    
    // If no matches found, treat the entire string as a single game
    if games.isEmpty {
      let trimmed = normalizedPGN.trimmingCharacters(in: .whitespacesAndNewlines)
      if !trimmed.isEmpty {
        games.append(trimmed)
      }
    }
    
    return games
  }

  /// Parses a PGN string and returns a game.
  ///
  /// - parameter pgn: The PGN string of a chess game.
  /// - parameter position: The starting position of the chess game.
  ///     Defaults to the standard position.
  /// - returns: A Swift representation of the chess game,
  ///     or `nil` if the PGN is invalid.
  ///
  public static func parse(
    game pgn: String,
    startingWith position: Position = .standard
  ) -> Game? {
    let processedPGN = pgn
      .replacingOccurrences(of: "\n", with: " ")
      .replacingOccurrences(of: "\r", with: " ")

    let range = NSRange(0..<processedPGN.utf16.count)

    // Parse tags
    let tags: [(String, String)]? = try? NSRegularExpression(pattern: Pattern.tags)
      .matches(in: processedPGN, range: range)
      .map {
        NSString(string: pgn).substring(with: $0.range)
          .trimmingCharacters(in: .whitespacesAndNewlines)
      }
      .compactMap { tag in
        let tagRange = NSRange(0..<tag.utf16.count)
        let matches = try? NSRegularExpression(pattern: Pattern.tagPair)
          .matches(in: tag, range: tagRange)

        if let matches,
          matches.count >= 1,
          matches[0].numberOfRanges >= 3
        {
          let key = matches[0].range(at: 1)
          let value = matches[0].range(at: 2)

          return (
            NSString(string: tag).substring(with: key)
              .trimmingCharacters(in: .whitespacesAndNewlines),
            NSString(string: tag).substring(with: value)
              .trimmingCharacters(in: .whitespacesAndNewlines)
          )
        } else {
          return nil
        }
      }

    let parsedTags = parsed(tags: Dictionary<String, String>(tags ?? []) { a, _ in a })

    // Determine starting position: use FEN tag if present and SetUp is "1", otherwise use provided position
    var actualStartingPosition = position
    if !parsedTags.fen.isEmpty && parsedTags.setUp == "1" {
      if let fenPosition = FENParser.parse(fen: parsedTags.fen) {
        actualStartingPosition = fenPosition
      }
    }

    // Extract movetext (everything after tags)
    var movetext = processedPGN
    if let tags = tags, !tags.isEmpty {
      // Remove only PGN tag pairs (not time annotations like [%clk] or [%emt])
      // PGN tags have format: [TagName "TagValue"]
      let tagPattern = #"\[[A-Za-z][A-Za-z0-9_]*\s+"[^"]*"\]"#
      movetext = movetext.replacingOccurrences(of: tagPattern, with: "", options: .regularExpression)
        .trimmingCharacters(in: .whitespacesAndNewlines)
    }

    // Tokenize and parse movetext
    let tokens = tokenize(movetext: movetext)
    let moveSequence = parseTokens(tokens)
    
    if moveSequence.elements.isEmpty && 
       moveSequence.initialPositionComments.isEmpty && 
       (tags == nil || tags!.isEmpty) {
      return nil
    }

    // Build game from parsed sequence with the correct starting position
    var game = Game(startingWith: actualStartingPosition, tags: parsedTags)
    
    // Handle initial position comments
    if !moveSequence.initialPositionComments.isEmpty {
      let initialPositionComment = mergeComments(moveSequence.initialPositionComments)
      let headNodeMove = Move(metaMove: nil, positionComment: initialPositionComment)
      game.moves.setNodeMove(index: MoveTree.minimumIndex, newMove: headNodeMove)
    }
    
    buildGame(from: moveSequence, into: &game, startingFrom: MoveTree.minimumIndex)

    // Set result if found
    if let result = moveSequence.result {
      game.tags.result = result
    }

    return game
  }

  /// Builds a game from a parsed move sequence.
  private static func buildGame(
    from sequence: MoveSequence,
    into game: inout Game,
    startingFrom parentIndex: MoveTree.MoveIndex
  ) {
    var currentIndex = parentIndex

    for element in sequence.elements {
      guard let currentPosition = game.positions[currentIndex] else {
        continue
      }

      // Parse the move
      guard var metaMove = SANParser.parse(metaMove: element.san, in: currentPosition) else {
        continue
      }

      // Set annotations and comments
      metaMove.moveAssessment = element.moveAssessment
      metaMove.positionAssessment = element.positionAssessment
      let move = Move(metaMove: metaMove, positionComment: element.positionComment)

      // Add move to game
      let newIndex = game.make(move: move, from: currentIndex)

      // Process variations recursively
      for variation in element.variations {
        buildGame(from: variation, into: &game, startingFrom: currentIndex)
      }

      // Update current index for next move
      currentIndex = newIndex
    }
  }

  /// Converts a ``Game`` object into a PGN string.
  ///
  /// - parameter game: The chess game to convert.
  /// - returns: A string containing the PGN of `game`.
  ///
  public static func convert(game: Game) -> String {
    var pgn = ""
    
    // tags
    
    // Handle result tag with dynamic determination if empty
    let resultTag: Game.Tag
    if game.tags.result.isEmpty {
      let determinedResult = determineGameResult(for: game)
      var dynamicResultTag = Game.Tag(name: "Result")
      dynamicResultTag.wrappedValue = determinedResult
      resultTag = dynamicResultTag
    } else {
      resultTag = game.tags.$result
    }
    
    [
      game.tags.$event,
      game.tags.$site,
      game.tags.$date,
      game.tags.$round,
      game.tags.$white,
      game.tags.$black,
      resultTag,
      game.tags.$whiteTeam,
      game.tags.$blackTeam,
      game.tags.$whiteTitle,
      game.tags.$blackTitle,
      game.tags.$whiteElo,
      game.tags.$blackElo,
      game.tags.$whiteFideId,
      game.tags.$blackFideId,
      game.tags.$annotator,
      game.tags.$plyCount,
      game.tags.$timeControl,
      game.tags.$time,
      game.tags.$termination,
      game.tags.$mode,
      game.tags.$fen,
      game.tags.$setUp
    ]
      .map(\.pgn)
      .filter { !$0.isEmpty }
      .forEach { pgn += $0 + "\n" }
    
    game.tags.other.sorted(by: <).forEach { key, value in
      pgn += "[\(key) \"\(value)\"]\n"
    }
    
    if !pgn.isEmpty {
      pgn += "\n"  // extra line between tags and movetext
    }
    
    let pgnRepresentation = game.moves.pgnRepresentation
    for (i, element) in pgnRepresentation.enumerated() {
      switch element {
      case .whiteNumber(let number):
        pgn += "\(number). "
      case .blackNumber(let number):
        if i == 1 || (i > 1 && (pgnRepresentation[i-1] == .variationStart || pgnRepresentation[i-1] == .variationEnd)) {
          pgn += "\(number)... "
        }
      case let .move(move, _):
        if !move.pgnDiscription.isEmpty {
          pgn += move.pgnDiscription + " "
        }
      case .variationStart:
        pgn += "("
      case .variationEnd:
        pgn = pgn.trimmingCharacters(in: .whitespaces)
        pgn += ") "
      }
    }

    // Add result at the end of movetext
    let result = determineGameResult(for: game)
    if !result.isEmpty {
      pgn = pgn.trimmingCharacters(in: .whitespaces)
      if !pgn.isEmpty {
        pgn += " "
      }
      pgn += result
    }

    return pgn.trimmingCharacters(in: .whitespaces)
  }

  /// Determines the appropriate game result based on the game state.
  /// If the result tag is already set and valid, returns it.
  /// Otherwise, analyzes the final position to determine the result.
  private static func determineGameResult(for game: Game) -> String {
    // First check if result tag is already set and not empty
    let existingResult = game.tags.result.trimmingCharacters(in: .whitespacesAndNewlines)
    if !existingResult.isEmpty {
      return existingResult
    }
    
    // If no result tag or empty, analyze the final position
    let lastIndex = game.moves.lastMainVariationIndex
    
    // If we're at the starting position (no moves), return ongoing
    if lastIndex == MoveTree.minimumIndex {
      return "*"
    }
    
    guard let lastMoveNode = game.moves.dictionary[lastIndex],
          let lastMove = lastMoveNode.move.metaMove else {
      // Can't access last move, return ongoing
      return "*"
    }
    
    // Check the check state of the last move
    switch lastMove.checkState {
    case .checkmate:
      // Checkmate - winner is the side that made the last move
      // The side to move in current position is the losing side
      guard let finalPosition = game.positions[lastIndex] else {
        return "*"
      }
      return finalPosition.sideToMove == .white ? "0-1" : "1-0"
    case .stalemate:
      // Stalemate - draw
      return "1/2-1/2"
    case .check, .none:
      // Game is still ongoing
      return "*"
    }
  }

  private static func parsed(tags: [String: String]) -> Game.Tags {
    var gameTags = Game.Tags()

    tags.forEach { key, value in
      switch key.lowercased() {
      case "event": gameTags.event = value
      case "site": gameTags.site = value
      case "date": gameTags.date = value
      case "round": gameTags.round = value
      case "white": gameTags.white = value
      case "black": gameTags.black = value
      case "whiteteam": gameTags.whiteTeam = value
      case "blackteam": gameTags.blackTeam = value
      case "whitetitle": gameTags.whiteTitle = value
      case "blacktitle": gameTags.blackTitle = value
      case "whiteelo": gameTags.whiteElo = value
      case "blackelo": gameTags.blackElo = value
      case "whitefideid": gameTags.whiteFideId = value
      case "blackfideid": gameTags.blackFideId = value
      case "result": gameTags.result = value
      case "eco": gameTags.eco = value
      case "opening": gameTags.opening = value
      case "annotator": gameTags.annotator = value
      case "comment": gameTags.comment = value
      case "plycount": gameTags.plyCount = value
      case "timecontrol": gameTags.timeControl = value
      case "time": gameTags.time = value
      case "termination": gameTags.termination = value
      case "mode": gameTags.mode = value
      case "fen": gameTags.fen = value
      case "setup": gameTags.setUp = value
      default: gameTags.other[key] = value
      }
    }

    return gameTags
  }

  /// Result of parsing a complex comment
  private struct CommentParseResult {
    let content: String
    let length: Int
  }
  
  /// Parse complex comments that may contain nested brackets like {[%clk 01:20:52]}
  private static func parseComplexComment(from text: String, startIndex: String.Index) -> CommentParseResult? {
    guard text.hasPrefix("{") else { return nil }
    
    var braceDepth = 0
    var currentIndex = text.startIndex
    var commentContent = ""
    
    for char in text {
      if char == "{" {
        braceDepth += 1
        if braceDepth > 1 {
          commentContent.append(char)
        }
      } else if char == "}" {
        braceDepth -= 1
        if braceDepth == 0 {
          // Found the closing brace
          let length = text.distance(from: text.startIndex, to: text.index(after: currentIndex))
          return CommentParseResult(content: commentContent, length: length)
        } else {
          commentContent.append(char)
        }
      } else {
        if braceDepth > 0 {
          commentContent.append(char)
        }
      }
      
      currentIndex = text.index(after: currentIndex)
      if currentIndex >= text.endIndex {
        break
      }
    }
    
    // If we reach here, we didn't find a matching closing brace
    return nil
  }

  /// Merges multiple comment strings into a single PositionComment.
  /// This handles the case where a move may have multiple comment blocks.
  /// For each comment type (text, time, visual), only the first occurrence is kept.
  private static func mergeComments(_ comments: [String]) -> Move.PositionComment {
    if comments.isEmpty {
      return Move.PositionComment()
    }
    
    var finalText: String = ""
    var finalTimeAnnotations = Move.TimeAnnotations()
    var finalVisualAnnotations = Move.VisualAnnotations()
    
    var hasText = false
    var hasRemainingTime = false
    var hasTimeSpent = false
    var hasSquareHighlights = false
    var hasArrows = false
    
    // Process each comment and extract components, keeping only the first of each type
    for comment in comments {
      let parsedComment = Move.PositionComment.parse(from: comment)
      
      // Take first non-empty text
      if !hasText && !parsedComment.text.isEmpty {
        finalText = parsedComment.text
        hasText = true
      }
      
      // Take first remaining time annotation
      if !hasRemainingTime && parsedComment.timeAnnotations.remainingTime != nil {
        finalTimeAnnotations.remainingTime = parsedComment.timeAnnotations.remainingTime
        hasRemainingTime = true
      }
      
      // Take first time spent annotation
      if !hasTimeSpent && parsedComment.timeAnnotations.timeSpent != nil {
        finalTimeAnnotations.timeSpent = parsedComment.timeAnnotations.timeSpent
        hasTimeSpent = true
      }
      
      // Take first square highlights
      if !hasSquareHighlights && !parsedComment.visualAnnotations.squareHighlights.isEmpty {
        finalVisualAnnotations.squareHighlights = parsedComment.visualAnnotations.squareHighlights
        hasSquareHighlights = true
      }
      
      // Take first arrows
      if !hasArrows && !parsedComment.visualAnnotations.arrows.isEmpty {
        finalVisualAnnotations.arrows = parsedComment.visualAnnotations.arrows
        hasArrows = true
      }
    }
    
    return Move.PositionComment(
      text: finalText,
      timeAnnotations: finalTimeAnnotations,
      visualAnnotations: finalVisualAnnotations
    )
  }
}
