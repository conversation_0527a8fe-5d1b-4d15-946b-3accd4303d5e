//
//  PGNTextGenerationTests.swift
//  ChessKitTests
//
//  Created on 2025/6/11.
//

@testable import ChessKit
import XCTest

final class PGNTextGenerationTests: XCTestCase {

   func testFutureMovesInVariation() {
       // Create a game with variations to test future() method behavior
       var game = Game()
       
       // Main line: 1. e4 e5 2. Nf3 Nc6
       let e4Index = game.make(move: Move(metaMove: MetaMove(san: "e4", position: .standard)), from: game.startingIndex)
       let e5Index = game.make(move: Move(metaMove: MetaMove(san: "e5", position: game.positions[e4Index]!)), from: e4Index)
       let nf3Index = game.make(move: Move(metaMove: MetaMove(san: "Nf3", position: game.positions[e5Index]!)), from: e5Index)
       let nc6Index = game.make(move: Move(metaMove: MetaMove(san: "Nc6", position: game.positions[nf3Index]!)), from: nf3Index)
       
       // Add a variation: 2. d4 exd4 3. c3
       let d4Index = game.make(move: Move(metaMove: MetaMove(san: "d4", position: game.positions[e5Index]!)), from: e5Index)
       let exd4Index = game.make(move: Move(metaMove: MetaMove(san: "exd4", position: game.positions[d4Index]!)), from: d4Index)
       let c3Index = game.make(move: Move(metaMove: MetaMove(san: "c3", position: game.positions[exd4Index]!)), from: exd4Index)
       
       // Test future moves from main line
       let mainLineFuture = game.moves.future(for: nf3Index)
       XCTAssertTrue(mainLineFuture.contains(nc6Index))
       
       // Test future moves from variation
       let variationFuture = game.moves.future(for: d4Index)
       XCTAssertTrue(variationFuture.contains(exd4Index))
       XCTAssertTrue(variationFuture.contains(c3Index))
       
       // Verify that variation future doesn't contain main line moves
       XCTAssertFalse(variationFuture.contains(nc6Index))
       
       // Test variation detection
       XCTAssertFalse(game.moves.isOnMainVariation(index: d4Index))
       XCTAssertFalse(game.moves.isOnMainVariation(index: exd4Index))
       XCTAssertFalse(game.moves.isOnMainVariation(index: c3Index))
       
       XCTAssertTrue(game.moves.isOnMainVariation(index: nf3Index)) // Main variation
       XCTAssertTrue(game.moves.isOnMainVariation(index: nc6Index)) // Main variation
   }
   
   func testVariationPGNGeneration() {
       // Test PGN generation for variations
       var game = Game()

       let e4Index = game.make(move: Move(metaMove: MetaMove(san: "e4", position: .standard)), from: game.startingIndex)
       let e5Index = game.make(move: Move(metaMove: MetaMove(san: "e5", position: game.positions[e4Index]!)), from: e4Index)

       // Create a variation: 2. d4 exd4 3. c3
       let d4Index = game.make(move: Move(metaMove: MetaMove(san: "d4", position: game.positions[e5Index]!)), from: e5Index)
       let exd4Index = game.make(move: Move(metaMove: MetaMove(san: "exd4", position: game.positions[d4Index]!)), from: d4Index)
       let c3Index = game.make(move: Move(metaMove: MetaMove(san: "c3", position: game.positions[exd4Index]!)), from: exd4Index)

       // Test PGN generation for the variation
       let variationPGN = game.moves.variationPGN(from: d4Index)

       // Convert to string representation
       var pgnString = ""
       for element in variationPGN {
           switch element {
           case .whiteNumber(let number):
               if !pgnString.isEmpty { pgnString += " " }
               pgnString += "\(number)."
           case .blackNumber(let number):
               if !pgnString.isEmpty { pgnString += " " }
               pgnString += "\(number)..."
           case .move(let move, _):
               if !pgnString.isEmpty && !pgnString.hasSuffix(".") && !pgnString.hasSuffix("...") {
                   pgnString += " "
               }
               pgnString += move.metaMove?.san ?? ""
           case .variationStart:
               pgnString += " ("
           case .variationEnd:
               pgnString += ")"
           }
       }
       // Should contain the variation moves
       XCTAssertTrue(pgnString.contains("d4"))
       XCTAssertTrue(pgnString.contains("exd4"))
       XCTAssertTrue(pgnString.contains("c3"))

       // Verify moves exist
       XCTAssertEqual(game.moves.getNodeMove(index: d4Index)?.metaMove?.san, "d4")
       XCTAssertEqual(game.moves.getNodeMove(index: exd4Index)?.metaMove?.san, "exd4")
       XCTAssertEqual(game.moves.getNodeMove(index: c3Index)?.metaMove?.san, "c3")
   }
}
