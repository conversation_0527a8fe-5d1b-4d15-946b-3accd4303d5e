//
//  SANParserTests.swift
//  ChessKitTests
//

@testable import ChessKit
import XCTest

final class SANParserTests: XCTestCase {

   func testCastling() {
       let p1 = Position(fen: "r3k3/8/8/8/8/8/8/4K2R w Kq - 0 1")!
       let shortCastle = SANParser.parse(metaMove: "O-O", in: p1)
       XCTAssertEqual(shortCastle?.result, .castle(.wK))

       let p2 = Position(fen: "r3k3/8/8/8/8/8/8/5RK1 b q - 0 1")!
       let longCastle = SANParser.parse(metaMove: "O-O-O", in: p2)
       XCTAssertEqual(longCastle?.result, .castle(.bQ))
   }

   func testPromotion() {
       let p = Position(fen: "8/P7/8/6k1/8/8/8/4K3 w - - 0 1")!
       let promotion = SANParser.parse(metaMove: "a8=Q", in: p)

       let promotedPiece = Piece(.queen, color: .white, square: .a8)
       XCTAssertEqual(promotion?.promotedPiece, promotedPiece)
   }

   func testChecksAndMates() {
       let p1 = Position(fen: "8/k7/7Q/6R1/8/8/8/4K3 w - - 0 1")!

       let check = SANParser.parse(metaMove: "Rg7+", in: p1)
       XCTAssertEqual(check?.checkState, .check)

       let p2 = Position(fen: "8/k5R1/7Q/8/8/8/8/4K3 b - - 0 1")!

       let kingMove = SANParser.parse(metaMove: "Ka8", in: p2)
       XCTAssertEqual(kingMove?.checkState, MetaMove.CheckState.none)

       let p3 = Position(fen: "k7/6R1/7Q/8/8/8/8/4K3 w - - 0 1")!

       let checkmate = SANParser.parse(metaMove: "Qh8#", in: p3)
       XCTAssertEqual(checkmate?.checkState, .checkmate)
   }

   func testDisambiguation() {
       let pw = Position(fen: "2kr3r/8/8/R7/4Q2Q/8/8/R1K4Q w - - 0 1")!
       let pb = Position(fen: "2kr3r/8/8/R7/4Q2Q/8/8/R1K4Q b - - 0 1")!

       let rookFileMove = SANParser.parse(metaMove: "R1a3", in: pw)
       XCTAssertEqual(rookFileMove?.result, .move)
       XCTAssertEqual(rookFileMove?.piece.kind, .rook)
       XCTAssertEqual(rookFileMove?.disambiguation, MetaMove.Disambiguation.byRank(1))
       XCTAssertEqual(rookFileMove?.start, .a1)
       XCTAssertEqual(rookFileMove?.end, .a3)
       XCTAssertEqual(rookFileMove?.promotedPiece, nil)
       XCTAssertEqual(rookFileMove?.checkState, MetaMove.CheckState.none)

       let rookRankMove = SANParser.parse(metaMove: "Rdf8", in: pb)
       XCTAssertEqual(rookRankMove?.result, .move)
       XCTAssertEqual(rookRankMove?.piece.kind, .rook)
       XCTAssertEqual(rookRankMove?.disambiguation, MetaMove.Disambiguation.byFile(.d))
       XCTAssertEqual(rookRankMove?.start, .d8)
       XCTAssertEqual(rookRankMove?.end, .f8)
       XCTAssertEqual(rookRankMove?.promotedPiece, nil)
       XCTAssertEqual(rookRankMove?.checkState, MetaMove.CheckState.none)

       let queenMove = SANParser.parse(metaMove: "Qh4e1", in: pw)
       XCTAssertEqual(queenMove?.result, .move)
       XCTAssertEqual(queenMove?.piece.kind, .queen)
       XCTAssertEqual(queenMove?.disambiguation, MetaMove.Disambiguation.bySquare(.h4))
       XCTAssertEqual(queenMove?.start, .h4)
       XCTAssertEqual(queenMove?.end, .e1)
       XCTAssertEqual(queenMove?.promotedPiece, nil)
       XCTAssertEqual(queenMove?.checkState, MetaMove.CheckState.none)
   }

   func testValidSANButInvalidMove() {
       XCTAssertNil(SANParser.parse(metaMove: "axb5", in: .standard))
       XCTAssertNil(SANParser.parse(metaMove: "Bb5", in: .standard))
   }

   func testInvalidSAN() {
       XCTAssertNil(SANParser.parse(metaMove: "bad move", in: .standard))
   }

}
