//
//  UndoTests.swift
//  ChessKitTests
//
//  Created by AI Assistant on 2025-07-20.
//

import XCTest
@testable import ChessKit

final class UndoTests: XCTestCase {
    
    var game: Game!
    
    override func setUp() {
        super.setUp()
        game = Game()
    }
    
    override func tearDown() {
        game = nil
        super.tearDown()
    }
    
    // MARK: - MoveTree Add Operation Tests
    
    func testMoveTreeAddWithUndo() {
        var moveTree = MoveTree()
        let move = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))
        
        // Test add with undo
        let (newIndex, undoState) = moveTree.addWithUndo(move: move, toParentIndex: MoveTree.minimumIndex)
        
        XCTAssertNotEqual(newIndex, MoveTree.minimumIndex)
        XCTAssertEqual(undoState.parentIndex, MoveTree.minimumIndex)
        XCTAssertNotNil(moveTree.dictionary[newIndex])
        
        // Verify we can find the new move using the undo state's method
        XCTAssertNotNil(undoState.newMoveIndexToRemove(in: moveTree))
        XCTAssertEqual(undoState.resolvedParentIndex(in: moveTree), MoveTree.minimumIndex)
        
        // Test undo
        let undoSuccess = moveTree.undoAdd(undoState)
        XCTAssertTrue(undoSuccess)
        XCTAssertNil(moveTree.dictionary[newIndex])
        XCTAssertNil(moveTree.headNode.next)
    }
    
    func testMoveTreeAddMultipleMovesWithUndo() {
        var moveTree = MoveTree()
        
        let move1 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))
        
        let move2 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))
        
        // Add first move
        let (index1, undoState1) = moveTree.addWithUndo(move: move1, toParentIndex: MoveTree.minimumIndex)
        
        // Add second move
        let (index2, undoState2) = moveTree.addWithUndo(move: move2, toParentIndex: index1)
        
        XCTAssertNotEqual(index1, index2)
        XCTAssertEqual(moveTree.headNode.next?.index, index1)
        XCTAssertEqual(moveTree.dictionary[index1]?.next?.index, index2)
        
        // Undo second move
        XCTAssertTrue(moveTree.undoAdd(undoState2))
        XCTAssertNil(moveTree.dictionary[index2])
        XCTAssertNil(moveTree.dictionary[index1]?.next)
        
        // Undo first move
        XCTAssertTrue(moveTree.undoAdd(undoState1))
        XCTAssertNil(moveTree.dictionary[index1])
        XCTAssertNil(moveTree.headNode.next)
    }
    
    // MARK: - MoveTree Delete Operation Tests
    
    func testMoveTreeDeleteWithUndo() {
        var moveTree = MoveTree()
        
        // Add some moves first
        let move1 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))
        
        let move2 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))
        
        let index1 = moveTree.add(move: move1, toParentIndex: MoveTree.minimumIndex)
        let index2 = moveTree.add(move: move2, toParentIndex: index1)
        
        // Test delete with undo
        let (success, undoState) = moveTree.deleteWithUndo(at: index1)
        
        XCTAssertTrue(success)
        XCTAssertNotNil(undoState)
        XCTAssertNil(moveTree.dictionary[index1])
        XCTAssertNil(moveTree.dictionary[index2])
        XCTAssertNil(moveTree.headNode.next)
        
        // Test undo delete
        let undoSuccess = moveTree.undoDelete(undoState!)
        XCTAssertTrue(undoSuccess)
        XCTAssertNotNil(moveTree.dictionary[index1])
        XCTAssertNotNil(moveTree.dictionary[index2])
        XCTAssertEqual(moveTree.headNode.next?.index, index1)
        XCTAssertEqual(moveTree.dictionary[index1]?.next?.index, index2)
    }
    
    // MARK: - MoveTree Promote Operation Tests
    
    func testMoveTreePromoteWithUndo() {
        var moveTree = MoveTree()
        
        // Create a main line: 1. e4 e5 2. Nf3
        let e4 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))
        
        let e5 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))
        
        let nf3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .g1),
            start: .g1,
            end: .f3
        ))
        
        // Create a variation: 2. Nc3 instead of 2. Nf3
        let nc3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .b1),
            start: .b1,
            end: .c3
        ))
        
        let index1 = moveTree.add(move: e4, toParentIndex: MoveTree.minimumIndex)
        let index2 = moveTree.add(move: e5, toParentIndex: index1)
        let index3 = moveTree.add(move: nf3, toParentIndex: index2) // Main line
        let index4 = moveTree.add(move: nc3, toParentIndex: index2) // Variation
        
        // Verify initial state: Nf3 is main, Nc3 is variation
        XCTAssertEqual(moveTree.dictionary[index2]?.next?.index, index3)
        XCTAssertTrue(moveTree.dictionary[index2]?.children.contains { $0.index == index4 } ?? false)
        
        // Test promote with undo
        let (success, undoState) = moveTree.promoteWithUndo(index: index4)
        
        XCTAssertTrue(success)
        XCTAssertNotNil(undoState)
        
        // Verify promotion: Nc3 should now be main, Nf3 should be variation
        XCTAssertEqual(moveTree.dictionary[index2]?.next?.index, index4)
        XCTAssertTrue(moveTree.dictionary[index2]?.children.contains { $0.index == index3 } ?? false)
        
        // Test undo promote
        let undoSuccess = moveTree.undoPromote(undoState!)
        XCTAssertTrue(undoSuccess)
        
        // Verify back to original state
        XCTAssertEqual(moveTree.dictionary[index2]?.next?.index, index3)
        XCTAssertTrue(moveTree.dictionary[index2]?.children.contains { $0.index == index4 } ?? false)
    }
    
    // MARK: - MoveTree Edit Move Tests
    
    func testMoveTreeEditMoveWithUndo() {
        var moveTree = MoveTree()
        
        let originalMove = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))
        
        let newMove = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .d2),
            start: .d2,
            end: .d4
        ))
        
        let index = moveTree.add(move: originalMove, toParentIndex: MoveTree.minimumIndex)
        
        // Test edit with undo
        let undoState = moveTree.setNodeMoveWithUndo(index: index, newMove: newMove)
        
        XCTAssertNotNil(undoState)
        XCTAssertEqual(undoState?.moveIndex, index)
        XCTAssertEqual(moveTree.getNodeMove(index: index)?.metaMove?.start, .d2)
        XCTAssertEqual(moveTree.getNodeMove(index: index)?.metaMove?.end, .d4)
        
        // Test undo edit
        let undoSuccess = moveTree.undoEditMove(undoState!)
        XCTAssertTrue(undoSuccess)
        XCTAssertEqual(moveTree.getNodeMove(index: index)?.metaMove?.start, .e2)
        XCTAssertEqual(moveTree.getNodeMove(index: index)?.metaMove?.end, .e4)
    }

    // MARK: - Game Layer Tests

    func testGameMakeWithUndo() {
        let move = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))

        let originalPositionCount = game.positions.count

        // Test make with undo
        let (newIndex, undoState) = game.makeWithUndo(move: move, from: game.startingIndex)

        XCTAssertNotEqual(newIndex, game.startingIndex)
        XCTAssertNotNil(undoState)
        XCTAssertEqual(game.positions.count, originalPositionCount + 1)
        XCTAssertNotNil(game.positions[newIndex])

        // Test undo make
        let undoSuccess = game.undoMake(undoState!)
        XCTAssertTrue(undoSuccess)
        XCTAssertEqual(game.positions.count, originalPositionCount)
        XCTAssertNil(game.positions[newIndex])
    }

    func testGameDeleteWithUndo() {
        // First add some moves
        let move1 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))

        let move2 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))

        let index1 = game.make(move: move1, from: game.startingIndex)
        let index2 = game.make(move: move2, from: index1)

        let originalPositionCount = game.positions.count

        // Test delete with undo
        let (success, undoState) = game.deleteWithUndo(at: index1)

        XCTAssertTrue(success)
        XCTAssertNotNil(undoState)
        XCTAssertNil(game.positions[index1])
        XCTAssertNil(game.positions[index2])
        XCTAssertEqual(game.positions.count, originalPositionCount - 2)

        // Test undo delete
        let undoSuccess = game.undoDelete(undoState!)
        XCTAssertTrue(undoSuccess)
        XCTAssertNotNil(game.positions[index1])
        XCTAssertNotNil(game.positions[index2])
        XCTAssertEqual(game.positions.count, originalPositionCount)
    }

    func testGamePromoteWithUndo() {
        // Create a game with variations
        let e4 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))

        let e5 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))

        let nf3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .g1),
            start: .g1,
            end: .f3
        ))

        let nc3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .b1),
            start: .b1,
            end: .c3
        ))

        let index1 = game.make(move: e4, from: game.startingIndex)
        let index2 = game.make(move: e5, from: index1)
        let index3 = game.make(move: nf3, from: index2) // Main line
        let index4 = game.make(move: nc3, from: index2) // Variation

        // Test promote with undo
        let (success, undoState) = game.promoteWithUndo(index: index4)

        XCTAssertTrue(success)
        XCTAssertNotNil(undoState)

        // Verify promotion
        XCTAssertEqual(game.moves.dictionary[index2]?.next?.index, index4)

        // Test undo promote
        let undoSuccess = game.undoPromote(undoState!)
        XCTAssertTrue(undoSuccess)

        // Verify back to original state
        XCTAssertEqual(game.moves.dictionary[index2]?.next?.index, index3)
    }

    func testGameOverwriteWithUndo() {
        // First add some moves
        let move1 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))

        let move2 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))

        let newMove = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .d7),
            start: .d7,
            end: .d5
        ))

        let index1 = game.make(move: move1, from: game.startingIndex)
        let index2 = game.make(move: move2, from: index1)

        let originalPositionCount = game.positions.count

        // Test overwrite with undo
        let (newIndex, undoState) = game.overwriteWithUndo(move: newMove, from: index1)

        XCTAssertNotEqual(newIndex, index2)
        XCTAssertNotNil(undoState)
        XCTAssertNil(game.positions[index2]) // Old move should be deleted
        XCTAssertNotNil(game.positions[newIndex]) // New move should exist
        XCTAssertEqual(game.positions.count, originalPositionCount) // Same count (replaced)

        // Test undo overwrite
        let undoSuccess = game.undoOverwrite(undoState!)
        XCTAssertTrue(undoSuccess)
        XCTAssertNotNil(game.positions[index2]) // Old move restored
        XCTAssertNil(game.positions[newIndex]) // New move removed
    }

    func testGameDeleteBeforeMoveWithUndo() {
        // Create a game: 1. e4 e5 2. Nf3 Nc6 3. Bb5
        let e4 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))

        let e5 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))

        let nf3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .g1),
            start: .g1,
            end: .f3
        ))

        let nc6 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .black, square: .b8),
            start: .b8,
            end: .c6
        ))

        let bb5 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.bishop, color: .white, square: .f1),
            start: .f1,
            end: .b5
        ))

        let index1 = game.make(move: e4, from: game.startingIndex)
        let index2 = game.make(move: e5, from: index1)
        let index3 = game.make(move: nf3, from: index2)
        let index4 = game.make(move: nc6, from: index3)
        let index5 = game.make(move: bb5, from: index4)

        let originalStartingPosition = game.positions[game.startingIndex]!
        let originalPositionCount = game.positions.count

        // Test delete before move (delete everything before Bb5)
        let (success, undoState) = game.deleteBeforeMoveWithUndo(at: index5)

        XCTAssertTrue(success)
        XCTAssertNotNil(undoState)

        // Verify positions before index5 are deleted
        XCTAssertNil(game.positions[index1])
        XCTAssertNil(game.positions[index2])
        XCTAssertNil(game.positions[index3])
        XCTAssertNil(game.positions[index4])
        XCTAssertNotNil(game.positions[index5])

        // Verify starting position changed
        XCTAssertNotEqual(game.positions[game.startingIndex]!, originalStartingPosition)
        XCTAssertEqual(game.tags.setUp, "1")

        // Test undo delete before move
        let undoSuccess = game.undoDeleteBeforeMove(undoState!)
        XCTAssertTrue(undoSuccess)

        // Verify all positions restored
        XCTAssertNotNil(game.positions[index1])
        XCTAssertNotNil(game.positions[index2])
        XCTAssertNotNil(game.positions[index3])
        XCTAssertNotNil(game.positions[index4])
        XCTAssertNotNil(game.positions[index5])
        XCTAssertEqual(game.positions.count, originalPositionCount)

        // Verify starting position restored
        XCTAssertEqual(game.positions[game.startingIndex]!, originalStartingPosition)
    }

    func testGamePromoteToMainVariationWithUndo() {
        // Create a complex variation structure
        // 1. e4 e5 (2. Nc3 (2... f5 3. exf5) Nf6) 2. Nf3 Nc6

        let e4 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))

        let e5 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))

        let nf3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .g1),
            start: .g1,
            end: .f3
        ))

        let nc3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .b1),
            start: .b1,
            end: .c3
        ))

        let f5 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .f7),
            start: .f7,
            end: .f5
        ))

        let exf5 = Move(metaMove: MetaMove(
            result: .capture(Piece(.pawn, color: .black, square: .f5)),
            piece: Piece(.pawn, color: .white, square: .e4),
            start: .e4,
            end: .f5
        ))

        let nf6 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .black, square: .g8),
            start: .g8,
            end: .f6
        ))

        let nc6 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .black, square: .b8),
            start: .b8,
            end: .c6
        ))

        // Build the tree
        let index1 = game.make(move: e4, from: game.startingIndex)  // 1. e4
        let index2 = game.make(move: e5, from: index1)              // 1... e5
        let index3 = game.make(move: nf3, from: index2)             // 2. Nf3 (main)
        let index4 = game.make(move: nc3, from: index2)             // 2. Nc3 (variation)
        let index5 = game.make(move: f5, from: index4)              // 2... f5
        let index6 = game.make(move: exf5, from: index5)            // 3. exf5
        let _ = game.make(move: nf6, from: index4)             // 2... Nf6 (variation)
        let _ = game.make(move: nc6, from: index3)             // 2... Nc6

        // Verify initial state: Nf3 is main line
        XCTAssertEqual(game.moves.dictionary[index2]?.next?.index, index3)
        XCTAssertTrue(game.moves.isOnMainVariation(index: index3))
        XCTAssertFalse(game.moves.isOnMainVariation(index: index6))

        // Test promote to main variation (promote the deeply nested exf5)
        let (success, undoState) = game.promoteToMainVariationWithUndo(index: index6)

        XCTAssertTrue(success)
        XCTAssertNotNil(undoState)

        // Verify exf5 is now on main variation
        XCTAssertTrue(game.moves.isOnMainVariation(index: index6))

        // Test undo promote to main
        let undoSuccess = game.undoPromoteToMain(undoState!)
        XCTAssertTrue(undoSuccess)

        // Verify back to original state
        XCTAssertEqual(game.moves.dictionary[index2]?.next?.index, index3)
        XCTAssertTrue(game.moves.isOnMainVariation(index: index3))
        XCTAssertFalse(game.moves.isOnMainVariation(index: index6))
    }

    func testGameEditMoveWithUndo() {
        let originalMove = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))

        let newMove = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .d2),
            start: .d2,
            end: .d4
        ))

        let index = game.make(move: originalMove, from: game.startingIndex)

        // Test edit with undo
        let undoState = game.editMoveWithUndo(index: index, newMove: newMove)

        XCTAssertNotNil(undoState)
        XCTAssertEqual(game.moves.getNodeMove(index: index)?.metaMove?.start, .d2)
        XCTAssertEqual(game.moves.getNodeMove(index: index)?.metaMove?.end, .d4)

        // Test undo edit
        let undoSuccess = game.undoEditMove(undoState!)
        XCTAssertTrue(undoSuccess)
        XCTAssertEqual(game.moves.getNodeMove(index: index)?.metaMove?.start, .e2)
        XCTAssertEqual(game.moves.getNodeMove(index: index)?.metaMove?.end, .e4)
    }
    
    // MARK: - Consecutive Undo Tests
    
    func testMoveTreeConsecutiveUndo() {
        var moveTree = MoveTree()
        
        let move1 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))
        
        let move2 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))
        
        let move3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .g1),
            start: .g1,
            end: .f3
        ))
        
        // Add three moves
        let (index1, undoState1) = moveTree.addWithUndo(move: move1, toParentIndex: MoveTree.minimumIndex)
        let (index2, undoState2) = moveTree.addWithUndo(move: move2, toParentIndex: index1)
        let (index3, undoState3) = moveTree.addWithUndo(move: move3, toParentIndex: index2)
        
        // Verify all moves exist
        XCTAssertNotNil(moveTree.dictionary[index1])
        XCTAssertNotNil(moveTree.dictionary[index2])
        XCTAssertNotNil(moveTree.dictionary[index3])
        
        // First undo - remove third move
        XCTAssertTrue(moveTree.undoAdd(undoState3))
        XCTAssertNotNil(moveTree.dictionary[index1])
        XCTAssertNotNil(moveTree.dictionary[index2])
        XCTAssertNil(moveTree.dictionary[index3])
        
        // Second consecutive undo - remove second move
        XCTAssertTrue(moveTree.undoAdd(undoState2))
        XCTAssertNotNil(moveTree.dictionary[index1])
        XCTAssertNil(moveTree.dictionary[index2])
        XCTAssertNil(moveTree.dictionary[index3])
        
        // Third consecutive undo - remove first move
        XCTAssertTrue(moveTree.undoAdd(undoState1))
        XCTAssertNil(moveTree.dictionary[index1])
        XCTAssertNil(moveTree.dictionary[index2])
        XCTAssertNil(moveTree.dictionary[index3])
        XCTAssertNil(moveTree.headNode.next)
    }
    
    func testMoveTreeConsecutiveUndoWithVariations() {
        var moveTree = MoveTree()
        
        let e4 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))
        
        let e5 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))
        
        let nf3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .g1),
            start: .g1,
            end: .f3
        ))
        
        let nc3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .b1),
            start: .b1,
            end: .c3
        ))
        
        // Build tree with variations
        let index1 = moveTree.add(move: e4, toParentIndex: MoveTree.minimumIndex)
        let index2 = moveTree.add(move: e5, toParentIndex: index1)
        let index3 = moveTree.add(move: nf3, toParentIndex: index2) // Main line
        let (index4, _) = moveTree.addWithUndo(move: nc3, toParentIndex: index2) // Variation
        
        // Verify initial state
        XCTAssertEqual(moveTree.dictionary[index2]?.next?.index, index3)
        XCTAssertTrue(moveTree.dictionary[index2]?.children.contains { $0.index == index4 } ?? false)
        
        // Delete variation first (different order to test the problem)
        let (success2, undoState2) = moveTree.deleteWithUndo(at: index4)
        XCTAssertTrue(success2)
        
        // Verify variation deleted, main line remains
        XCTAssertNotNil(moveTree.dictionary[index3])
        XCTAssertNil(moveTree.dictionary[index4])
        XCTAssertEqual(moveTree.dictionary[index2]?.next?.index, index3)
        XCTAssertTrue(moveTree.dictionary[index2]?.children.isEmpty ?? true)
        
        // Delete main line with undo
        let (success1, undoState1) = moveTree.deleteWithUndo(at: index3)
        XCTAssertTrue(success1)
        
        // Verify both moves are deleted
        XCTAssertNil(moveTree.dictionary[index3])
        XCTAssertNil(moveTree.dictionary[index4])
        XCTAssertNil(moveTree.dictionary[index2]?.next)
        XCTAssertTrue(moveTree.dictionary[index2]?.children.isEmpty ?? true)
        
        // Consecutive undo - restore main line first
        XCTAssertTrue(moveTree.undoDelete(undoState1!))
        XCTAssertNotNil(moveTree.dictionary[index3])
        XCTAssertNil(moveTree.dictionary[index4])
        XCTAssertEqual(moveTree.dictionary[index2]?.next?.index, index3)
        XCTAssertTrue(moveTree.dictionary[index2]?.children.isEmpty ?? true)
        
        // Second consecutive undo - restore variation
        XCTAssertTrue(moveTree.undoDelete(undoState2!))
        XCTAssertNotNil(moveTree.dictionary[index3])
        XCTAssertNotNil(moveTree.dictionary[index4])
        XCTAssertEqual(moveTree.dictionary[index2]?.next?.index, index3)
        XCTAssertTrue(moveTree.dictionary[index2]?.children.contains { $0.index == index4 } ?? false)
    }
    
    // MARK: - Consecutive Redo Tests
    
    func testMoveTreeConsecutiveRedo() {
        var moveTree = MoveTree()
        
        let move1 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))
        
        let move2 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))
        
        let move3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .g1),
            start: .g1,
            end: .f3
        ))
        
        // Add three moves and then undo them all
        let (index1, undoState1) = moveTree.addWithUndo(move: move1, toParentIndex: MoveTree.minimumIndex)
        let (index2, undoState2) = moveTree.addWithUndo(move: move2, toParentIndex: index1)
        let (index3, undoState3) = moveTree.addWithUndo(move: move3, toParentIndex: index2)
        
        // Undo all three moves
        XCTAssertTrue(moveTree.undoAdd(undoState3))
        XCTAssertTrue(moveTree.undoAdd(undoState2))
        XCTAssertTrue(moveTree.undoAdd(undoState1))
        
        // Verify all undone
        XCTAssertNil(moveTree.dictionary[index1])
        XCTAssertNil(moveTree.dictionary[index2])
        XCTAssertNil(moveTree.dictionary[index3])
        
        // First redo - add first move back
        let (newIndex1, _) = moveTree.addWithUndo(move: move1, toParentIndex: MoveTree.minimumIndex)
        XCTAssertNotNil(moveTree.dictionary[newIndex1])
        XCTAssertNil(moveTree.dictionary[newIndex1]?.next)
        
        // Second consecutive redo - add second move back
        let (newIndex2, _) = moveTree.addWithUndo(move: move2, toParentIndex: newIndex1)
        XCTAssertNotNil(moveTree.dictionary[newIndex2])
        XCTAssertEqual(moveTree.dictionary[newIndex1]?.next?.index, newIndex2)
        
        // Third consecutive redo - add third move back
        let (newIndex3, _) = moveTree.addWithUndo(move: move3, toParentIndex: newIndex2)
        XCTAssertNotNil(moveTree.dictionary[newIndex3])
        XCTAssertEqual(moveTree.dictionary[newIndex2]?.next?.index, newIndex3)
        
        // Verify structure is correct
        XCTAssertEqual(moveTree.headNode.next?.index, newIndex1)
        XCTAssertEqual(moveTree.dictionary[newIndex1]?.next?.index, newIndex2)
        XCTAssertEqual(moveTree.dictionary[newIndex2]?.next?.index, newIndex3)
    }
    
    func testMoveTreeConsecutiveRedoWithPromote() {
        var moveTree = MoveTree()
        
        let e4 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))
        
        let e5 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))
        
        let nf3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .g1),
            start: .g1,
            end: .f3
        ))
        
        let nc3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .b1),
            start: .b1,
            end: .c3
        ))
        
        // Build tree with variations
        let index1 = moveTree.add(move: e4, toParentIndex: MoveTree.minimumIndex)
        let index2 = moveTree.add(move: e5, toParentIndex: index1)
        let index3 = moveTree.add(move: nf3, toParentIndex: index2) // Main line
        let index4 = moveTree.add(move: nc3, toParentIndex: index2) // Variation
        
        // Promote variation and undo twice
        let (success1, undoState1) = moveTree.promoteWithUndo(index: index4)
        XCTAssertTrue(success1)
        let (success2, undoState2) = moveTree.promoteWithUndo(index: index3)
        XCTAssertTrue(success2)
        
        // Undo both promotions
        XCTAssertTrue(moveTree.undoPromote(undoState2!))
        XCTAssertTrue(moveTree.undoPromote(undoState1!))
        
        // Verify back to original state
        XCTAssertEqual(moveTree.dictionary[index2]?.next?.index, index3)
        
        // Consecutive redo - promote again
        let (redoSuccess1, _) = moveTree.promoteWithUndo(index: index4)
        XCTAssertTrue(redoSuccess1)
        XCTAssertEqual(moveTree.dictionary[index2]?.next?.index, index4)
        
        let (redoSuccess2, _) = moveTree.promoteWithUndo(index: index3)
        XCTAssertTrue(redoSuccess2)
        XCTAssertEqual(moveTree.dictionary[index2]?.next?.index, index3)
    }
    
    // MARK: - Game Layer Consecutive Undo/Redo Tests
    
    func testGameConsecutiveUndo() {
        let move1 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))
        
        let move2 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))
        
        let move3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .g1),
            start: .g1,
            end: .f3
        ))
        
        // Add three moves
        let (index1, undoState1) = game.makeWithUndo(move: move1, from: game.startingIndex)
        let (index2, undoState2) = game.makeWithUndo(move: move2, from: index1)
        let (index3, undoState3) = game.makeWithUndo(move: move3, from: index2)
        
        let originalCount = game.positions.count
        
        // Verify all positions exist
        XCTAssertNotNil(game.positions[index1])
        XCTAssertNotNil(game.positions[index2])
        XCTAssertNotNil(game.positions[index3])
        
        // First consecutive undo
        XCTAssertTrue(game.undoMake(undoState3!))
        XCTAssertNotNil(game.positions[index1])
        XCTAssertNotNil(game.positions[index2])
        XCTAssertNil(game.positions[index3])
        XCTAssertEqual(game.positions.count, originalCount - 1)
        
        // Second consecutive undo
        XCTAssertTrue(game.undoMake(undoState2!))
        XCTAssertNotNil(game.positions[index1])
        XCTAssertNil(game.positions[index2])
        XCTAssertNil(game.positions[index3])
        XCTAssertEqual(game.positions.count, originalCount - 2)
        
        // Third consecutive undo
        XCTAssertTrue(game.undoMake(undoState1!))
        XCTAssertNil(game.positions[index1])
        XCTAssertNil(game.positions[index2])
        XCTAssertNil(game.positions[index3])
        XCTAssertEqual(game.positions.count, originalCount - 3)
    }
    
    func testGameConsecutiveRedo() {
        let move1 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))
        
        let move2 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))
        
        let move3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .g1),
            start: .g1,
            end: .f3
        ))
        
        // Add three moves and undo them all
        let (index1, undoState1) = game.makeWithUndo(move: move1, from: game.startingIndex)
        let (index2, undoState2) = game.makeWithUndo(move: move2, from: index1)
        let (_, undoState3) = game.makeWithUndo(move: move3, from: index2)
        
        XCTAssertTrue(game.undoMake(undoState3!))
        XCTAssertTrue(game.undoMake(undoState2!))
        XCTAssertTrue(game.undoMake(undoState1!))
        
        let baseCount = game.positions.count
        
        // Consecutive redo using makeWithUndo
        let (newIndex1, _) = game.makeWithUndo(move: move1, from: game.startingIndex)
        XCTAssertNotNil(game.positions[newIndex1])
        XCTAssertEqual(game.positions.count, baseCount + 1)
        
        let (newIndex2, _) = game.makeWithUndo(move: move2, from: newIndex1)
        XCTAssertNotNil(game.positions[newIndex2])
        XCTAssertEqual(game.positions.count, baseCount + 2)
        
        let (newIndex3, _) = game.makeWithUndo(move: move3, from: newIndex2)
        XCTAssertNotNil(game.positions[newIndex3])
        XCTAssertEqual(game.positions.count, baseCount + 3)
        
        // Verify move tree structure
        XCTAssertEqual(game.moves.headNode.next?.index, newIndex1)
        XCTAssertEqual(game.moves.dictionary[newIndex1]?.next?.index, newIndex2)
        XCTAssertEqual(game.moves.dictionary[newIndex2]?.next?.index, newIndex3)
    }
    
    // MARK: - Mixed Consecutive Undo/Redo Scenarios
    
    func testMixedConsecutiveUndoRedo() {
        var moveTree = MoveTree()
        
        let move1 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))
        
        let move2 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))
        
        let move3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .g1),
            start: .g1,
            end: .f3
        ))
        
        // Add moves
        let (index1, _) = moveTree.addWithUndo(move: move1, toParentIndex: MoveTree.minimumIndex)
        let (index2, undoState2) = moveTree.addWithUndo(move: move2, toParentIndex: index1)
        let (index3, undoState3) = moveTree.addWithUndo(move: move3, toParentIndex: index2)
        
        // Undo two moves
        XCTAssertTrue(moveTree.undoAdd(undoState3))
        XCTAssertTrue(moveTree.undoAdd(undoState2))
        
        // Redo one move
        let (newIndex2, newUndo2) = moveTree.addWithUndo(move: move2, toParentIndex: index1)
        XCTAssertNotNil(moveTree.dictionary[newIndex2])
        
        // Undo again
        XCTAssertTrue(moveTree.undoAdd(newUndo2))
        
        // Redo with different move
        let altMove = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .d7),
            start: .d7,
            end: .d5
        ))
        
        let (altIndex, _) = moveTree.addWithUndo(move: altMove, toParentIndex: index1)
        XCTAssertNotNil(moveTree.dictionary[altIndex])
        XCTAssertEqual(moveTree.dictionary[index1]?.next?.index, altIndex)
        
        // Verify original index2 and index3 are still gone
        XCTAssertNil(moveTree.dictionary[index2])
        XCTAssertNil(moveTree.dictionary[index3])
    }
    
    func testComplexUndoRedoWithPathResolution() {
        var moveTree = MoveTree()
        
        // Create a complex tree structure
        let e4 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .e2),
            start: .e2,
            end: .e4
        ))
        
        let e5 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .black, square: .e7),
            start: .e7,
            end: .e5
        ))
        
        let nf3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .g1),
            start: .g1,
            end: .f3
        ))
        
        let nc3 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .white, square: .b1),
            start: .b1,
            end: .c3
        ))
        
        let nf6 = Move(metaMove: MetaMove(
            result: .move,
            piece: Piece(.knight, color: .black, square: .g8),
            start: .g8,
            end: .f6
        ))
        
        // Build tree: 1. e4 e5 (2. Nf3 Nf6) 2. Nc3
        let index1 = moveTree.add(move: e4, toParentIndex: MoveTree.minimumIndex)
        let index2 = moveTree.add(move: e5, toParentIndex: index1)
        let index3 = moveTree.add(move: nf3, toParentIndex: index2) // Main
        let index4 = moveTree.add(move: nc3, toParentIndex: index2) // Variation
        let index5 = moveTree.add(move: nf6, toParentIndex: index3) // Continue main
        
        // Multiple operations that could invalidate indices
        let (promoteSuccess1, promoteUndo1) = moveTree.promoteWithUndo(index: index4) // Promote Nc3
        XCTAssertTrue(promoteSuccess1)
        
        let (deleteSuccess, deleteUndo) = moveTree.deleteWithUndo(at: index3) // Delete Nf3 (now variation)
        XCTAssertTrue(deleteSuccess)
        
        let (promoteSuccess2, _) = moveTree.promoteWithUndo(index: index3) // This should fail
        XCTAssertFalse(promoteSuccess2)
        
        // Consecutive undo operations that rely on path resolution
        XCTAssertTrue(moveTree.undoDelete(deleteUndo!))
        XCTAssertTrue(moveTree.undoPromote(promoteUndo1!))
        
        // Verify structure is restored
        XCTAssertEqual(moveTree.dictionary[index2]?.next?.index, index3)
        XCTAssertTrue(moveTree.dictionary[index2]?.children.contains { $0.index == index4 } ?? false)
        XCTAssertEqual(moveTree.dictionary[index3]?.next?.index, index5)
    }
}
