//
//  MockBoardDelegate.swift
//  ChessKit
//

@testable import ChessKit

final class MockBoardDelegate: BoardDelegate {
  private let didPromote: (@Sendable (Move) -> Void)?
  private let didCheckKing: (@Sendable (Piece.Color) -> Void)?
  private let didEnd: (@Sendable (Board.EndResult) -> Void)?

  init(
    didPromote: (@Sendable (Move) -> Void)? = nil,
    didCheckKing: (@Sendable (Piece.Color) -> Void)? = nil,
    didEnd: (@Sendable (Board.EndResult) -> Void)? = nil
  ) {
    self.didPromote = didPromote
    self.didCheckKing = didCheckKing
    self.didEnd = didEnd
  }

  func willPromote(with move: Move) {
    // This is a mock, so we don't need to do anything here.
  }

  func didPromote(with move: Move) {
    didPromote?(move)
  }

  func didCheckKing(ofColor color: Piece.Color) {
    didCheckKing?(color)
  }

  func didEnd(with result: Board.EndResult) {
    didEnd?(result)
  }
}
