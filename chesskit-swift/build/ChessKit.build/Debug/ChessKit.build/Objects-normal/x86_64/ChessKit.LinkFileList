/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/Attacks.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/Bitboard.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/PieceSet.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/Square+BB.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/Board.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/Clock.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/Configuration.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/Game.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/Move.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/MoveTree.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/EngineLANParser+Regex.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/EngineLANParser.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/FENParser.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/FENValidator.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/PGNParser+Regex.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/PGNParser.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/SANParser+Regex.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/SANParser.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/Piece.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/Position.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/Castling.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/EnPassant.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/Square.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/UndoState.o
/Users/<USER>/Projects/MacChessBase/chesskit-swift/build/ChessKit.build/Debug/ChessKit.build/Objects-normal/x86_64/Comparable+Bounded.o
