# MacChessBase 多棋谱保存功能技术规划

## 概述

本文档详细规划MacChessBase的多棋谱保存功能，支持复杂的多游戏文件管理、智能保存策略选择，以及增量文件更新机制。

## 功能需求分析

### 核心需求
1. **多棋谱同时保存**: 支持将棋谱队列中的所有对局批量保存
2. **智能保存策略**: 根据文件来源选择不同的保存策略
3. **增量文件更新**: 对于多棋谱文件，支持只更新被修改的游戏部分
4. **来源追踪**: 记录每个游戏的原始文件来源和位置信息
5. **用户选择机制**: 提供灵活的保存选项供用户选择

### 复杂场景处理
1. **新建棋局**: 完全新创建的游戏，无原始文件来源
2. **单棋谱文件导入**: 从单个PGN文件导入的游戏
3. **多棋谱文件导入**: 从包含多个游戏的PGN文件导入的游戏
4. **混合来源**: 棋谱队列包含来自不同文件的游戏
5. **文件夹批量导入**: 从文件夹导入的多个文件

## 技术架构设计

### 1. 数据结构设计

#### GameSource (游戏来源信息)
```swift
/// 游戏来源类型
enum GameSourceType {
    case newGame                    // 新建游戏
    case singlePGNFile(URL)        // 单棋谱文件
    case multiPGNFile(URL, Int)    // 多棋谱文件 (文件路径, 游戏索引)
    case clipboard                  // 剪贴板导入
    case folderImport(URL)         // 文件夹导入
}

/// 游戏来源信息
struct GameSource {
    let type: GameSourceType
    let originalFilePath: URL?      // 原始文件路径
    let gameIndex: Int?             // 在原文件中的游戏索引 (0-based)
    let textRange: NSRange?         // 在原文件中的文本范围
    let lastModified: Date          // 最后修改时间
    let fileHash: String?           // 原文件内容哈希 (用于检测文件变化)
}

/// 文件索引信息 (用于增量更新)
struct PGNFileIndex {
    let filePath: URL
    let games: [PGNGameIndex]       // 文件中的游戏索引
    let fileHash: String            // 文件内容哈希
    let lastIndexed: Date           // 最后索引时间
}

/// 单个游戏在PGN文件中的索引信息
struct PGNGameIndex {
    let gameNumber: Int             // 游戏编号 (1-based)
    let textRange: NSRange          // 在文件中的文本范围
    let gameId: String              // 游戏唯一标识 (基于关键元数据生成)
    let lastModified: Date          // 游戏最后修改时间
}
```

#### SaveStrategy (保存策略)
```swift
/// 保存策略类型
enum SaveStrategy {
    case saveToOriginalFiles        // 保存回原文件 (增量更新)
    case saveAsNewMultiPGN(URL)    // 保存为新的多棋谱文件
    case saveAsSeparateFiles(URL)   // 分别保存到指定目录
    case replaceOriginalFile(URL)   // 完全替换原文件
}

/// 保存操作配置
struct SaveConfiguration {
    let strategy: SaveStrategy
    let includedSessions: [UUID]    // 要保存的会话ID列表
    let preserveOrder: Bool         // 是否保持原始顺序
    let backupOriginal: Bool        // 是否备份原文件
}
```

### 2. 核心类扩展

#### GameSession 扩展
```swift
extension GameSession {
    // 游戏来源信息
    @Published var gameSource: GameSource?
    
    // 修改状态追踪
    @Published var hasUnsavedChanges: Bool = false
    
    /// 检查游戏是否已修改
    func checkForUnsavedChanges() -> Bool {
        // 比较当前PGN与原始PGN
        guard let source = gameSource else { return true } // 新游戏默认有变化
        
        // 实现PGN差异检测逻辑
        return hasGameChanged()
    }
    
    /// 生成游戏唯一标识
    func generateGameId() -> String {
        // 基于关键元数据生成唯一标识
        let key = "\(game.tags.white)|\(game.tags.black)|\(game.tags.date)|\(game.tags.round)"
        return key.md5
    }
}
```

#### GameSessionManager 扩展
```swift
extension GameSessionManager {
    // 文件索引缓存
    private var fileIndexCache: [URL: PGNFileIndex] = [:]
    
    /// 增强的多游戏导入 (带来源追踪)
    func importPGNWithSourceTracking(_ pgn: String, from source: GameSourceType) {
        // 解析多游戏
        let games = PGNParser.parseMultiple(games: pgn)
        
        // 为每个游戏创建来源信息
        for (index, game) in games.enumerated() {
            let session = createNewSession()
            session.importGameWithoutUndo(game)
            
            // 设置来源信息
            session.gameSource = GameSource(
                type: source,
                originalFilePath: extractFilePath(from: source),
                gameIndex: games.count > 1 ? index : nil,
                textRange: calculateTextRange(for: game, in: pgn),
                lastModified: Date(),
                fileHash: calculateFileHash(for: source)
            )
            
            extractGameName(for: session, from: game, gameIndex: games.count > 1 ? index + 1 : nil)
        }
    }
    
    /// 批量保存所有游戏
    func saveAllGames(with configuration: SaveConfiguration) async throws {
        let targetSessions = sessions.filter { configuration.includedSessions.contains($0.id) }
        
        switch configuration.strategy {
        case .saveToOriginalFiles:
            try await saveToOriginalFiles(sessions: targetSessions)
        case .saveAsNewMultiPGN(let url):
            try await saveAsMultiPGN(sessions: targetSessions, to: url)
        case .saveAsSeparateFiles(let directory):
            try await saveAsSeparateFiles(sessions: targetSessions, to: directory)
        case .replaceOriginalFile(let url):
            try await replaceOriginalFile(sessions: targetSessions, at: url)
        }
    }
}
```

#### ChessFileManager 扩展
```swift
extension ChessFileManager {
    /// 增量保存到多棋谱文件
    func saveGameIncrementally(game: Game, to filePath: URL, at textRange: NSRange) throws {
        // 读取原文件内容
        let originalContent = try String(contentsOf: filePath, encoding: .utf8)
        let nsString = NSString(string: originalContent)
        
        // 替换指定范围的内容
        let newGamePGN = game.pgn
        let modifiedContent = nsString.replacingCharacters(in: textRange, with: newGamePGN)
        
        // 写入修改后的内容
        try modifiedContent.write(to: filePath, atomically: true, encoding: .utf8)
    }
    
    /// 保存多个游戏为单个PGN文件
    func saveMultipleGames(_ games: [Game], to url: URL) throws {
        let combinedPGN = games.map { $0.pgn }.joined(separator: "\n\n")
        try combinedPGN.write(to: url, atomically: true, encoding: .utf8)
    }
    
    /// 为多游戏PGN创建索引
    func createPGNIndex(for url: URL) throws -> PGNFileIndex {
        let content = try String(contentsOf: url, encoding: .utf8)
        let games = PGNParser.parseMultiple(games: content)
        
        var gameIndices: [PGNGameIndex] = []
        var currentPosition = 0
        
        // 使用正则表达式找到每个游戏的文本范围
        let gamePattern = try NSRegularExpression(pattern: "\\[Event\\s+\"[^\"]*\"\\]", options: [])
        let matches = gamePattern.matches(in: content, options: [], range: NSRange(location: 0, length: content.count))
        
        for (index, match) in matches.enumerated() {
            let nextMatchStart = index + 1 < matches.count ? matches[index + 1].range.location : content.count
            let gameRange = NSRange(location: match.range.location, length: nextMatchStart - match.range.location)
            
            let gameIndex = PGNGameIndex(
                gameNumber: index + 1,
                textRange: gameRange,
                gameId: games[index].generateGameId(),
                lastModified: Date()
            )
            gameIndices.append(gameIndex)
        }
        
        return PGNFileIndex(
            filePath: url,
            games: gameIndices,
            fileHash: content.md5,
            lastIndexed: Date()
        )
    }
}
```

### 3. 用户界面设计

#### 保存对话框 (SaveAllGamesDialog)
```swift
struct SaveAllGamesDialog: View {
    @Binding var isPresented: Bool
    let sessions: [GameSession]
    let onSave: (SaveConfiguration) -> Void
    
    @State private var saveStrategy: SaveStrategy = .saveAsNewMultiPGN(URL(fileURLWithPath: ""))
    @State private var selectedSessions: Set<UUID> = []
    @State private var showFileSelector = false
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Save Multiple Games")
                .font(.title2)
                .bold()
            
            // 游戏选择列表
            gameSelectionList
            
            // 保存策略选择
            saveStrategySelection
            
            // 操作按钮
            actionButtons
        }
        .frame(width: 600, height: 500)
    }
    
    private var gameSelectionList: some View {
        // 显示可选择的游戏会话列表
        // 按来源分组显示
        // 显示修改状态
    }
    
    private var saveStrategySelection: some View {
        // 根据游戏来源智能推荐保存策略
        // 提供策略选择选项
    }
}
```

#### 菜单和快捷键集成
```swift
// 在 AppDelegate 或 MenuCommands 中添加
CommandMenu("File") {
    // 现有命令...
    
    Divider()
    
    Button("Save All Games...") {
        NotificationCenter.default.post(name: .saveAllGames, object: nil)
    }
    .keyboardShortcut("s", modifiers: [.command, .option])
    
    Button("Save to Original Files") {
        NotificationCenter.default.post(name: .saveToOriginalFiles, object: nil)
    }
    .keyboardShortcut("s", modifiers: [.command, .shift, .option])
}
```

### 4. 智能保存策略算法

#### 策略决策逻辑
```swift
class SaveStrategyAnalyzer {
    func recommendStrategy(for sessions: [GameSession]) -> [SaveStrategy] {
        let sourceGroups = groupSessionsBySource(sessions)
        var recommendations: [SaveStrategy] = []
        
        // 分析不同场景
        if sourceGroups.count == 1 {
            let group = sourceGroups.first!
            switch group.sourceType {
            case .multiPGNFile(let url, _):
                // 所有游戏来自同一个多棋谱文件
                recommendations.append(.saveToOriginalFiles)
                recommendations.append(.replaceOriginalFile(url))
                
            case .singlePGNFile(let url):
                // 所有游戏来自单个文件
                recommendations.append(.saveToOriginalFiles)
                
            case .newGame:
                // 全是新游戏
                recommendations.append(.saveAsNewMultiPGN(generateDefaultURL()))
                recommendations.append(.saveAsSeparateFiles(generateDefaultDirectory()))
            }
        } else {
            // 混合来源
            recommendations.append(.saveAsNewMultiPGN(generateDefaultURL()))
            recommendations.append(.saveAsSeparateFiles(generateDefaultDirectory()))
        }
        
        return recommendations
    }
    
    private func groupSessionsBySource(_ sessions: [GameSession]) -> [SessionSourceGroup] {
        // 按来源对会话进行分组
    }
}
```

### 5. 增量更新算法

#### PGN 差异检测和更新
```swift
class IncrementalPGNUpdater {
    func updatePGNFile(at url: URL, with updatedGames: [(GameSession, PGNGameIndex)]) throws {
        // 读取原文件
        let originalContent = try String(contentsOf: url, encoding: .utf8)
        var mutableContent = originalContent
        
        // 按倒序更新 (避免范围偏移)
        let sortedUpdates = updatedGames.sorted { $0.1.textRange.location > $1.1.textRange.location }
        
        for (session, gameIndex) in sortedUpdates {
            let newPGN = session.game.pgn
            let nsString = NSMutableString(string: mutableContent)
            nsString.replaceCharacters(in: gameIndex.textRange, with: newPGN)
            mutableContent = nsString as String
        }
        
        // 创建备份
        try createBackup(for: url)
        
        // 写入更新内容
        try mutableContent.write(to: url, atomically: true, encoding: .utf8)
        
        // 更新文件索引
        try updateFileIndex(for: url)
    }
    
    private func createBackup(for url: URL) throws {
        let backupURL = url.appendingPathExtension("backup")
        try FileManager.default.copyItem(at: url, to: backupURL)
    }
}
```

## 实现阶段规划

### 阶段 1: 基础架构 (1-2天)
- [ ] 实现 GameSource 和相关数据结构
- [ ] 扩展 GameSession 支持来源追踪
- [ ] 修改现有导入逻辑添加来源信息

### 阶段 2: 文件索引系统 (2-3天)  
- [ ] 实现 PGNFileIndex 和索引创建
- [ ] 实现增量更新算法
- [ ] 添加文件哈希和变化检测

### 阶段 3: 保存策略引擎 (2-3天)
- [ ] 实现 SaveStrategyAnalyzer
- [ ] 实现各种保存策略的具体逻辑
- [ ] 添加错误处理和回滚机制

### 阶段 4: 用户界面 (2-3天)
- [ ] 实现 SaveAllGamesDialog
- [ ] 集成菜单和快捷键
- [ ] 添加进度指示和用户反馈

### 阶段 5: 测试和优化 (1-2天)
- [ ] 单元测试覆盖
- [ ] 集成测试各种保存场景
- [ ] 性能优化和内存管理

## 用户体验设计

### 快捷键方案
建议使用标准macOS模式：
- **Command+S**: 保存当前游戏到原位置
- **Command+Shift+S**: 当前游戏另存为
- **Command+Option+S**: 保存所有游戏 (推荐，符合"保存全部"惯例)

### 保存对话框流程
1. 用户按 Command+Option+S 或选择菜单
2. 显示游戏选择界面，按来源分组
3. 智能推荐保存策略
4. 用户确认后执行保存
5. 显示进度和结果反馈

### 智能提示系统
- 检测未保存的修改并高亮显示
- 当游戏来源复杂时提供策略建议
- 保存完成后显示文件位置
- 错误时提供具体的修复建议

## 技术挑战和解决方案

### 1. 文本范围计算精度
**挑战**: PGN解析时精确计算每个游戏的文本范围
**解决方案**: 使用正则表达式匹配游戏边界，结合ChessKit解析结果交叉验证

### 2. 并发保存操作
**挑战**: 多个文件同时保存时的竞态条件
**解决方案**: 使用串行队列执行文件操作，添加文件锁机制

### 3. 大文件性能
**挑战**: 包含数百个游戏的大型PGN文件处理性能
**解决方案**: 实现流式处理和延迟加载，添加进度指示

### 4. 文件变化检测
**挑战**: 外部修改原文件时的冲突检测
**解决方案**: 文件哈希比对，提供冲突解决选项

## 兼容性和向后兼容

### 现有功能保持
- 现有的单游戏保存功能完全保持不变
- Command+S 继续执行传统保存逻辑
- 现有的undo/redo机制不受影响

### 数据迁移
- GameSource 为可选字段，现有会话自动适配
- 文件索引为按需创建，不影响现有工作流
- 新功能作为现有功能的扩展，不破坏原有架构

这个技术规划提供了完整的多棋谱保存功能实现路径，考虑了复杂的使用场景和技术挑战，为后续开发提供详细的指导框架。