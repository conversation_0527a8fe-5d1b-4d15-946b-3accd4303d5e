refactor: Fix pawn promotion flow to create correct moves with promoted piece

- Detect promotion scenarios before move creation instead of post-processing
- Play move sound immediately when pawn reaches promotion rank  
- Store promotion squares (start/end) instead of incomplete Move object
- Create complete Move with promotedPiece only after user selection
- Add playGenericMoveSound() method to SoundManager for pre-move audio
- Update ChessGameViewModel to use promotionStartSquare/promotionEndSquare
- Fix promotingPieceColor computation to use board position lookup
- Prevent duplicate sound playback during promotion completion
- Add .promotionNeeded case to MoveExecutionResult enum
- Update all state management and cleanup logic for new promotion flow
- Fix test assertions to check new promotion state properties

This ensures moves are created with correct promoted piece information from the start, 
eliminating issues with incomplete temporary moves and improving the promotion UX.