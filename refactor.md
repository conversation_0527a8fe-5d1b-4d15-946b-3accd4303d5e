# Refactoring Plan: `editableBoardView` to `Canvas`

## 1. Objective

To resolve the significant UI rendering delay when `PositionEditorView` appears. The root cause has been identified as excessive view complexity, leading to a slow layout phase. This plan outlines the refactoring of the chessboard from a complex hierarchy of `VStack`/`HStack` views to a single, high-performance `Canvas` view.

## 2. Problem Analysis Summary

The current implementation of `editableBoardView` uses nested `VStack` and `HStack` views to create an 8x8 grid. This results in a view hierarchy of several hundred individual views for the board alone (64 squares, each with multiple sub-layers, plus gesture recognizers).

The Time Profiler trace confirms that the application spends a majority of its time in AppKit's layout engine (`-[NSView layoutSubtreeIfNeeded]`) trying to compute the frames for this massive number of views, which blocks the main thread and delays the presentation of the sheet.

## 3. Proposed Solution: `Canvas`

We will replace the entire grid of views with a single `Canvas`. The `Canvas` view is a GPU-accelerated drawing surface that is extremely efficient for this use case.

- **Drawing**: The board, squares, and pieces will be drawn manually within the `Canvas`'s `draw` closure. This reduces the view count for the board from ~200 to 1.
- **Interaction**: A single set of gesture recognizers will be attached to the `Canvas`. The location of a tap will be used to calculate which square was clicked, instead of each square having its own gesture recognizer.

This change will make the layout phase almost instantaneous, resolving the delay.

## 4. Detailed Implementation Steps

### Step 1: Replace `editableBoardView` Body with a `Canvas`

In `PositionEditorView.swift`, the `editableBoardView` computed property will be completely replaced.

**Current Code (Simplified):**
```swift
private var editableBoardView: some View {
    ZStack {
        VStack(spacing: 0) {
            ForEach(transformer.rankLabels, id: \.self) { rank in
                HStack(spacing: 0) {
                    ForEach(transformer.fileLabels, id: \.self) { file in
                        let square = Square("\(file)\(rank)")
                        editableSquareView(for: square, squareSize: squareSize)
                    }
                }
            }
        }
        coordinateLabelsView(squareSize: squareSize)
    }
    // ...
}
```

**New Code:**
```swift
private var editableBoardView: some View {
    let boardSize = self.boardSize // Ensure boardSize is accessible
    ZStack {
        Canvas { context, size in
            // Drawing logic will go here (Step 2)
            drawChessboard(in: context, size: size)
            drawPieces(in: context, size: size)
        }
        .gesture(
            // Interaction logic will go here (Step 3)
            TapGesture()
                .onEnded { _ in
                    // This is a placeholder, full logic in Step 3
                    print("Canvas tapped")
                }
        )
        
        coordinateLabelsView(squareSize: boardSize / 8)
    }
}
```

### Step 2: Implement Drawing Logic

We will create new helper methods within `PositionEditorView` to handle the drawing.

**2.1 Draw the Checkerboard:**
This function will iterate through all 64 squares and draw a colored rectangle for each.

```swift
private func drawChessboard(in context: GraphicsContext, size: CGSize) {
    let squareSize = size.width / 8.0
    
    for rank in 0..<8 {
        for file in 0..<8 {
            let rect = CGRect(x: CGFloat(file) * squareSize, y: CGFloat(rank) * squareSize, width: squareSize, height: squareSize)
            let isLight = (rank + file) % 2 == 0
            let color = isLight ? Color.brown.opacity(0.3) : Color.brown.opacity(0.7)
            
            context.fill(Path(rect), with: .color(color))
        }
    }
}
```

**2.2 Draw the Pieces:**
This function will get the list of pieces from the `viewModel` and draw their corresponding images onto the canvas. It must be called *after* `drawChessboard`.

```swift
private func drawPieces(in context: GraphicsContext, size: CGSize) {
    let squareSize = size.width / 8.0
    let transformer = BoardCoordinateTransformer(isFlipped: viewModel.isBoardFlipped)

    for piece in viewModel.position.allPieces {
        let viewCoordinates = transformer.getViewCoordinates(for: piece.square)
        
        let rect = CGRect(
            x: CGFloat(viewCoordinates.file) * squareSize,
            y: CGFloat(viewCoordinates.rank) * squareSize,
            width: squareSize,
            height: squareSize
        ).insetBy(dx: 4, dy: 4) // Add padding like before

        // The pieceImageName(for:) helper method can be reused
        if let image = NSImage(named: pieceImageName(for: piece)) {
            context.draw(Image(nsImage: image), in: rect)
        }
    }
}
```
*(Note: The `BoardCoordinateTransformer` will be useful for handling the flipped board state correctly when calculating piece positions.)*

### Step 3: Implement Interaction Logic

We will attach gesture recognizers to the `Canvas` and create a helper function to translate a tap location (`CGPoint`) into a chess `Square`.

**3.1 Add Gestures:**
We will use `.onTapGesture` for left-clicks and the existing `RightClickGestureView` for right-clicks, but apply them to the `Canvas` instead of each square.

```swift
// Inside editableBoardView, applied to the Canvas
.onTapGesture { location in
    handleTap(at: location, size: boardSize)
}
.overlay(
    RightClickGestureView {
        // To get the location, the RightClickGestureView needs to be updated
        // to provide the tap location in its callback.
    }
)
```
*(For simplicity, we can start with just the left-click tap gesture. The right-click gesture will require a minor modification to `RightClickGestureView` to pass the location of the click.)*

**3.2 Create a Point-to-Square Conversion Helper:**
This function is the core of the new interaction model.

```swift
private func square(at point: CGPoint, size: CGSize) -> Square? {
    let squareSize = size.width / 8.0
    
    // Check if the point is within the board bounds
    guard CGRect(origin: .zero, size: size).contains(point) else {
        return nil
    }

    let fileIndex = Int(point.x / squareSize)
    let rankIndex = Int(point.y / squareSize)

    let transformer = BoardCoordinateTransformer(isFlipped: viewModel.isBoardFlipped)
    return transformer.getSquare(fromViewCoordinates: (rank: rankIndex, file: fileIndex))
}
```

**3.3 Implement the Tap Handler:**

```swift
private func handleTap(at location: CGPoint, size: CGSize) {
    if let tappedSquare = square(at: location, size: size) {
        viewModel.handleSquareTap(tappedSquare)
    }
}

// A similar function will be needed for right-clicks.
```

### Step 4: Clean Up

The old `editableSquareView(for:squareSize:)` function can be completely deleted, as its role is now fulfilled by the `Canvas` drawing and interaction logic.

## 5. Benefits of This Refactoring

- **Performance:** Drastically improves performance by reducing the view count from hundreds to one, eliminating the layout bottleneck. The UI will appear instantly.
- **Simplicity:** The logic for drawing and interaction becomes more centralized and easier to reason about, rather than being distributed across 64 separate views.
- **Maintainability:** Future visual changes to the board (e.g., drawing arrows, highlighting squares) can be done easily within the `Canvas` context.
